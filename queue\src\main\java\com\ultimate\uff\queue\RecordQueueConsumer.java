package com.ultimate.uff.queue;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.tenant.RecordContext;
import com.ultimate.uff.validation.AfterAuthorizationService;
import com.ultimate.uff.validation.GlobalResponse;
import com.ultimate.uff.validation.RecordInsertionService;
import com.ultimate.uff.validation.TenantAwareDatabaseService;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Map;

@Service
public class RecordQueueConsumer {

    private static final Logger logger = LoggerFactory.getLogger(RecordQueueConsumer.class);

    @Autowired private RecordInsertionService recordInsertionService;
    @Autowired private AfterAuthorizationService afterAuthorizationService;
    @Autowired private TenantAwareDatabaseService databaseService;
    @Autowired private KafkaTemplate<String, String> kafkaTemplate;
    @Autowired private ObjectMapper objectMapper;

    @KafkaListener(topics = "record-topic", groupId = "uff-group")
    public void handleInsertRecord(ConsumerRecord<String, String> record) {
        try {
            Map<String, Object> data = objectMapper.readValue(record.value(), new TypeReference<>() {});
            String tableName = requireHeader(record, "table-name");
            String userId = requireHeader(record, "user-id");
            String currCom = requireHeader(record, "current-company");
            String primCom = requireHeader(record, "primary-company");
            int authorizeNumber = Integer.parseInt(requireHeader(record, "authorize-number"));
            boolean isTenantBased = Boolean.parseBoolean(requireHeader(record, "is-tenant-based"));

            RecordContext.set(currCom, primCom, isTenantBased);

            GlobalResponse response = recordInsertionService.insertRecord(tableName, data, userId, authorizeNumber);
            sendReply(record, Map.of("success", response.isSuccess(), "message", response.getMessage()));

        } catch (Exception e) {
            logger.error("Insert error: {}", e.getMessage(), e);
            sendReply(record, Map.of("success", false, "message", e.getMessage()));
        } finally {
            RecordContext.clear();
        }
    }

    @KafkaListener(topics = "record-authorize-topic", groupId = "uff-group")
    public void handleAuthorizeRecord(ConsumerRecord<String, String> record) {
        try {
            Map<String, Object> data = objectMapper.readValue(record.value(), new TypeReference<>() {});
            String tableName = requireHeader(record, "table-name");
            String userId = requireHeader(record, "user-id");
            String currCom = requireHeader(record, "current-company");
            String primCom = requireHeader(record, "primary-company");
            int authorizeNumber = Integer.parseInt(requireHeader(record, "authorize-number"));
            boolean isTenantBased = Boolean.parseBoolean(requireHeader(record, "is-tenant-based"));
            String id = (String) data.get("id");

            if (id == null || id.isEmpty()) throw new IllegalArgumentException("Missing ID");

            RecordContext.set(currCom, primCom, isTenantBased);

            GlobalResponse result = recordInsertionService.authorizeRecord(tableName, id, userId, authorizeNumber);

            if (!result.isSuccess()) {
                sendReply(record, Map.of("success", false, "message", result.getMessage()));
                return;
            }

            Map<String, Object> liveRecord = databaseService.getRecordById(primCom,tableName, id);
            if (liveRecord != null) {
            	if (liveRecord != null) {
            	    Map<String, Object> postAuthPayload = Map.of(
            	        "tableName", tableName,
            	        "id", id,
            	        "record", liveRecord,
            	        "context", Map.of(
            	            "currentCompany", RecordContext.getCurrentCompany(),
            	            "primaryCompany", RecordContext.getPrimaryCompany(),
            	            "isTenantBased", RecordContext.isTenantBased()
            	        )
            	    );

            	    String json = objectMapper.writeValueAsString(postAuthPayload);
            	    kafkaTemplate.send("record-postauth-topic", json);
            	}

            }

            sendReply(record, Map.of("success", true, "message", "Record authorized successfully"));

        } catch (Exception e) {
            logger.error("Authorize error: {}", e.getMessage(), e);
            sendReply(record, Map.of("success", false, "message", e.getMessage()));
        } finally {
            RecordContext.clear();
        }
    }

    @KafkaListener(topics = "record-delete-topic", groupId = "uff-group")
    public void handleDeleteRecord(ConsumerRecord<String, String> record) {
        try {
            Map<String, Object> data = objectMapper.readValue(record.value(), new TypeReference<>() {});
            String tableName = requireHeader(record, "table-name");
            String userId = requireHeader(record, "user-id");
            String currCom = requireHeader(record, "current-company");
            String primCom = requireHeader(record, "primary-company");
            int authorizeNumber = Integer.parseInt(requireHeader(record, "authorize-number"));
            boolean isTenantBased = Boolean.parseBoolean(requireHeader(record, "is-tenant-based"));
            String id = (String) data.get("id");

            if (id == null || id.isEmpty()) throw new IllegalArgumentException("Missing ID");

            RecordContext.set(currCom, primCom, isTenantBased);

            GlobalResponse result = recordInsertionService.deleteRecord(tableName, id, userId, authorizeNumber);
            sendReply(record, Map.of("success", result.isSuccess(), "message", result.getMessage()));

        } catch (Exception e) {
            logger.error("Delete error: {}", e.getMessage(), e);
            sendReply(record, Map.of("success", false, "message", e.getMessage()));
        } finally {
            RecordContext.clear();
        }
    }

    @KafkaListener(topics = "record-reject-topic", groupId = "uff-group")
    public void handleRejectRecord(ConsumerRecord<String, String> record) {
        try {
            Map<String, Object> data = objectMapper.readValue(record.value(), new TypeReference<>() {});
            String tableName = requireHeader(record, "table-name");
            String userId = requireHeader(record, "user-id");
            String currCom = requireHeader(record, "current-company");
            String primCom = requireHeader(record, "primary-company");
            int authorizeNumber = Integer.parseInt(requireHeader(record, "authorize-number"));
            boolean isTenantBased = Boolean.parseBoolean(requireHeader(record, "is-tenant-based"));
            String id = (String) data.get("id");

            if (id == null || id.isEmpty()) throw new IllegalArgumentException("Missing ID");

            RecordContext.set(currCom, primCom, isTenantBased);

            GlobalResponse result = recordInsertionService.rejectRecord(tableName, id, userId, authorizeNumber);
            sendReply(record, Map.of("success", result.isSuccess(), "message", result.getMessage()));

        } catch (Exception e) {
            logger.error("Reject error: {}", e.getMessage(), e);
            sendReply(record, Map.of("success", false, "message", e.getMessage()));
        } finally {
            RecordContext.clear();
        }
    }

    private String requireHeader(ConsumerRecord<?, ?> record, String key) {
        Header header = record.headers().lastHeader(key);
        if (header == null) throw new IllegalArgumentException("Missing required header: " + key);
        return new String(header.value(), StandardCharsets.UTF_8);
    }

    private void sendReply(ConsumerRecord<String, String> requestRecord, Map<String, Object> reply) {
        try {
            Header replyTopic = requestRecord.headers().lastHeader("reply-topic");
            Header correlationId = requestRecord.headers().lastHeader("kafka_correlationId");

            if (replyTopic != null && correlationId != null) {
                String topic = new String(replyTopic.value(), StandardCharsets.UTF_8);
                String replyJson = objectMapper.writeValueAsString(reply);
                ProducerRecord<String, String> response = new ProducerRecord<>(topic, replyJson);
                response.headers().add("kafka_correlationId", correlationId.value());
                kafkaTemplate.send(response);
            } else {
                logger.error("Missing reply-topic or kafka_correlationId headers.");
            }
        } catch (Exception e) {
            logger.error("Failed to send Kafka reply: {}", e.getMessage(), e);
        }
    }
}
