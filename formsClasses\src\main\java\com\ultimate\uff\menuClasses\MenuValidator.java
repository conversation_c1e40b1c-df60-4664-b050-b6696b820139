package com.ultimate.uff.menuClasses;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.CustomValidator;

import java.util.List;
import java.util.Map;

@Component
public class MenuValidator implements CustomValidator {
 
    @Autowired
    private DatabaseService databaseService;

     HttpSession session;

    private final List<String> validTypes = List.of("qur", "scr", "table", "menu");

    @Override
    public String validate(String id, Map<String, Object> recordDetail) {
        try {


            // Deserialize recordDetail to a Menu object
            Menu menu = new Menu(recordDetail);

            // Perform your validation logic
            if (menu.getMenusList() == null || menu.getMenusList().isEmpty()) {
                return "Menu list is missing or empty";
            }

            // Iterate over menuList and validate each MenuItem
            List<Menus> menuItems = menu.getMenusList();
            for (Menus menuItem : menuItems) {
                String type = menuItem.getType();
                if (!validTypes.contains(type)) {
                    return "Invalid type '" + type + "'. Must be one of " + String.join(", ", validTypes);
                }

                // Access the current looped menuItem's application
                String application = menuItem.getApplication();
                if (application == null || application.trim().isEmpty()) {
                    return "Application must exist within the type '" + type + "'";
                }

                if (!isValidApplication(type, application)) {
                    return "Invalid application '" + application + "' for type '" + type + "'";
                }
            }


            return "Validation successful";
        } catch (Exception e) {
            e.printStackTrace();
            return "Error during validation: " + e.getMessage();
        }
    }

    private boolean isValidApplication(String type, String application) {
        switch (type) {
            case "scr":
                return recordExistsInTable("screen_builder", application);
            case "qur":
                return recordExistsInTable("query_builder", application);
            case "table":
                return tableExists(application);
            case "menu":
                return recordExistsInTable("menu", application);
            default:
                return false;
        }
    }

    private boolean recordExistsInTable(String tableName, String application) {
        return databaseService.checkRecordExistence(tableName, application);
    }

    private boolean tableExists(String tableName) {
        return databaseService.checkTableExistence(tableName);
    }
}
