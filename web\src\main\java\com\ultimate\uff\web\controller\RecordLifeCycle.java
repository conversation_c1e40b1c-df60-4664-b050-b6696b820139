package com.ultimate.uff.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.ultimate.uff.queue.RecordProcessingService;
import com.ultimate.uff.tenant.RecordContext;

import java.util.Map;

@RestController
@RequestMapping("/api/tables")
public class RecordLifeCycle {

    @Autowired
    private RecordProcessingService recordProcessingService;

    @PostMapping("/{tableName}/records")
    public ResponseEntity<Map<String, Object>> processRecord(
            @PathVariable String tableName,
            @RequestParam(defaultValue = "0") int authorizeNumber,
            @RequestParam(defaultValue = "false") boolean isTenantBased,
            @RequestBody Map<String, Object> request,
            @RequestHeader(value = "Current-Tenant", defaultValue = "0001") String currentTenant,
            @RequestHeader(value = "Primary-Tenant", defaultValue = "0001") String primaryTenant) {

        RecordContext.set(currentTenant, primaryTenant, isTenantBased);
        return recordProcessingService.processRecord(tableName, request, authorizeNumber);
    }

    @PutMapping("/{tableName}/records/{id}/authorize")
    public ResponseEntity<Map<String, Object>> authorizeRecord(
            @PathVariable String tableName,
            @PathVariable String id,
            @RequestParam(defaultValue = "0") int authorizeNumber,
            @RequestParam(defaultValue = "false") boolean isTenantBased,
            @RequestHeader(value = "Current-Tenant", defaultValue = "0001") String currentTenant,
            @RequestHeader(value = "Primary-Tenant", defaultValue = "0001") String primaryTenant) {

        RecordContext.set(currentTenant, primaryTenant, isTenantBased);
        return recordProcessingService.authorizeRecord(tableName, id, authorizeNumber);
    }

    @DeleteMapping("/{tableName}/records/{id}")
    public ResponseEntity<Map<String, Object>> deleteRecord(
            @PathVariable String tableName,
            @PathVariable String id,
            @RequestParam(defaultValue = "0") int authorizeNumber,
            @RequestParam(defaultValue = "false") boolean isTenantBased,
            @RequestHeader(value = "Current-Tenant", defaultValue = "0001") String currentTenant,
            @RequestHeader(value = "Primary-Tenant", defaultValue = "0001") String primaryTenant) {

        RecordContext.set(currentTenant, primaryTenant, isTenantBased);
        return recordProcessingService.deleteRecord(tableName, id, authorizeNumber);
    }

    @PutMapping("/{tableName}/records/{id}/reject")
    public ResponseEntity<Map<String, Object>> rejectRecord(
            @PathVariable String tableName,
            @PathVariable String id,
            @RequestParam(defaultValue = "0") int authorizeNumber,
            @RequestParam(defaultValue = "false") boolean isTenantBased,
            @RequestHeader(value = "Current-Tenant", defaultValue = "0001") String currentTenant,
            @RequestHeader(value = "Primary-Tenant", defaultValue = "0001") String primaryTenant) {

        RecordContext.set(currentTenant, primaryTenant, isTenantBased);
        return recordProcessingService.rejectRecord(tableName, id, authorizeNumber);
    }
    
    @PostMapping("/{tableName}/validate")
    public ResponseEntity<Map<String, Object>> validateRecord(
            @PathVariable String tableName,
            @RequestBody Map<String, Object> request,
    @RequestParam(defaultValue = "false") boolean isTenantBased,
    @RequestHeader(value = "Current-Tenant", defaultValue = "0001") String currentTenant,
    @RequestHeader(value = "Primary-Tenant", defaultValue = "0001") String primaryTenant){
        RecordContext.set(currentTenant, primaryTenant, isTenantBased);

        return recordProcessingService.validateRecord(tableName, request);
    }

    @GetMapping("/{tableName}/records/{id}")
    public ResponseEntity<Map<String, Object>> getRecord(
            @PathVariable String tableName,
            @PathVariable String id,
            @RequestParam(defaultValue = "false") boolean isTenantBased,
            @RequestHeader(value = "Current-Tenant", defaultValue = "0001") String currentTenant,
            @RequestHeader(value = "Primary-Tenant", defaultValue = "0001") String primaryTenant) {
        RecordContext.set(currentTenant, primaryTenant, isTenantBased);

        return recordProcessingService.getRecord(tableName, id);
    }
    
    @GetMapping("/{tableName}/metadata")
    public ResponseEntity<Map<String, Object>> getMetadata(
            @PathVariable String tableName){

        return recordProcessingService.getTableMetadata(tableName);
    }

    @PostMapping("/transaction")
    public ResponseEntity<Map<String, Object>> handleTransaction(
            @RequestParam String form,
            @RequestParam String function,
            @RequestParam(required = true) String transactionId,
            @RequestParam(defaultValue = "0") int authorizeNumber,
            @RequestHeader(value = "Current-Tenant", defaultValue = "0001") String currentTenant,
            @RequestHeader(value = "Primary-Tenant", defaultValue = "0001") String primaryTenant,
            @RequestBody(required = false) Map<String, Object> body) {


        switch (function.toLowerCase()) {
            case "i":
                return recordProcessingService.processRecord(form, body, authorizeNumber);
            case "a":
                return recordProcessingService.authorizeRecord(form, transactionId, authorizeNumber);
            case "r":
                return recordProcessingService.rejectRecord(form, transactionId, authorizeNumber);
            case "d":
                return recordProcessingService.deleteRecord(form, transactionId, authorizeNumber);
            case "v":
                return recordProcessingService.validateRecord(form, body);
            case "g":
                return recordProcessingService.getRecord(form, transactionId);
            case "m":
                return recordProcessingService.getTableMetadata(form);
            default:
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of(
                        "status", "error",
                        "message", "Unknown function type: " + function
                ));
        }
    }

    @GetMapping("/generate-id/{tableName}")
    public ResponseEntity<Map<String, Object>> generateId(@PathVariable String tableName) {
        return recordProcessingService.generateId(tableName);
    }
}
