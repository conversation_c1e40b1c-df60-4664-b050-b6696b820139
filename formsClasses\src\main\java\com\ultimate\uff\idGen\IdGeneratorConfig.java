package com.ultimate.uff.idGen;


import java.util.HashMap;
import java.util.Map;

public class IdGeneratorConfig {

    // Fields from metadata
    private String idMode;
    private String prefix;
    private String date;
    private String dateFormat;
    private String lastSeq;
    private String randomLength;
    private String allowedChars;
    private String lastGenId;
    private String nextId;

    // Metadata fields
    private String recordInputter;
    private String recordAuthorizer;
    private int recordCount;
    private String dateTime;

    // Extra/dynamic fields
    private Map<String, Object> extraFields = new HashMap<>();

    // Constructor from Map
    public IdGeneratorConfig(Map<String, Object> configDetail) {
        this.idMode = (String) configDetail.get("idMode");
        this.prefix = (String) configDetail.get("prefix");
        this.date = (String) configDetail.get("date");
        this.dateFormat = (String) configDetail.get("dateFormat");
        this.lastSeq = (String) configDetail.get("lastSeq");
        this.randomLength = (String) configDetail.get("randomLength");
        this.allowedChars = (String) configDetail.get("allowedChars");
        this.lastGenId = (String) configDetail.get("lastGenId");
        this.nextId = (String) configDetail.get("nextId");

        this.recordInputter = (String) configDetail.get("recordInputter");
        this.recordAuthorizer = (String) configDetail.get("recordAuthorizer");
        this.recordCount = configDetail.get("recordCount") != null ? (Integer) configDetail.get("recordCount") : 0;
        this.dateTime = (String) configDetail.get("dateTime");

        for (Map.Entry<String, Object> entry : configDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    // Helper to check known fields
    private boolean isFieldHandled(String key) {
        return key.equals("idMode") || key.equals("prefix") || key.equals("date") ||
               key.equals("dateFormat") || key.equals("lastSeq") || key.equals("randomLength") ||
               key.equals("allowedChars") || key.equals("lastGenId") || key.equals("nextId") ||
               key.equals("recordInputter") || key.equals("recordAuthorizer") ||
               key.equals("recordCount") || key.equals("dateTime");
    }

    // Getters and setters
    public String getIdMode() {
        return idMode;
    }

    public void setIdMode(String idMode) {
        this.idMode = idMode;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getDateFormat() {
        return dateFormat;
    }

    public void setDateFormat(String dateFormat) {
        this.dateFormat = dateFormat;
    }

    public String getLastSeq() {
        return lastSeq;
    }

    public void setLastSeq(String lastSeq) {
        this.lastSeq = lastSeq;
    }

    public String getRandomLength() {
        return randomLength;
    }

    public void setRandomLength(String randomLength) {
        this.randomLength = randomLength;
    }

    public String getAllowedChars() {
        return allowedChars;
    }

    public void setAllowedChars(String allowedChars) {
        this.allowedChars = allowedChars;
    }

    public String getLastGenId() {
        return lastGenId;
    }

    public void setLastGenId(String lastGenId) {
        this.lastGenId = lastGenId;
    }

    public String getNextId() {
        return nextId;
    }

    public void setNextId(String nextId) {
        this.nextId = nextId;
    }

    public String getRecordInputter() {
        return recordInputter;
    }

    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
    }

    public String getRecordAuthorizer() {
        return recordAuthorizer;
    }

    public void setRecordAuthorizer(String recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
    }

    public int getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public Map<String, Object> getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(Map<String, Object> extraFields) {
        this.extraFields = extraFields;
    }
}
