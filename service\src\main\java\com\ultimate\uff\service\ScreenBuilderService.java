package com.ultimate.uff.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import com.ultimate.uff.uql.service.DatabaseService;

@Service
public class ScreenBuilderService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private DatabaseService databaseService;
    
    public Map<String, Object> getscreenMetadata(String tableName) {
    	Map<String, Object> getMetadataQuery = databaseService.getRecordById("screen_builder", tableName);
        try {
            return getMetadataQuery;
        } catch (Exception e) {
            return null;
        }
    }


}
