package com.ultimate.uff.validation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ultimate.uff.uql.service.DatabaseService;

import java.util.List;
import java.util.Map;

@Service
public class TenantTableCreationService {

    @Autowired
    private ReadOnlyDatabaseService readOnlyDatabaseService;
  

    public void createTablesForTenants() {
        // Step 1: Fetch tenant record IDs where isPrimary = true and status = 'NEW'
        List<String> tenantIds = readOnlyDatabaseService.getRecordIdsByCriteria("tenant", Map.of(
        	    "isPrimary", Map.of("EQ", true),
        	    "status", Map.of("EQ", "NEW")
        	));

        // Step 2: Fetch form definition IDs
        List<String> formDefinitionIds = readOnlyDatabaseService.getRecordIdsByCriteria("formDefinition", Map.of());

        // Step 3: Iterate over form definitions
        for (String formDefinitionId : formDefinitionIds) {

            Map<String, Object> formDef = readOnlyDatabaseService.getRecordById("formDefinition", formDefinitionId);

            if (formDef == null) continue;

            // Properly handle the Boolean and String conversion
            boolean isTenantBased = false;
            Object isTenantBasedObj = formDef.get("isTenantBased");

            if (isTenantBasedObj instanceof Boolean) {
                isTenantBased = (Boolean) isTenantBasedObj;
            } else if (isTenantBasedObj instanceof String) {
                isTenantBased = Boolean.parseBoolean((String) isTenantBasedObj);
            }

            if (isTenantBased) {
                // Iterate over tenant IDs
                for (String tenantId : tenantIds) {
                    Map<String, Object> tenant = readOnlyDatabaseService.getRecordById("tenant", tenantId);
                    if (tenant == null) continue;

                    String tenantCode = (String) tenant.get("ID");
                    if (tenantCode == null) continue;

                    String tableName = tenantCode +"_"+ formDefinitionId;

                    try {
                        if (!readOnlyDatabaseService.checkTableExistence(tableName)) {
                        	readOnlyDatabaseService.createTable(tableName);
                        	readOnlyDatabaseService.createTable(tableName+"$UNAU");
                        	readOnlyDatabaseService.createTable(tableName+"$HIST");
                        }
                    } catch (Exception e) {
                        System.err.println("Error creating table: " + tableName + " - " + e.getMessage());
                    }
                }
            } else {
                try {
                    if (!readOnlyDatabaseService.checkTableExistence(formDefinitionId)) {
                    	readOnlyDatabaseService.createTable(formDefinitionId);
                    	readOnlyDatabaseService.createTable(formDefinitionId+"$UNAU");
                    	readOnlyDatabaseService.createTable(formDefinitionId+"$HIST");
                    }
                } catch (Exception e) {
                    System.err.println("Error creating table: " + formDefinitionId + " - " + e.getMessage());
                }
            }
        }
    }
}
