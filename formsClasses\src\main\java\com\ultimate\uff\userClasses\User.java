package com.ultimate.uff.userClasses;

import java.util.Map;

public class User {

    private String id;
    private String tableName;
    private String user;
    private String profile;
    private String endTime;
    private String startTime;
    private String signOnName;
    private String passwordAttempts;
    private String passwordValidity;
    private String inputter;
    private String authorizer;
    private String inDate;
    private String inTime;
    private int recordCount;

    // Constructor that initializes User fields from the recordDetail map
    public User(Map<String, Object> recordDetail) {
        this.id = (String) recordDetail.get("ID");
        this.tableName = (String) recordDetail.get("table_name");
        this.user = (String) recordDetail.get("user");
        this.profile = (String) recordDetail.get("profile");
        this.endTime = (String) recordDetail.get("end_time");
        this.startTime = (String) recordDetail.get("start_time");
        this.signOnName = (String) recordDetail.get("Sign_on_name");
        this.passwordAttempts = (String) recordDetail.get("password.attempts");
        this.passwordValidity = (String) recordDetail.get("password.validaty");
        this.inputter = (String) recordDetail.get("Inputter");
        this.authorizer = (String) recordDetail.get("Authorizer");
        this.inDate = (String) recordDetail.get("IN_DATE");
        this.inTime = (String) recordDetail.get("IN_TIME");

        // Handle record_count safely
        Object recordCountValue = recordDetail.get("record_count");
        if (recordCountValue instanceof Integer) {
            this.recordCount = (Integer) recordCountValue;
        } else if (recordCountValue instanceof String) {
            try {
                this.recordCount = Integer.parseInt((String) recordCountValue);
            } catch (NumberFormatException e) {
                this.recordCount = 0; // Default value
            }
        } else {
            this.recordCount = 0; // Default value
        }
    }

    // Method to update the recordDetail map and return the updated map
    public Map<String, Object> updateRecordDetail(Map<String, Object> recordDetail) {
        if (this.id != null) {
            recordDetail.put("ID", this.id);
        }
        if (this.tableName != null) {
            recordDetail.put("table_name", this.tableName);
        }
        if (this.user != null) {
            recordDetail.put("user", this.user);
        }
        if (this.profile != null) {
            recordDetail.put("profile", this.profile);
        }
        if (this.endTime != null) {
            recordDetail.put("end_time", this.endTime);
        }
        if (this.startTime != null) {
            recordDetail.put("start_time", this.startTime);
        }
        if (this.signOnName != null) {
            recordDetail.put("Sign_on_name", this.signOnName);
        }
        if (this.passwordAttempts != null) {
            recordDetail.put("password.attempts", this.passwordAttempts);
        }
        if (this.passwordValidity != null) {
            recordDetail.put("password.validaty", this.passwordValidity);
        }
        if (this.inputter != null) {
            recordDetail.put("Inputter", this.inputter);
        }
        if (this.authorizer != null) {
            recordDetail.put("Authorizer", this.authorizer);
        }
        if (this.inDate != null) {
            recordDetail.put("IN_DATE", this.inDate);
        }
        if (this.inTime != null) {
            recordDetail.put("IN_TIME", this.inTime);
        }
        recordDetail.put("record_count", this.recordCount);

        return recordDetail;
    }

    // Getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getSignOnName() {
        return signOnName;
    }

    public void setSignOnName(String signOnName) {
        this.signOnName = signOnName;
    }

    public String getPasswordAttempts() {
        return passwordAttempts;
    }

    public void setPasswordAttempts(String passwordAttempts) {
        this.passwordAttempts = passwordAttempts;
    }

    public String getPasswordValidity() {
        return passwordValidity;
    }

    public void setPasswordValidity(String passwordValidity) {
        this.passwordValidity = passwordValidity;
    }

    public String getInputter() {
        return inputter;
    }

    public void setInputter(String inputter) {
        this.inputter = inputter;
    }

    public String getAuthorizer() {
        return authorizer;
    }

    public void setAuthorizer(String authorizer) {
        this.authorizer = authorizer;
    }

    public String getInDate() {
        return inDate;
    }

    public void setInDate(String inDate) {
        this.inDate = inDate;
    }

    public String getInTime() {
        return inTime;
    }

    public void setInTime(String inTime) {
        this.inTime = inTime;
    }

    public int getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
    }
}
