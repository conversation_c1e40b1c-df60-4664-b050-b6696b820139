#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1383536 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=32044, tid=25964
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-6b288e027b65d5e236c1aace1e7e362b-sock

Host: Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz, 8 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.1)
Time: Tue May 27 14:28:06 2025 Egypt Daylight Time elapsed time: 27.877248 seconds (0d 0h 0m 27s)

---------------  T H R E A D  ---------------

Current thread (0x00000149c0ca1580):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=25964, stack(0x0000002988c00000,0x0000002988d00000) (1024K)]


Current CompileTask:
C2:27877 13532   !   4       org.codehaus.plexus.interpolation.StringSearchInterpolator::interpolate (668 bytes)

Stack: [0x0000002988c00000,0x0000002988d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b68f2]
V  [jvm.dll+0x1e0029]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x43340]
C  [KERNEL32.DLL+0x31fd7]
C  [ntdll.dll+0x6d7d0]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000014a0c0c7c20, length=48, elements={
0x00000149ac8cd6e0, 0x00000149c0c812e0, 0x00000149c0c82e10, 0x00000149c0c8a400,
0x00000149c0c8b530, 0x00000149c0c8fdf0, 0x00000149c0c9b480, 0x00000149c0ca1580,
0x00000149c0ca1e00, 0x0000014a037052a0, 0x0000014a039710a0, 0x0000014a087afe10,
0x0000014a0863ca50, 0x0000014a08382910, 0x0000014a089199a0, 0x0000014a09195f00,
0x0000014a08fd5f90, 0x0000014a091f4ea0, 0x0000014a091f5530, 0x0000014a091f5bc0,
0x0000014a091f68e0, 0x0000014a091f6250, 0x0000014a091f6f70, 0x0000014a091f3af0,
0x0000014a091f4810, 0x0000014a091f4180, 0x0000014a086cc200, 0x0000014a086c9410,
0x0000014a086cc890, 0x0000014a086c9aa0, 0x0000014a086cbb70, 0x0000014a086cae50,
0x0000014a086cb4e0, 0x0000014a086ccf20, 0x0000014a086cd5b0, 0x0000014a086ca130,
0x0000014a086cdc40, 0x0000014a086ca7c0, 0x0000014a086d03a0, 0x0000014a086ce2d0,
0x0000014a086ceff0, 0x0000014a086cf680, 0x0000014a08059bc0, 0x0000014a0ba47d80,
0x0000014a0ba4b890, 0x0000014a0ba4a4e0, 0x0000014a0ba48410, 0x0000014a0ba4bf20
}

Java Threads: ( => current thread )
  0x00000149ac8cd6e0 JavaThread "main"                              [_thread_blocked, id=2500, stack(0x0000002988200000,0x0000002988300000) (1024K)]
  0x00000149c0c812e0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=21592, stack(0x0000002988600000,0x0000002988700000) (1024K)]
  0x00000149c0c82e10 JavaThread "Finalizer"                  daemon [_thread_blocked, id=29168, stack(0x0000002988700000,0x0000002988800000) (1024K)]
  0x00000149c0c8a400 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=22372, stack(0x0000002988800000,0x0000002988900000) (1024K)]
  0x00000149c0c8b530 JavaThread "Attach Listener"            daemon [_thread_blocked, id=13836, stack(0x0000002988900000,0x0000002988a00000) (1024K)]
  0x00000149c0c8fdf0 JavaThread "Service Thread"             daemon [_thread_blocked, id=13316, stack(0x0000002988a00000,0x0000002988b00000) (1024K)]
  0x00000149c0c9b480 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=14272, stack(0x0000002988b00000,0x0000002988c00000) (1024K)]
=>0x00000149c0ca1580 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=25964, stack(0x0000002988c00000,0x0000002988d00000) (1024K)]
  0x00000149c0ca1e00 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=34164, stack(0x0000002988d00000,0x0000002988e00000) (1024K)]
  0x0000014a037052a0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=20088, stack(0x0000002988e00000,0x0000002988f00000) (1024K)]
  0x0000014a039710a0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=13640, stack(0x0000002989000000,0x0000002989100000) (1024K)]
  0x0000014a087afe10 JavaThread "Active Thread: Equinox Container: 91c6530e-4e31-48ca-8fbb-f94fa5d2100b"        [_thread_blocked, id=16332, stack(0x0000002989800000,0x0000002989900000) (1024K)]
  0x0000014a0863ca50 JavaThread "Refresh Thread: Equinox Container: 91c6530e-4e31-48ca-8fbb-f94fa5d2100b" daemon [_thread_blocked, id=32576, stack(0x0000002989900000,0x0000002989a00000) (1024K)]
  0x0000014a08382910 JavaThread "Framework Event Dispatcher: Equinox Container: 91c6530e-4e31-48ca-8fbb-f94fa5d2100b" daemon [_thread_blocked, id=33336, stack(0x0000002989b00000,0x0000002989c00000) (1024K)]
  0x0000014a089199a0 JavaThread "Start Level: Equinox Container: 91c6530e-4e31-48ca-8fbb-f94fa5d2100b" daemon [_thread_blocked, id=31988, stack(0x0000002989c00000,0x0000002989d00000) (1024K)]
  0x0000014a09195f00 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=33544, stack(0x0000002989d00000,0x0000002989e00000) (1024K)]
  0x0000014a08fd5f90 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=18384, stack(0x0000002989100000,0x0000002989200000) (1024K)]
  0x0000014a091f4ea0 JavaThread "Worker-JM"                         [_thread_blocked, id=33144, stack(0x0000002989f00000,0x000000298a000000) (1024K)]
  0x0000014a091f5530 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=30388, stack(0x000000298a000000,0x000000298a100000) (1024K)]
  0x0000014a091f5bc0 JavaThread "Worker-0"                          [_thread_blocked, id=29256, stack(0x000000298a100000,0x000000298a200000) (1024K)]
  0x0000014a091f68e0 JavaThread "Worker-1: Initialize workspace"        [_thread_blocked, id=26160, stack(0x000000298a200000,0x000000298a300000) (1024K)]
  0x0000014a091f6250 JavaThread "Java indexing"              daemon [_thread_blocked, id=20812, stack(0x000000298a400000,0x000000298a500000) (1024K)]
  0x0000014a091f6f70 JavaThread "Worker-2"                          [_thread_blocked, id=18624, stack(0x000000298a800000,0x000000298a900000) (1024K)]
  0x0000014a091f3af0 JavaThread "Worker-3: Initialize Workspace"        [_thread_blocked, id=22652, stack(0x000000298a900000,0x000000298aa00000) (1024K)]
  0x0000014a091f4810 JavaThread "Worker-4: Updating workspace"        [_thread_blocked, id=31124, stack(0x000000298aa00000,0x000000298ab00000) (1024K)]
  0x0000014a091f4180 JavaThread "Thread-2"                   daemon [_thread_in_native, id=33468, stack(0x000000298ab00000,0x000000298ac00000) (1024K)]
  0x0000014a086cc200 JavaThread "Thread-3"                   daemon [_thread_in_native, id=23136, stack(0x000000298ac00000,0x000000298ad00000) (1024K)]
  0x0000014a086c9410 JavaThread "Thread-4"                   daemon [_thread_in_native, id=2920, stack(0x000000298ad00000,0x000000298ae00000) (1024K)]
  0x0000014a086cc890 JavaThread "Thread-5"                   daemon [_thread_in_native, id=29448, stack(0x000000298ae00000,0x000000298af00000) (1024K)]
  0x0000014a086c9aa0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=20532, stack(0x000000298af00000,0x000000298b000000) (1024K)]
  0x0000014a086cbb70 JavaThread "Thread-7"                   daemon [_thread_in_native, id=15572, stack(0x000000298b000000,0x000000298b100000) (1024K)]
  0x0000014a086cae50 JavaThread "Thread-8"                   daemon [_thread_in_native, id=17056, stack(0x000000298b100000,0x000000298b200000) (1024K)]
  0x0000014a086cb4e0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=25132, stack(0x000000298b200000,0x000000298b300000) (1024K)]
  0x0000014a086ccf20 JavaThread "Thread-10"                  daemon [_thread_blocked, id=32456, stack(0x000000298b300000,0x000000298b400000) (1024K)]
  0x0000014a086cd5b0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=17308, stack(0x000000298b400000,0x000000298b500000) (1024K)]
  0x0000014a086ca130 JavaThread "Worker-5: Java indexing... "        [_thread_blocked, id=23120, stack(0x000000298b500000,0x000000298b600000) (1024K)]
  0x0000014a086cdc40 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=15748, stack(0x000000298b600000,0x000000298b700000) (1024K)]
  0x0000014a086ca7c0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=6652, stack(0x000000298b700000,0x000000298b800000) (1024K)]
  0x0000014a086d03a0 JavaThread "Worker-6: Building"                [_thread_blocked, id=16712, stack(0x000000298b800000,0x000000298b900000) (1024K)]
  0x0000014a086ce2d0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=14768, stack(0x0000002988f00000,0x0000002989000000) (1024K)]
  0x0000014a086ceff0 JavaThread "Compiler Processing Task"   daemon [_thread_blocked, id=11760, stack(0x000000298b900000,0x000000298ba00000) (1024K)]
  0x0000014a086cf680 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=13720, stack(0x000000298ba00000,0x000000298bb00000) (1024K)]
  0x0000014a08059bc0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=22180, stack(0x0000002989e00000,0x0000002989f00000) (1024K)]
  0x0000014a0ba47d80 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=26548, stack(0x000000298a300000,0x000000298a400000) (1024K)]
  0x0000014a0ba4b890 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=11132, stack(0x000000298bb00000,0x000000298bc00000) (1024K)]
  0x0000014a0ba4a4e0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=29340, stack(0x000000298bc00000,0x000000298bd00000) (1024K)]
  0x0000014a0ba48410 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=33388, stack(0x000000298bd00000,0x000000298be00000) (1024K)]
  0x0000014a0ba4bf20 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=22524, stack(0x000000298be00000,0x000000298bf00000) (1024K)]
Total: 48

Other Threads:
  0x00000149ac98ea60 VMThread "VM Thread"                           [id=27240, stack(0x0000002988500000,0x0000002988600000) (1024K)]
  0x00000149c0b9ca00 WatcherThread "VM Periodic Task Thread"        [id=1972, stack(0x0000002988400000,0x0000002988500000) (1024K)]
  0x00000149ac8ecd20 WorkerThread "GC Thread#0"                     [id=23772, stack(0x0000002988300000,0x0000002988400000) (1024K)]
  0x0000014a084d28e0 WorkerThread "GC Thread#1"                     [id=31428, stack(0x0000002989200000,0x0000002989300000) (1024K)]
  0x0000014a081b9e70 WorkerThread "GC Thread#2"                     [id=15376, stack(0x0000002989300000,0x0000002989400000) (1024K)]
  0x0000014a08463420 WorkerThread "GC Thread#3"                     [id=19268, stack(0x0000002989400000,0x0000002989500000) (1024K)]
  0x0000014a084637c0 WorkerThread "GC Thread#4"                     [id=13800, stack(0x0000002989500000,0x0000002989600000) (1024K)]
  0x0000014a084f1670 WorkerThread "GC Thread#5"                     [id=31004, stack(0x0000002989600000,0x0000002989700000) (1024K)]
  0x0000014a084742e0 WorkerThread "GC Thread#6"                     [id=27828, stack(0x0000002989700000,0x0000002989800000) (1024K)]
  0x0000014a08474dc0 WorkerThread "GC Thread#7"                     [id=31916, stack(0x0000002989a00000,0x0000002989b00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  27918 13532   !   4       org.codehaus.plexus.interpolation.StringSearchInterpolator::interpolate (668 bytes)
C2 CompilerThread1  27918 13548       4       org.apache.maven.model.inheritance.DefaultInheritanceAssembler$InheritanceModelMerger::mergePluginContainer_Plugins (433 bytes)
Total: 2

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd98c5e308] Threads_lock - owner thread: 0x00000149ac98ea60
[0x00007ffd98c5e408] Heap_lock - owner thread: 0x0000014a086d03a0

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000149c1000000-0x00000149c1ba0000-0x00000149c1ba0000), size 12189696, SharedBaseAddress: 0x00000149c1000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000149c2000000-0x0000014a02000000, reserved size: 1073741824
Narrow klass base: 0x00000149c1000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 8066M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 3584K, used 512K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eae00000)
  from space 512K, 100% used [0x00000000eaf80000,0x00000000eb000000,0x00000000eb000000)
  to   space 1024K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf00000)
 ParOldGen       total 361472K, used 361090K [0x00000000c0000000, 0x00000000d6100000, 0x00000000eab00000)
  object space 361472K, 99% used [0x00000000c0000000,0x00000000d60a0a50,0x00000000d6100000)
 Metaspace       used 62680K, committed 64192K, reserved 1114112K
  class space    used 6423K, committed 7104K, reserved 1048576K

Card table byte_map: [0x00000149ac270000,0x00000149ac480000] _byte_map_base: 0x00000149abc70000

Marking Bits: (ParMarkBitMap*) 0x00007ffd98c631f0
 Begin Bits: [0x00000149beac0000, 0x00000149bfac0000)
 End Bits:   [0x00000149bfac0000, 0x00000149c0ac0000)

Polling page: 0x00000149aa8a0000

Metaspace:

Usage:
  Non-class:     54.94 MB used.
      Class:      6.27 MB used.
       Both:     61.21 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      55.75 MB ( 87%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       6.94 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      62.69 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  7.48 MB
       Class:  9.04 MB
        Both:  16.52 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 100.62 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1216.
num_arena_deaths: 16.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1004.
num_space_uncommitted: 1.
num_chunks_returned_to_freelist: 41.
num_chunks_taken_from_freelist: 4194.
num_chunk_merges: 17.
num_chunk_splits: 2591.
num_chunks_enlarged: 1446.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=8790Kb max_used=9157Kb free=111209Kb
 bounds [0x00000149b7590000, 0x00000149b7e90000, 0x00000149beac0000]
CodeHeap 'profiled nmethods': size=120000Kb used=24688Kb max_used=26729Kb free=95311Kb
 bounds [0x00000149afac0000, 0x00000149b1520000, 0x00000149b6ff0000]
CodeHeap 'non-nmethods': size=5760Kb used=1425Kb max_used=1534Kb free=4334Kb
 bounds [0x00000149b6ff0000, 0x00000149b7260000, 0x00000149b7590000]
 total_blobs=12130 nmethods=11390 adapters=646
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 27.326 Thread 0x00000149c0ca1e00 nmethod 13641 0x00000149b065fb90 code [0x00000149b065fd40, 0x00000149b065ff48]
Event: 27.328 Thread 0x0000014a08059bc0 nmethod 13543 0x00000149b7866810 code [0x00000149b7866c80, 0x00000149b7869c28]
Event: 27.329 Thread 0x0000014a08059bc0 13548       4       org.apache.maven.model.inheritance.DefaultInheritanceAssembler$InheritanceModelMerger::mergePluginContainer_Plugins (433 bytes)
Event: 27.355 Thread 0x00000149c0ca1e00 13642       3       org.apache.maven.model.normalization.DefaultModelNormalizer::mergeDuplicates (264 bytes)
Event: 27.359 Thread 0x00000149c0ca1e00 nmethod 13642 0x00000149b05c8490 code [0x00000149b05c8960, 0x00000149b05cb280]
Event: 27.393 Thread 0x00000149c0ca1e00 13643       2       org.apache.maven.model.Contributor::setLocation (356 bytes)
Event: 27.394 Thread 0x00000149c0ca1e00 nmethod 13643 0x00000149b05f7110 code [0x00000149b05f73a0, 0x00000149b05f7da0]
Event: 27.400 Thread 0x00000149c0ca1e00 13644       3       org.apache.maven.model.io.xpp3.MavenXpp3ReaderEx::parseIssueManagement (290 bytes)
Event: 27.403 Thread 0x00000149c0ca1e00 nmethod 13644 0x00000149afb76b10 code [0x00000149afb76f20, 0x00000149afb78998]
Event: 27.447 Thread 0x00000149c0ca1e00 13647       3       org.apache.maven.model.merge.MavenModelMerger::mergeReportPlugin_ReportSets (235 bytes)
Event: 27.451 Thread 0x00000149c0ca1e00 nmethod 13647 0x00000149b03f7d10 code [0x00000149b03f8140, 0x00000149b03fa3d8]
Event: 27.490 Thread 0x00000149c0ca1e00 13649       3       org.eclipse.core.internal.jobs.JobChangeEvent::<init> (37 bytes)
Event: 27.491 Thread 0x00000149c0ca1e00 nmethod 13649 0x00000149b06e2610 code [0x00000149b06e27c0, 0x00000149b06e2990]
Event: 27.492 Thread 0x00000149c0ca1e00 13648       1       org.eclipse.core.runtime.jobs.Job::shouldRun (2 bytes)
Event: 27.492 Thread 0x00000149c0ca1e00 nmethod 13648 0x00000149b79b1110 code [0x00000149b79b12a0, 0x00000149b79b1368]
Event: 27.492 Thread 0x00000149c0ca1e00 13650       1       org.eclipse.core.internal.jobs.InternalJob::setResult (6 bytes)
Event: 27.492 Thread 0x00000149c0ca1e00 nmethod 13650 0x00000149b79b0e10 code [0x00000149b79b0fa0, 0x00000149b79b1088]
Event: 27.494 Thread 0x00000149c0ca1e00 13651   !   3       java.io.FilterOutputStream::close (133 bytes)
Event: 27.495 Thread 0x00000149c0ca1e00 nmethod 13651 0x00000149afdcfb90 code [0x00000149afdcfdc0, 0x00000149afdd0588]
Event: 27.495 Thread 0x00000149c0ca1e00 13652       3       org.apache.maven.model.inheritance.DefaultInheritanceAssembler$InheritanceModelMerger::mergeReporting_Plugins (253 bytes)

GC Heap History (20 events):
Event: 27.230 GC heap before
{Heap before GC invocations=644 (full 4):
 PSYoungGen      total 2048K, used 2048K [0x00000000eab00000, 0x00000000eb180000, 0x0000000100000000)
  eden space 1024K, 100% used [0x00000000eab00000,0x00000000eac00000,0x00000000eac00000)
  from space 1024K, 100% used [0x00000000eae00000,0x00000000eaf00000,0x00000000eaf00000)
  to   space 2048K, 0% used [0x00000000eac00000,0x00000000eac00000,0x00000000eae00000)
 ParOldGen       total 356864K, used 353650K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d595ca00,0x00000000d5c80000)
 Metaspace       used 62642K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.233 GC heap after
{Heap after GC invocations=644 (full 4):
 PSYoungGen      total 3072K, used 640K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 1024K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eac00000)
  from space 2048K, 31% used [0x00000000eac00000,0x00000000eaca0000,0x00000000eae00000)
  to   space 1536K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf80000)
 ParOldGen       total 356864K, used 354586K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5a46a20,0x00000000d5c80000)
 Metaspace       used 62642K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.243 GC heap before
{Heap before GC invocations=645 (full 4):
 PSYoungGen      total 3072K, used 1664K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 1024K, 100% used [0x00000000eab00000,0x00000000eac00000,0x00000000eac00000)
  from space 2048K, 31% used [0x00000000eac00000,0x00000000eaca0000,0x00000000eae00000)
  to   space 1536K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf80000)
 ParOldGen       total 356864K, used 354586K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5a46a20,0x00000000d5c80000)
 Metaspace       used 62643K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.245 GC heap after
{Heap after GC invocations=645 (full 4):
 PSYoungGen      total 2048K, used 480K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 1536K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eac80000)
  from space 512K, 93% used [0x00000000eae00000,0x00000000eae78000,0x00000000eae80000)
  to   space 1024K, 0% used [0x00000000eac80000,0x00000000eac80000,0x00000000ead80000)
 ParOldGen       total 356864K, used 355026K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5ab4a40,0x00000000d5c80000)
 Metaspace       used 62643K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.259 GC heap before
{Heap before GC invocations=646 (full 4):
 PSYoungGen      total 2048K, used 2016K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 1536K, 100% used [0x00000000eab00000,0x00000000eac80000,0x00000000eac80000)
  from space 512K, 93% used [0x00000000eae00000,0x00000000eae78000,0x00000000eae80000)
  to   space 1024K, 0% used [0x00000000eac80000,0x00000000eac80000,0x00000000ead80000)
 ParOldGen       total 356864K, used 355026K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5ab4a40,0x00000000d5c80000)
 Metaspace       used 62643K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.261 GC heap after
{Heap after GC invocations=646 (full 4):
 PSYoungGen      total 2560K, used 640K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 1536K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eac80000)
  from space 1024K, 62% used [0x00000000eac80000,0x00000000ead20000,0x00000000ead80000)
  to   space 1024K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf00000)
 ParOldGen       total 356864K, used 355350K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5b05a50,0x00000000d5c80000)
 Metaspace       used 62643K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.273 GC heap before
{Heap before GC invocations=647 (full 4):
 PSYoungGen      total 2560K, used 2176K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 1536K, 100% used [0x00000000eab00000,0x00000000eac80000,0x00000000eac80000)
  from space 1024K, 62% used [0x00000000eac80000,0x00000000ead20000,0x00000000ead80000)
  to   space 1024K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf00000)
 ParOldGen       total 356864K, used 355350K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5b05a50,0x00000000d5c80000)
 Metaspace       used 62643K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.276 GC heap after
{Heap after GC invocations=647 (full 4):
 PSYoungGen      total 3072K, used 640K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 62% used [0x00000000eae00000,0x00000000eaea0000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 356864K, used 355886K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5b8ba70,0x00000000d5c80000)
 Metaspace       used 62643K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.297 GC heap before
{Heap before GC invocations=648 (full 4):
 PSYoungGen      total 3072K, used 2688K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 62% used [0x00000000eae00000,0x00000000eaea0000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 356864K, used 355886K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5b8ba70,0x00000000d5c80000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.300 GC heap after
{Heap after GC invocations=648 (full 4):
 PSYoungGen      total 3072K, used 768K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 75% used [0x00000000ead00000,0x00000000eadc0000,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf80000)
 ParOldGen       total 356864K, used 356370K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5c04a80,0x00000000d5c80000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.315 GC heap before
{Heap before GC invocations=649 (full 4):
 PSYoungGen      total 3072K, used 2816K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 75% used [0x00000000ead00000,0x00000000eadc0000,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf80000)
 ParOldGen       total 356864K, used 356370K [0x00000000c0000000, 0x00000000d5c80000, 0x00000000eab00000)
  object space 356864K, 99% used [0x00000000c0000000,0x00000000d5c04a80,0x00000000d5c80000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.321 GC heap after
{Heap after GC invocations=649 (full 4):
 PSYoungGen      total 2560K, used 1024K [0x00000000eab00000, 0x00000000eb180000, 0x0000000100000000)
  eden space 1536K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eac80000)
  from space 1024K, 100% used [0x00000000eae80000,0x00000000eaf80000,0x00000000eaf80000)
  to   space 2048K, 0% used [0x00000000eac80000,0x00000000eac80000,0x00000000eae80000)
 ParOldGen       total 357376K, used 357130K [0x00000000c0000000, 0x00000000d5d00000, 0x00000000eab00000)
  object space 357376K, 99% used [0x00000000c0000000,0x00000000d5cc2a80,0x00000000d5d00000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.339 GC heap before
{Heap before GC invocations=650 (full 4):
 PSYoungGen      total 2560K, used 2560K [0x00000000eab00000, 0x00000000eb180000, 0x0000000100000000)
  eden space 1536K, 100% used [0x00000000eab00000,0x00000000eac80000,0x00000000eac80000)
  from space 1024K, 100% used [0x00000000eae80000,0x00000000eaf80000,0x00000000eaf80000)
  to   space 2048K, 0% used [0x00000000eac80000,0x00000000eac80000,0x00000000eae80000)
 ParOldGen       total 357376K, used 357130K [0x00000000c0000000, 0x00000000d5d00000, 0x00000000eab00000)
  object space 357376K, 99% used [0x00000000c0000000,0x00000000d5cc2a80,0x00000000d5d00000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.345 GC heap after
{Heap after GC invocations=650 (full 4):
 PSYoungGen      total 3584K, used 736K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 1536K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eac80000)
  from space 2048K, 35% used [0x00000000eac80000,0x00000000ead38000,0x00000000eae80000)
  to   space 1536K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eb000000)
 ParOldGen       total 358400K, used 358074K [0x00000000c0000000, 0x00000000d5e00000, 0x00000000eab00000)
  object space 358400K, 99% used [0x00000000c0000000,0x00000000d5daeaa0,0x00000000d5e00000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.363 GC heap before
{Heap before GC invocations=651 (full 4):
 PSYoungGen      total 3584K, used 2272K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 1536K, 100% used [0x00000000eab00000,0x00000000eac80000,0x00000000eac80000)
  from space 2048K, 35% used [0x00000000eac80000,0x00000000ead38000,0x00000000eae80000)
  to   space 1536K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eb000000)
 ParOldGen       total 358400K, used 358074K [0x00000000c0000000, 0x00000000d5e00000, 0x00000000eab00000)
  object space 358400K, 99% used [0x00000000c0000000,0x00000000d5daeaa0,0x00000000d5e00000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.367 GC heap after
{Heap after GC invocations=651 (full 4):
 PSYoungGen      total 3072K, used 528K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 51% used [0x00000000eae80000,0x00000000eaf04010,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 358912K, used 358638K [0x00000000c0000000, 0x00000000d5e80000, 0x00000000eab00000)
  object space 358912K, 99% used [0x00000000c0000000,0x00000000d5e3bab0,0x00000000d5e80000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.410 GC heap before
{Heap before GC invocations=652 (full 4):
 PSYoungGen      total 3072K, used 2576K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 51% used [0x00000000eae80000,0x00000000eaf04010,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 358912K, used 358638K [0x00000000c0000000, 0x00000000d5e80000, 0x00000000eab00000)
  object space 358912K, 99% used [0x00000000c0000000,0x00000000d5e3bab0,0x00000000d5e80000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.414 GC heap after
{Heap after GC invocations=652 (full 4):
 PSYoungGen      total 3584K, used 320K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 1024K, 31% used [0x00000000ead80000,0x00000000eadd0000,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 359424K, used 359070K [0x00000000c0000000, 0x00000000d5f00000, 0x00000000eab00000)
  object space 359424K, 99% used [0x00000000c0000000,0x00000000d5ea7ab0,0x00000000d5f00000)
 Metaspace       used 62644K, committed 64128K, reserved 1114112K
  class space    used 6418K, committed 7104K, reserved 1048576K
}
Event: 27.453 GC heap before
{Heap before GC invocations=653 (full 4):
 PSYoungGen      total 3584K, used 2880K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 1024K, 31% used [0x00000000ead80000,0x00000000eadd0000,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 359424K, used 359070K [0x00000000c0000000, 0x00000000d5f00000, 0x00000000eab00000)
  object space 359424K, 99% used [0x00000000c0000000,0x00000000d5ea7ab0,0x00000000d5f00000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6421K, committed 7104K, reserved 1048576K
}
Event: 27.456 GC heap after
{Heap after GC invocations=653 (full 4):
 PSYoungGen      total 3584K, used 480K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eae00000)
  from space 512K, 93% used [0x00000000eae80000,0x00000000eaef8000,0x00000000eaf00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 359424K, used 359302K [0x00000000c0000000, 0x00000000d5f00000, 0x00000000eab00000)
  object space 359424K, 99% used [0x00000000c0000000,0x00000000d5ee1ab0,0x00000000d5f00000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6421K, committed 7104K, reserved 1048576K
}

Dll operation events (12 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.146 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.166 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.170 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.171 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.174 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.190 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.255 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.303 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.022 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna10440786470473843670.dll
Event: 6.015 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 6.018 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 27.456 Thread 0x0000014a091f6250 Uncommon trap: trap_request=0xfffffff4 fr.pc=0x00000149b7e5dcd4 relative=0x0000000000001e34
Event: 27.456 Thread 0x0000014a091f6250 Uncommon trap: reason=null_check action=make_not_entrant pc=0x00000149b7e5dcd4 method=org.eclipse.jdt.internal.core.index.DiskIndex.copyQueryResults(Lorg/eclipse/jdt/internal/compiler/util/HashtableOfObject;I)V @ 51 c2
Event: 27.456 Thread 0x0000014a091f6250 DEOPT PACKING pc=0x00000149b7e5dcd4 sp=0x000000298a4fedb0
Event: 27.456 Thread 0x0000014a091f6250 DEOPT UNPACKING pc=0x00000149b7043aa2 sp=0x000000298a4fed80 mode 2
Event: 27.489 Thread 0x0000014a091f6250 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000149b79c881c relative=0x00000000000005fc
Event: 27.489 Thread 0x0000014a091f6250 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000149b79c881c method=org.eclipse.jdt.internal.compiler.util.SimpleLookupTable.put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; @ 61 c2
Event: 27.489 Thread 0x0000014a091f6250 DEOPT PACKING pc=0x00000149b79c881c sp=0x000000298a4feef0
Event: 27.489 Thread 0x0000014a091f6250 DEOPT UNPACKING pc=0x00000149b7043aa2 sp=0x000000298a4feea8 mode 2
Event: 27.500 Thread 0x0000014a091f6250 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000149b7817ea0 relative=0x0000000000000160
Event: 27.500 Thread 0x0000014a091f6250 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000149b7817ea0 method=org.eclipse.jdt.internal.compiler.util.Util.isClassFileName(Ljava/lang/String;)Z @ 20 c2
Event: 27.500 Thread 0x0000014a091f6250 DEOPT PACKING pc=0x00000149b7817ea0 sp=0x000000298a4ff040
Event: 27.500 Thread 0x0000014a091f6250 DEOPT UNPACKING pc=0x00000149b7043aa2 sp=0x000000298a4fefc0 mode 2
Event: 27.500 Thread 0x0000014a091f6250 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000149b7dd7794 relative=0x0000000000000bb4
Event: 27.500 Thread 0x0000014a091f6250 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000149b7dd7794 method=org.eclipse.jdt.internal.core.search.indexing.BinaryContainer.isValidPackageNameForClassOrisModule(Ljava/lang/String;)Z @ 41 c2
Event: 27.500 Thread 0x0000014a091f6250 DEOPT PACKING pc=0x00000149b7dd7794 sp=0x000000298a4fefe0
Event: 27.500 Thread 0x0000014a091f6250 DEOPT UNPACKING pc=0x00000149b7043aa2 sp=0x000000298a4fefd0 mode 2
Event: 27.500 Thread 0x0000014a091f6250 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000149b7e0f73c relative=0x0000000000001cdc
Event: 27.500 Thread 0x0000014a091f6250 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000149b7e0f73c method=org.eclipse.jdt.internal.compiler.parser.Scanner.internalScanIdentifierOrKeyword(II[C)Lorg/eclipse/jdt/internal/compiler/parser/TerminalToken; @ 302 c2
Event: 27.500 Thread 0x0000014a091f6250 DEOPT PACKING pc=0x00000149b7e0f73c sp=0x000000298a4fef40
Event: 27.500 Thread 0x0000014a091f6250 DEOPT UNPACKING pc=0x00000149b7043aa2 sp=0x000000298a4feed8 mode 2

Classes loaded (20 events):
Event: 26.114 Loading class com/sun/org/apache/xerces/internal/xni/XNIException
Event: 26.114 Loading class com/sun/org/apache/xerces/internal/xni/XNIException done
Event: 26.120 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLParseException
Event: 26.120 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLParseException done
Event: 26.315 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable
Event: 26.315 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable done
Event: 26.330 Loading class java/util/TreeMap$AscendingSubMap
Event: 26.332 Loading class java/util/TreeMap$NavigableSubMap
Event: 26.333 Loading class java/util/TreeMap$NavigableSubMap done
Event: 26.333 Loading class java/util/TreeMap$AscendingSubMap done
Event: 26.334 Loading class java/util/TreeMap$AscendingSubMap$AscendingEntrySetView
Event: 26.334 Loading class java/util/TreeMap$NavigableSubMap$EntrySetView
Event: 26.334 Loading class java/util/TreeMap$NavigableSubMap$EntrySetView done
Event: 26.334 Loading class java/util/TreeMap$AscendingSubMap$AscendingEntrySetView done
Event: 26.335 Loading class java/util/TreeMap$NavigableSubMap$SubMapEntryIterator
Event: 26.335 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator
Event: 26.335 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator done
Event: 26.335 Loading class java/util/TreeMap$NavigableSubMap$SubMapEntryIterator done
Event: 27.434 Loading class sun/nio/fs/WindowsFileCopy
Event: 27.434 Loading class sun/nio/fs/WindowsFileCopy done

Classes unloaded (20 events):
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c26975d8 'org/apache/maven/shared/filtering/MavenReaderFilterRequest'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c26972d0 'org/apache/maven/shared/filtering/MavenFileFilterRequest'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c26970e0 'org/codehaus/plexus/interpolation/Interpolator'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2696ef0 'org/codehaus/plexus/interpolation/BasicInterpolator'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2696d00 'org/codehaus/plexus/interpolation/ValueSource'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2696948 'org/apache/maven/shared/filtering/MavenResourcesExecution'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2696680 'org/apache/maven/shared/filtering/AbstractMavenFilteringRequest'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2696430 'org/apache/maven/shared/filtering/MavenFilteringException'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c26961f0 'org/apache/maven/shared/filtering/DefaultMavenResourcesFiltering'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2696000 'org/apache/maven/shared/filtering/MavenResourcesFiltering'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2695c00 'org/apache/maven/shared/filtering/DefaultMavenReaderFilter'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c26954d0 'org/apache/maven/shared/filtering/MavenReaderFilter'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2695240 'org/apache/maven/shared/filtering/DefaultMavenFileFilter'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2695000 'org/apache/maven/shared/filtering/BaseFilter'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2694d68 'org/apache/maven/shared/filtering/MavenFileFilter'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2694b78 'org/apache/maven/shared/filtering/DefaultFilterInfo'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2694878 'org/apache/maven/plugins/resources/TestResourcesMojo'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2694600 'org/apache/maven/plugins/maven_resources_plugin/HelpMojo'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2694300 'org/apache/maven/plugins/resources/CopyResourcesMojo'
Event: 24.720 Thread 0x00000149ac98ea60 Unloading class 0x00000149c2694000 'org/apache/maven/plugins/resources/ResourcesMojo'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 26.216 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac1f770}> (0x00000000eac1f770) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.216 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac20270}> (0x00000000eac20270) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.220 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac41958}> (0x00000000eac41958) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.220 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac421a8}> (0x00000000eac421a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.224 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac52ef8}> (0x00000000eac52ef8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.225 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac539f8}> (0x00000000eac539f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.283 Thread 0x0000014a086d03a0 Implicit null exception at 0x00000149b79e3d03 to 0x00000149b79e402c
Event: 26.314 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab36b20}> (0x00000000eab36b20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.319 Thread 0x0000014a0ba47d80 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab4cfd8}> (0x00000000eab4cfd8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.319 Thread 0x0000014a0ba47d80 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab55e08}> (0x00000000eab55e08) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.324 Thread 0x0000014a0ba47d80 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab57090}> (0x00000000eab57090) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.325 Thread 0x0000014a0ba4a4e0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab5e138}> (0x00000000eab5e138) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.471 Thread 0x0000014a086d03a0 Implicit null exception at 0x00000149b77800e3 to 0x00000149b7780730
Event: 26.762 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaba2e80}> (0x00000000eaba2e80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.763 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaba3960}> (0x00000000eaba3960) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.763 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaba4818}> (0x00000000eaba4818) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.763 Thread 0x0000014a086d03a0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaba5068}> (0x00000000eaba5068) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.821 Thread 0x0000014a086d03a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eabe23d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eabe23d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 27.435 Thread 0x0000014a091f6250 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac5dba0}> (0x00000000eac5dba0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 27.488 Thread 0x0000014a091f6250 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eacdd290}> (0x00000000eacdd290) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 27.262 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.272 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.276 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.296 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.301 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.315 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.321 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.339 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.345 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.363 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.367 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.410 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.414 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.447 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 27.447 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 27.452 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 27.452 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 27.453 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.456 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.500 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0d75490
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0d82610
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0e3d510
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0e8ac90
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0eeb490
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0efde90
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f0a910
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f11310
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f12410
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f17590
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f17a90
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f22710
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f28190
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f3db90
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f3ee10
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f43010
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f6a110
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b0f6a590
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b10d6d10
Event: 24.771 Thread 0x00000149ac98ea60 flushing  nmethod 0x00000149b10daf10

Events (20 events):
Event: 21.405 Thread 0x0000014a0ba4b200 Thread exited: 0x0000014a0ba4b200
Event: 22.862 Thread 0x0000014a09a798c0 Thread exited: 0x0000014a09a798c0
Event: 24.468 Thread 0x0000014a09269280 Thread exited: 0x0000014a09269280
Event: 25.061 Thread 0x0000014a0ba497c0 Thread exited: 0x0000014a0ba497c0
Event: 25.163 Thread 0x00000149c0ca1e00 Thread added: 0x0000014a08059bc0
Event: 26.316 Thread 0x0000014a086d03a0 Thread added: 0x0000014a0ba47d80
Event: 26.319 Thread 0x0000014a0ba47d80 Thread added: 0x0000014a0ba4b890
Event: 26.320 Thread 0x0000014a0ba47d80 Thread added: 0x0000014a0ba4a4e0
Event: 26.363 Thread 0x0000014a086d03a0 Thread added: 0x0000014a0ba48410
Event: 26.752 Thread 0x0000014a086d03a0 Thread added: 0x0000014a0ba4bf20
Event: 26.915 Thread 0x0000014a086d03a0 Thread added: 0x0000014a0ba48aa0
Event: 26.916 Thread 0x0000014a086d03a0 Thread added: 0x0000014a0ba4c5b0
Event: 26.916 Thread 0x0000014a086d03a0 Thread added: 0x0000014a0ba4cc40
Event: 26.917 Thread 0x0000014a086d03a0 Thread added: 0x0000014a0ba497c0
Event: 26.918 Thread 0x0000014a086d03a0 Thread added: 0x0000014a0ba4d2d0
Event: 27.000 Thread 0x0000014a0ba4cc40 Thread exited: 0x0000014a0ba4cc40
Event: 27.000 Thread 0x0000014a0ba48aa0 Thread exited: 0x0000014a0ba48aa0
Event: 27.000 Thread 0x0000014a0ba497c0 Thread exited: 0x0000014a0ba497c0
Event: 27.000 Thread 0x0000014a0ba4c5b0 Thread exited: 0x0000014a0ba4c5b0
Event: 27.001 Thread 0x0000014a0ba4d2d0 Thread exited: 0x0000014a0ba4d2d0


Dynamic libraries:
0x00007ff70c070000 - 0x00007ff70c07e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe0b1b0000 - 0x00007ffe0b405000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe0a820000 - 0x00007ffe0a8e7000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe088b0000 - 0x00007ffe08c50000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe08760000 - 0x00007ffe088aa000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe01b30000 - 0x00007ffe01b48000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe02010000 - 0x00007ffe0202e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe0a2e0000 - 0x00007ffe0a49f000 	C:\Windows\System32\USER32.dll
0x00007ffdebb00000 - 0x00007ffdebd90000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076\COMCTL32.dll
0x00007ffe08730000 - 0x00007ffe08757000 	C:\Windows\System32\win32u.dll
0x00007ffe09b90000 - 0x00007ffe09c39000 	C:\Windows\System32\msvcrt.dll
0x00007ffe09840000 - 0x00007ffe0986a000 	C:\Windows\System32\GDI32.dll
0x00007ffe08600000 - 0x00007ffe08721000 	C:\Windows\System32\gdi32full.dll
0x00007ffe08c60000 - 0x00007ffe08d03000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe0a570000 - 0x00007ffe0a59f000 	C:\Windows\System32\IMM32.DLL
0x00007ffe020d0000 - 0x00007ffe020dc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffdeacb0000 - 0x00007ffdead3d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffd97fb0000 - 0x00007ffd98d40000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe0a4b0000 - 0x00007ffe0a562000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe09fd0000 - 0x00007ffe0a076000 	C:\Windows\System32\sechost.dll
0x00007ffe098f0000 - 0x00007ffe09a09000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe09870000 - 0x00007ffe098e4000 	C:\Windows\System32\WS2_32.dll
0x00007ffe07e80000 - 0x00007ffe07ece000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffded780000 - 0x00007ffded7b6000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe01030000 - 0x00007ffe0103b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffe07e60000 - 0x00007ffe07e74000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe074a0000 - 0x00007ffe074ba000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe02000000 - 0x00007ffe0200a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffdec8b0000 - 0x00007ffdecaf1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe0a8f0000 - 0x00007ffe0ac65000 	C:\Windows\System32\combase.dll
0x00007ffe0a6a0000 - 0x00007ffe0a776000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdef220000 - 0x00007ffdef259000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe08fd0000 - 0x00007ffe09069000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe01b90000 - 0x00007ffe01b9f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffdf6f10000 - 0x00007ffdf6f2f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe09150000 - 0x00007ffe09836000 	C:\Windows\System32\SHELL32.dll
0x00007ffe06430000 - 0x00007ffe06c5b000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffe09a20000 - 0x00007ffe09af3000 	C:\Windows\System32\SHCORE.dll
0x00007ffe09070000 - 0x00007ffe090cd000 	C:\Windows\System32\shlwapi.dll
0x00007ffe08520000 - 0x00007ffe08544000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdf6ef0000 - 0x00007ffdf6f08000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffdfa0f0000 - 0x00007ffdfa100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe05e30000 - 0x00007ffe05f4d000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffe07940000 - 0x00007ffe079a8000 	C:\Windows\system32\mswsock.dll
0x00007ffdeae20000 - 0x00007ffdeae36000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffdf7ba0000 - 0x00007ffdf7bb0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffddde70000 - 0x00007ffdddeb5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffe0a140000 - 0x00007ffe0a2d5000 	C:\Windows\System32\ole32.dll
0x00007ffe07ce0000 - 0x00007ffe07cfc000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffe07400000 - 0x00007ffe07438000 	C:\Windows\system32\rsaenh.dll
0x00007ffe079e0000 - 0x00007ffe07a0b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffe07f70000 - 0x00007ffe07f96000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffe07b60000 - 0x00007ffe07b6c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe06ee0000 - 0x00007ffe06f10000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffe0a130000 - 0x00007ffe0a13a000 	C:\Windows\System32\NSI.dll
0x00007ffdd3780000 - 0x00007ffdd37c9000 	C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna10440786470473843670.dll
0x00007ffe0a810000 - 0x00007ffe0a818000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdec4e0000 - 0x00007ffdec4ea000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ffdeab40000 - 0x00007ffdeab4b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ffe003e0000 - 0x00007ffe003fc000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffe05af0000 - 0x00007ffe05b12000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-63116079

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-6b288e027b65d5e236c1aace1e7e362b-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.1)
OS uptime: 0 days 3:30 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 1992, Current Mhz: 1792, Mhz Limit: 1792

Memory: 4k page, system-wide physical 8066M (387M free)
TotalPageFile size 14210M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 651M, peak: 660M
current process commit charge ("private bytes"): 626M, peak: 642M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
