package com.ultimate.uff.uql.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = com.ultimate.uff.main.UffMainApplication.class)
public abstract class BaseCriteriaTest {

    @Autowired
    protected DatabaseService databaseService;

    protected static String TEST_TABLE;
    protected static boolean allTestsPassed = true;

    protected final ObjectMapper mapper = new ObjectMapper();

    @BeforeAll
    public static void setup(@Autowired DatabaseService databaseService) {
        TEST_TABLE = "TEST_CRITERIA_JSON_" + System.currentTimeMillis();
        databaseService.createTable(TEST_TABLE);
        databaseService.insertRecord(TEST_TABLE, "T1",
                "{ \"ROW_ID\": \"T1\","
                        + "\"singleField\": \"singleValue\","
                        + "\"multiField\": [\"value1\", \"value2\", \"value3\"],"
                        + "\"multiDateField\": [\"2024-01-01T00:00:00Z\", \"2024-03-15T12:00:00Z\"],"
                        + "\"multiIntField\": [5, 10, 15],"
                        + "\"nestedArray\": [ { \"nestedKey\": \"nestedValue1\", \"type\": \"typeA\" } ],"
                        + "\"nestedGroupArray\": ["
                        + "  { \"timestamp\": \"2024-01-01T00:00:00Z\", \"amount\": 10 },"
                        + "  { \"timestamp\": \"2024-02-01T00:00:00Z\", \"amount\": 20 }"
                        + "],"
                        + "\"numericField\": 42,"
                        + "\"createdDate\": \"2024-01-01T00:00:00Z\" }");

        databaseService.insertRecord(TEST_TABLE, "T2",
                "{ \"ROW_ID\": \"T2\","
                        + "\"singleField\": \"anotherValue\","
                        + "\"multiField\": [\"value4\", \"value2\"],"
                        + "\"multiDateField\": [\"2024-01-31T00:00:00Z\"],"
                        + "\"multiIntField\": [20, 25],"
                        + "\"nestedArray\": ["
                        + "  { \"nestedKey\": \"nestedValue4\", \"type\": \"typeX\" },"
                        + "  { \"nestedKey\": \"nestedValue2\", \"type\": \"typeY\" }"
                        + "],"
                        + "\"nestedGroupArray\": ["
                        + "  { \"timestamp\": \"2024-03-01T00:00:00Z\", \"amount\": 40 }"
                        + "],"
                        + "\"numericField\": 20,"
                        + "\"createdDate\": \"2024-01-31T00:00:00Z\" }");

        databaseService.insertRecord(TEST_TABLE, "T3",
                "{ \"ROW_ID\": \"T3\","
                        + "\"singleField\": \"noMatchValue\","
                        + "\"multiField\": [\"none1\", \"none2\"],"
                        + "\"multiDateField\": [\"2024-02-15T00:00:00Z\"],"
                        + "\"multiIntField\": [30],"
                        + "\"nestedArray\": [ { \"nestedKey\": \"otherNested1\", \"type\": \"Z\" } ],"
                        + "\"nestedGroupArray\": [],"
                        + "\"numericField\": 10,"
                        + "\"createdDate\": \"2024-03-01T00:00:00Z\" }");

        databaseService.insertRecord(TEST_TABLE, "T4",
                "{ \"ROW_ID\": \"T4\","
                        + "\"singleField\": \"someValue\","
                        + "\"multiField\": [\"x\"],"
                        + "\"multiDateField\": [\"2024-04-01T00:00:00Z\"],"
                        + "\"multiIntField\": [99],"
                        + "\"nestedArray\": [],"
                        + "\"nestedGroupArray\": [],"
                        + "\"numericField\": 99,"
                        + "\"createdDate\": \"2024-03-31T00:00:00Z\" }");

        databaseService.insertRecord(TEST_TABLE, "T5",
                "{ \"ROW_ID\": \"T5\","
                        + "\"singleField\": \"valueTest\","
                        + "\"multiField\": [\"y\"],"
                        + "\"multiDateField\": [\"2024-05-01T00:00:00Z\"],"
                        + "\"multiIntField\": [0, 1],"
                        + "\"nestedArray\": [],"
                        + "\"nestedGroupArray\": ["
                        + "  { \"timestamp\": \"2024-04-01T00:00:00Z\", \"amount\": 5 }"
                        + "],"
                        + "\"numericField\": 5,"
                        + "\"createdDate\": \"2024-04-30T00:00:00Z\" }");

        databaseService.insertRecord(TEST_TABLE, "T6",
                "{ \"ROW_ID\": \"T6\","
                        + "\"singleField\": \"XValue\","
                        + "\"multiField\": [\"m1\", \"m2\"],"
                        + "\"multiDateField\": [\"2024-06-01T00:00:00Z\", \"2024-07-01T00:00:00Z\"],"
                        + "\"multiIntField\": [88, 100],"
                        + "\"nestedArray\": ["
                        + "  { \"nestedKey\": \"deepValue1\" },"
                        + "  { \"nestedKey\": \"deepValue2\" }"
                        + "],"
                        + "\"nestedGroupArray\": ["
                        + "  { \"timestamp\": \"2024-06-10T00:00:00Z\", \"amount\": 60 },"
                        + "  { \"timestamp\": \"2024-07-20T00:00:00Z\", \"amount\": 70 }"
                        + "],"
                        + "\"numericField\": 88,"
                        + "\"createdDate\": \"2024-05-30T00:00:00Z\" }");

        databaseService.insertRecord(TEST_TABLE, "T7",
                "{ \"ROW_ID\": \"T7\","
                        + "\"singleField\": \"YValue\","
                        + "\"multiField\": [\"m3\"],"
                        + "\"multiDateField\": [\"2025-01-01T00:00:00Z\"],"
                        + "\"multiIntField\": [15],"
                        + "\"nestedArray\": [ { \"nestedKey\": \"deepValue3\" } ],"
                        + "\"nestedGroupArray\": ["
                        + "  { \"timestamp\": \"2025-01-01T00:00:00Z\", \"amount\": 80 }"
                        + "],"
                        + "\"numericField\": 15,"
                        + "\"createdDate\": \"2025-06-29T00:00:00Z\" }");
        databaseService.insertRecord(TEST_TABLE, "T8",
                "{ \"ROW_ID\": \"T8\"," +
                        "  \"nestedArray\": [" +
                        "    { \"nestedKey\": [" +
                        "        { \"subkey\": [" +
                        "            { \"subofsub\": \"DeepValue\" }," +
                        "            { \"subofsub\": \"OtherValue\" }" +
                        "        ] }" +
                        "    ] }" +
                        "  ] }");
        databaseService.insertRecord(TEST_TABLE, "T9",
                "{ \"ROW_ID\": \"T9\"," +
                "  \"nestedArray\": [" +
                "    { \"nestedKey\": [" +
                "        { \"subkey\": [" +
                "            { \"subofsub\": [\"one\", \"two\", \"three\"] }" +
                "        ] }" +
                "    ] }" +
                "  ] }");
    }

    @AfterAll
    public static void teardown(@Autowired DatabaseService databaseService) {
        if (allTestsPassed)
            databaseService.dropTable(TEST_TABLE);
        else
            System.out.println("Preserving table for debugging: " + TEST_TABLE);
    }

    protected void runJsonTest(String jsonCriteria, String expectedValue) {
        runJsonTest(jsonCriteria, null, expectedValue);
    }

    protected void runJsonTest(String jsonCriteria, String fieldName, String expectedValue) {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(jsonCriteria, new TypeReference<Map<String, Object>>() {
            });
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);

            assertFalse(result.isEmpty(), "Expected records but found none");

            boolean found = false;
            for (JsonNode row : result) {
                JsonNode json = mapper.readTree(row.get("JSON_ROW_DETAIL").asText());

                if (fieldName != null) {
                    found = containsFieldValue(json, fieldName, expectedValue);
                } else {
                    found = containsValueRecursive(json, expectedValue);
                }

                if (found)
                    break;
            }

            assertTrue(found, "Expected to find value: " + expectedValue
                    + (fieldName != null ? (" in field: " + fieldName) : ""));
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error parsing JSON or executing test", e);
        }
    }

    protected boolean containsValueRecursive(JsonNode node, String expectedValue) {
        if (node.isValueNode()) {
            return expectedValue.equals(node.asText());
        }
        if (node.isArray()) {
            for (JsonNode child : node) {
                if (containsValueRecursive(child, expectedValue))
                    return true;
            }
        }
        if (node.isObject()) {
            for (JsonNode child : node) {
                if (containsValueRecursive(child, expectedValue))
                    return true;
            }
        }
        return false;
    }

    protected void runJsonNegativeTest(String jsonCriteria, String fieldName, String unexpectedValue) {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(jsonCriteria, new TypeReference<Map<String, Object>>() {
            });
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);

            boolean notFound = true;
            for (JsonNode row : result) {
                JsonNode json = mapper.readTree(row.get("JSON_ROW_DETAIL").asText());

                if (containsFieldValue(json, fieldName, unexpectedValue)) {
                    notFound = false;
                    break;
                }
            }

            assertTrue(notFound, "Did not expect any match of: " + unexpectedValue
                    + " in field: " + fieldName);
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error parsing JSON or executing negative test", e);
        }
    }

    protected boolean containsFieldValue(JsonNode json, String fieldName, String expectedValue) {
        if (json.isObject()) {
            for (Iterator<Map.Entry<String, JsonNode>> it = json.fields(); it.hasNext();) {
                Map.Entry<String, JsonNode> entry = it.next();
    
                if (entry.getKey().equals(fieldName)) {
                    JsonNode value = entry.getValue();

                    // Null check to prevent NullPointerException
                    if (value == null || value.isNull()) {
                        return false; // Handle null values gracefully
                    }
    
                    // Case 1: Actual array node
                    if (value.isArray()) {
                        for (JsonNode item : value) {
                            if (expectedValue.equals(item.asText())) return true;
                        }
                    }
    
                    // Case 2: Stringified JSON array (SQL result)
                    if (value.isTextual() && value.asText().startsWith("[")) {
                        try {
                            JsonNode parsed = mapper.readTree(value.asText());
                            if (parsed.isArray()) {
                                for (JsonNode item : parsed) {
                                    if (expectedValue.equals(item.asText())) return true;
                                }
                            }
                        } catch (Exception e) {
                            // fallback: just do string contains
                            if (value.asText().contains(expectedValue)) return true;
                        }
                    }
    
                    // Case 3: Scalar value
                    if (value != null && expectedValue.equals(value.asText())) return true;
                }
    
                // Recurse into child nodes
                if (containsFieldValue(entry.getValue(), fieldName, expectedValue)) return true;
            }
        } else if (json.isArray()) {
            for (JsonNode item : json) {
                if (containsFieldValue(item, fieldName, expectedValue)) return true;
            }
        }
        return false;
    }       

    protected void assertPageResults(ArrayNode result, List<String> expectedOrder) {
        assertEquals(expectedOrder.size(), result.size(), "Unexpected number of records returned");
        List<String> actualOrder = new ArrayList<>();
        for (JsonNode node : result) {
            actualOrder.add(node.get("ROW_ID").asText());
        }
        assertEquals(expectedOrder, actualOrder, "Unexpected row order");
    }

    public void runJsonTest(String criteriaJson, String fieldName, List<String> expectedValues) {
        try {
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, parseJsonToMap(criteriaJson));
            List<String> actualIds = new ArrayList<>();
            for (JsonNode row : result) {
                actualIds.add(row.get(fieldName).asText());
            }
    
            assertEquals(expectedValues.size(), actualIds.size(), "Unexpected number of matching records");
            assertTrue(actualIds.containsAll(expectedValues), "Missing expected rows: " + expectedValues);
            assertTrue(expectedValues.containsAll(actualIds), "Found unexpected rows: " + actualIds);
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in runJsonTest", e);
        }
    }
    //@SuppressWarnings("unchecked")
    protected Map<String, Object> parseJsonToMap(String jsonString) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            throw new RuntimeException("Failed to parse JSON string to Map: " + jsonString, e);
        }
    }    

    protected void runSelectTest(String jsonCriteria, String fieldName, String expectedValue) {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(jsonCriteria, new TypeReference<>() {});
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);

            assertFalse(result.isEmpty(), "Expected records but found none");

            boolean found = false;
            for (JsonNode row : result) {
                JsonNode json = row; // ✅ No JSON_ROW_DETAIL, read directly

                if (fieldName != null) {
                    // ✅ Try to get by exact key (for flattened fields like nestedArray.nestedKey)
                    JsonNode actualNode = json.get(fieldName);
                
                    if (actualNode != null) {
                        try {
                            JsonNode expectedNode = mapper.readTree(expectedValue);

                            if (actualNode.isTextual() && (actualNode.asText().startsWith("[") || actualNode.asText().startsWith("{"))) {
                                actualNode = mapper.readTree(actualNode.asText());
                            }
                
                            if (expectedNode.isArray() && actualNode.isArray()) {
                                found = expectedNode.equals(actualNode);
                            } else if  (expectedNode.isObject() && actualNode.isObject()) {
                                found = expectedNode.equals(actualNode);
                            }                            
                            else {
                                found = expectedValue.equals(actualNode.asText());
                            }
                        } catch (Exception parseEx) {
                            // If expectedValue isn't valid JSON (e.g. plain string), fallback
                            found = expectedValue.equals(actualNode.asText());
                        }
                    } else {
                        // Fallback to recursive search (e.g. nested path resolution)
                        found = containsFieldValue(json, fieldName, expectedValue);
                    }
                } else {
                    found = containsValueRecursive(json, expectedValue);
                }                

                // ✅ If not found in this row, log it (so we see what failed)
                if (!found) {
                    System.out.println("▶ FIELD: " + fieldName);
                    System.out.println("▶ EXPECTED VALUE: " + expectedValue);
                    System.out.println("▶ CURRENT RECORD:");
                    System.out.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(json));
                }

                if (found)
                    break;
            }

            assertTrue(found, "Expected to find value: " + expectedValue
                    + (fieldName != null ? (" in field: " + fieldName) : ""));
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error parsing JSON or executing select test", e);
        }
    }
    
}
