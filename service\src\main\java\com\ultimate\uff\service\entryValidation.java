package com.ultimate.uff.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ultimate.uff.account.Account;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.ValidationResponse;

@Service
public class entryValidation {

    @Autowired
    private DatabaseService DatabaseService;
    public ValidationResponse checkBalance(String queryBuilderId) {

    Account ac = new Account(DatabaseService.getRecordById("", ""));
    
	return ac.getValidationResponse();
    }
    }


