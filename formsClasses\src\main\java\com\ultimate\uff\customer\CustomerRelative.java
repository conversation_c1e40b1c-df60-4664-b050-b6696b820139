package com.ultimate.uff.customer;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;

import java.util.HashMap;
import java.util.Map;

public class CustomerRelative extends ValidatableRecord {

    private final FieldValue<String> customerRelativeName = new FieldValue<>(null);
    private final FieldValue<String> relationn = new FieldValue<>(null);
    private final Map<String, Object> extraFields = new HashMap<>();

    public CustomerRelative(Map<String, Object> fieldDetail) {
        customerRelativeName.setValue((String) fieldDetail.get("customerRelativeName"));
        relationn.setValue((String) fieldDetail.get("relationn"));

        for (Map.Entry<String, Object> entry : fieldDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    private boolean isFieldHandled(String fieldName) {
        return fieldName.equals("customerRelativeName") || fieldName.equals("relationn");
    }

    public FieldValue<String> getCustomerRelativeName() { return customerRelativeName; }
    public FieldValue<String> getRelationn() { return relationn; }
    public Map<String, Object> getExtraFields() { return extraFields; }
}
