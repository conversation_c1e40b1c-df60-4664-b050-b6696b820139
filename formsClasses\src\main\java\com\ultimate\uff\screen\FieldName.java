package com.ultimate.uff.screen;

import com.ultimate.uff.validation.*;
import java.util.*;

public class FieldName extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private final FieldValue<String> fieldName = new FieldValue<>(null);
    private final FieldValue<Integer> size = new FieldValue<>(null);
    private final FieldValue<String> label = new FieldValue<>(null);
    private final FieldValue<Integer> row = new FieldValue<>(null);
    private final FieldValue<Integer> column = new FieldValue<>(null);

    public FieldName() {}

    public FieldName(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        fieldName.setValue((String) recordDetail.get("fieldName"));
        size.setValue((Integer) recordDetail.get("size"));
        label.setValue((String) recordDetail.get("label"));
        row.setValue((Integer) recordDetail.get("row"));
        column.setValue((Integer) recordDetail.get("column"));
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("fieldName", fieldName.getValue());
        map.put("size", size.getValue());
        map.put("label", label.getValue());
        map.put("row", row.getValue());
        map.put("column", column.getValue());
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
case "fieldName", "size", "label", "row", "column" -> true;
            default -> false;
        };
    }

    public FieldValue<String> getFieldName() { return fieldName; }
    public FieldValue<Integer> getSize() { return size; }
    public FieldValue<String> getLabel() { return label; }
    public FieldValue<Integer> getRow() { return row; }
    public FieldValue<Integer> getColumn() { return column; }
}