package com.ultimate.uff.teller;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Field extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private final FieldValue<String> field = new FieldValue<>(null);
    private final FieldValue<String> allowedData = new FieldValue<>(null);
    private final FieldValue<String> fieldFunction = new FieldValue<>(null);

    public Field(Map<String, Object> fieldDetail) {
        field.setValue((String) fieldDetail.get("field"));
        allowedData.setValue((String) fieldDetail.get("allowedData"));
        fieldFunction.setValue((String) fieldDetail.get("fieldFunction"));
        for (Map.Entry<String, Object> entry : fieldDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("field", field.getValue());
        map.put("allowedData", allowedData.getValue());
        map.put("fieldFunction", fieldFunction.getValue());
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
            case "field", "allowedData", "fieldFunction" -> true;
            default -> false;
        };
    }

    public FieldValue<String> getField() { return field; }
    public FieldValue<String> getAllowedData() { return allowedData; }
    public FieldValue<String> getFieldFunction() { return fieldFunction; }
}