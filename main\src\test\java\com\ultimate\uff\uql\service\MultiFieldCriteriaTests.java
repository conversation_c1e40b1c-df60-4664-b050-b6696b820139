package com.ultimate.uff.uql.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

@SpringBootTest(classes = com.ultimate.uff.main.UffMainApplication.class)
public class MultiFieldCriteriaTests extends BaseCriteriaTest {
    @Test
    public void test_DNBW_ArrayField() {
        try {
            Map<String, Object> criteria = Map.of(
                    "multiField[]", Map.of("DNBW", "val"));

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);

            // ✅ Expected: all rows where multiField doesn't contain any value starting with
            // "val"
            List<String> expectedIds = List.of("T3", "T4", "T5", "T6", "T7", "T8", "T9");
            List<String> actualIds = new ArrayList<>();

            for (JsonNode row : result) {
                actualIds.add(row.get("ROW_ID").asText());
            }

            assertEquals(expectedIds.size(), actualIds.size(), "Unexpected number of matching records");
            assertTrue(actualIds.containsAll(expectedIds), "Missing expected rows: " + expectedIds);
            assertTrue(expectedIds.containsAll(actualIds), "Found unexpected rows: " + actualIds);
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_DNBW_ArrayField", e);
        }
    }

    @Test
    public void test_NULL_MissingInArray() {
        runJsonNegativeTest("{ \"multiField[]\": { \"EQ\": null } }", "multiField", "value1");
    }

    @Test
    public void test_DNBW_ArrayField_AllowOtherPrefixes() {
        runJsonTest("{ \"multiField[]\": { \"DNBW\": \"zzz\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_DNEW_ArrayField_NoMatchSuffix() {
        runJsonTest("{ \"multiField[]\": { \"DNEW\": \"zzz\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_Array_MatchFullList() {
        runJsonNegativeTest("{ \"multiField[]\": { \"EQ\": [\"value1\", \"value2\"] } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_Array_WithNull() {
        runJsonNegativeTest("{ \"multiField[]\": { \"EQ\": null } }", "multiField", "value1");
    }

    @Test
    public void test_NE_OnArrayValue() {
        runJsonTest("{ \"multiField[]\": { \"NE\": \"zzz\" } }", "ROW_ID", "T1");
    }

    // === multiField (String[]) ===

    @Test
    public void test_DNBW_ArrayField1() {
        runJsonTest("{ \"multiField[]\": { \"DNBW\": \"z\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_DNEW_ArrayField() {
        runJsonTest("{ \"multiField[]\": { \"DNEW\": \"z\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_EmptyArrayField() {
        runJsonNegativeTest("{ \"multiField[]\": { \"EQ\": [] } }", "multiField", "value1");
    }

    @Test
    public void test_EQ_EmptyArray() {
        runJsonNegativeTest("{ \"emptyArray[]\": { \"EQ\": \"something\" } }", "emptyArray", "something");
    }

    @Test
    public void test_EQ_Null_InArrayField() {
        runJsonNegativeTest("{ \"multiField[]\": { \"EQ\": null } }", "multiField", "value1");
    }

    @Test
    public void test_DNBW_OnNullSingleField() {
        runJsonTest("{ \"missingField[]\": { \"DNBW\": \"start\" } }", "singleValue");
    }

    @Test
    public void test_DNEW_OnEmptyArray() {
        runJsonTest("{ \"emptyArray[]\": { \"DNEW\": \"val\" } }", "singleValue");
    }

    // === multiIntField (Integer[]) ===

    @Test
    public void test_EQ_MultiIntField() {
        runJsonTest("{ \"multiIntField[]:INT\": { \"EQ\": \"88\" } }", "ROW_ID", "T6");
    }

    @Test
    public void test_NE_MultiIntField() {
        runJsonNegativeTest("{ \"multiIntField[]:INT\": { \"NE\": \"5\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_DNBW_MultiIntField() {
        runJsonTest("{ \"multiIntField[]:INT\": { \"DNBW\": \"100\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_DNEW_MultiIntField() {
        runJsonTest("{ \"multiIntField[]:INT\": { \"DNEW\": \"0\" } }", "ROW_ID", List.of("T4", "T7", "T8", "T9"));
        runJsonNegativeTest("{ \"multiIntField[]:INT\": { \"DNEW\": \"0\" } }", "ROW_ID", "T1"); // has 10
    }

    // === multiDateField (Date[]) ===

    @Test
    public void test_EQ_MultiDateField_Exact() {
        runJsonTest("{ \"multiDateField[]:DATE\": { \"EQ\": \"2024-01-01T00:00:00Z\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_MultiDateField_Fallback() {
        runJsonTest("{ \"multiDateField[]:DATE\": { \"EQ\": \"2024-03-15\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_DNBW_MultiDateField() {
        runJsonTest("{ \"multiDateField[]:DATE\": { \"DNBW\": \"2025\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_DNEW_MultiDateField_ShouldNotMatch_Any() {
        runJsonNegativeTest("{ \"multiDateField[]:DATE\": { \"DNEW\": \"Z\" } }", "ROW_ID", "T1");
    }
    // === Edge Case: Missing field ===

    @Test
    public void test_EQ_MissingArrayField_ShouldMatchEQNull() {
        runJsonTest("{ \"missingArrayField[]\": { \"EQ\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_NE_MissingArrayField_ShouldNotMatch() {
        runJsonNegativeTest("{ \"missingArrayField[]\": { \"NE\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EW_MultiField() {
        runJsonNegativeTest("{ \"multiField[]\": { \"EW\": \"3\" } }", "ROW_ID", "T2");
    }

    @Test
    public void test_CT_MultiField() {
        runJsonTest("{ \"multiField[]\": { \"CT\": \"value2\" } }", "ROW_ID", List.of("T1", "T2"));
    }

    @Test
    public void test_LK_MultiField_Like() {
        runJsonTest("{ \"multiField[]\": { \"LK\": \"val%\" } }", "ROW_ID", List.of("T1", "T2"));
    }

    @Test
    public void test_RG_MultiIntField() {
        runJsonTest("{ \"multiIntField[]:INT\": { \"RG\": [10, 100] } }",
                "ROW_ID", List.of("T1", "T2", "T3", "T4", "T6", "T7"));

    }

    @Test
    public void test_RG_MultiDateField() {
        runJsonTest(
                "{ \"multiDateField[]:DATE\": { \"RG\": [\"2023-01-01T00:00:00Z\", \"2024-12-31T23:59:59Z\"] } }",
                "ROW_ID",
                List.of("T1", "T2", "T3", "T4", "T5", "T6"));
    }

    @Test
    public void test_NR_MultiIntField() {
        runJsonTest("{ \"multiIntField[]:INT\": { \"NR\": [10, 100] } }",
                "ROW_ID", List.of("T5", "T8", "T9"));
    }

    @Test
    public void test_GT_MultiIntField() {
        runJsonTest("{ \"multiIntField[]:INT\": { \"GT\": \"50\" } }", "ROW_ID", List.of("T4", "T6"));
    }    

    @Test
    public void test_LT_MultiIntField() {
        runJsonTest("{ \"multiIntField[]:INT\": { \"LT\": \"10\" } }", "ROW_ID", List.of("T1", "T5"));
    }    

    @Test
    public void test_BW_MultiField() {
        runJsonTest("{ \"multiField[]\": { \"BW\": \"val\" } }", "ROW_ID", List.of("T1", "T2"));
    }

    @Test
    public void test_EW_MultiField_NoMatch() {
        runJsonNegativeTest("{ \"multiField[]\": { \"EW\": \"3\" } }", "ROW_ID", "T2");
    }

    @Test
    public void test_DNEW_MultiDateField_ShouldNotMatch_T1() {
        runJsonNegativeTest("{ \"multiDateField[]:DATE\": { \"DNEW\": \"Z\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_LK_MultiField_MatchPartial() {
        runJsonTest("{ \"multiField[]\": { \"LK\": \"%value%\" } }", "ROW_ID", List.of("T1", "T2"));
    }

    @Test
    public void test_LI_MultiField_CaseInsensitive() {
        runJsonTest("{ \"multiField[]\": { \"LI\": \"%VALUE2%\" } }", "ROW_ID", List.of("T1", "T2"));
    }

    @Test
    public void test_LK_MultiField_NoMatch() {
        runJsonNegativeTest("{ \"multiField[]\": { \"LK\": \"%zzz%\" } }", "multiField", "value1");
    }

}
