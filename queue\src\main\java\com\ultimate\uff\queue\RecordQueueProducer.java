package com.ultimate.uff.queue;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.validation.GlobalResponse;
import jakarta.servlet.http.HttpSession;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.requestreply.KafkaReplyTimeoutException;
import org.springframework.kafka.requestreply.ReplyingKafkaTemplate;
import org.springframework.kafka.requestreply.RequestReplyFuture;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Service
public class RecordQueueProducer {

    private static final Logger logger = LoggerFactory.getLogger(RecordQueueProducer.class);

    private final ReplyingKafkaTemplate<String, String, String> replyingKafkaTemplate;
    private final HttpSession session;
    private final ObjectMapper objectMapper;

    @Autowired
    public RecordQueueProducer(ReplyingKafkaTemplate<String, String, String> replyingKafkaTemplate,
                                HttpSession session,
                                ObjectMapper objectMapper) {
        this.replyingKafkaTemplate = replyingKafkaTemplate;
        this.session = session;
        this.objectMapper = objectMapper;
    }

    public GlobalResponse sendInsertAndWait(String tableName, Map<String, Object> record,
                                            int authorizeNumber,
                                            String currentCompany,
                                            String primaryCompany,
                                            boolean isTenantBased) {
        return sendWithHeaders("record-topic", record, tableName, currentCompany, primaryCompany, authorizeNumber, isTenantBased);
    }

    public GlobalResponse sendAuthorizeAndWait(String tableName, String id,
                                               String currentCompany,
                                               String primaryCompany,
                                               int authorizeNumber,
                                               boolean isTenantBased) {
        return sendWithHeaders("record-authorize-topic", Map.of("id", id),
                tableName, currentCompany, primaryCompany, authorizeNumber, isTenantBased);
    }

    public GlobalResponse sendDeleteAndWait(String tableName, String id,
                                            String currentCompany,
                                            String primaryCompany,
                                            int authorizeNumber,
                                            boolean isTenantBased) {
        return sendWithHeaders("record-delete-topic", Map.of("id", id),
                tableName, currentCompany, primaryCompany, authorizeNumber, isTenantBased);
    }

    public GlobalResponse sendRejectAndWait(String tableName, String id,
                                            String currentCompany,
                                            String primaryCompany,
                                            int authorizeNumber,
                                            boolean isTenantBased) {
        return sendWithHeaders("record-reject-topic", Map.of("id", id),
                tableName, currentCompany, primaryCompany, authorizeNumber, isTenantBased);
    }

    private GlobalResponse sendWithHeaders(String topic,
                                           Map<String, Object> payload,
                                           String tableName,
                                           String currentCompany,
                                           String primaryCompany,
                                           int authorizeNumber,
                                           boolean isTenantBased) {
        try {
            String json = objectMapper.writeValueAsString(payload);
            ProducerRecord<String, String> record = new ProducerRecord<>(topic, json);

            record.headers().add(header("reply-topic", "record-reply-topic"));
            record.headers().add(header("table-name", tableName));
            record.headers().add(header("user-id", extractUserId()));
            record.headers().add(header("current-company", currentCompany));
            record.headers().add(header("primary-company", primaryCompany));
            record.headers().add(header("authorize-number", String.valueOf(authorizeNumber)));
            record.headers().add(header("is-tenant-based", String.valueOf(isTenantBased)));

            logger.debug("📤 Sending Kafka request to [{}] with headers: {}", topic, record.headers());

            RequestReplyFuture<String, String, String> future = replyingKafkaTemplate.sendAndReceive(record);
            ConsumerRecord<String, String> response = future.get();

            logger.debug("✅ Kafka response received from [{}]: {}", topic, response.value());

            Map<String, Object> responseMap = objectMapper.readValue(response.value(), new TypeReference<>() {});
            boolean success = Boolean.TRUE.equals(responseMap.get("success"));
            String message = (String) responseMap.get("message");

            return new GlobalResponse(success, message);

        } catch (ExecutionException ex) {
            if (ex.getCause() instanceof KafkaReplyTimeoutException) {
                String msg = "⏳ Kafka reply timed out. No response received from service. Please try again.";
                logger.warn(msg);
                return new GlobalResponse(false, msg);
            }
            logger.error("❌ Kafka execution error: {}", ex.getMessage(), ex);
            return new GlobalResponse(false, "Kafka execution error: " + ex.getMessage());

        } catch (Exception e) {
            logger.error("❌ Kafka error [{}]: {}", topic, e.getMessage(), e);
            return new GlobalResponse(false, "Kafka error: " + e.getMessage());
        }
    }

    private RecordHeader header(String key, String value) {
        return new RecordHeader(key, value.getBytes(StandardCharsets.UTF_8));
    }

    private String extractUserId() {
        try {
            Object user = session.getAttribute("user");
            if (user instanceof JsonNode jsonNode && jsonNode.has("user")) {
                return jsonNode.get("user").asText();
            }
        } catch (Exception e) {
            logger.warn("⚠️ Could not extract user from session: {}", e.getMessage());
        }
        return "system";
    }
}
