package com.ultimate.uff.validation;

import java.util.ArrayList;
import java.util.List;

/**
 * Generic wrapper for any field value, allowing dynamic error tracking,
 * override flagging, and optional string representation.
 *
 * @param <T> the type of the underlying value
 */
public class FieldValue<T> {

    /** The actual value being stored */
    private T value;

    /** List of validation or processing errors related to the field */
    private final List<String> errors = new ArrayList<>();

    /** Indicates whether this field has been overridden by a user/system */
    private boolean override;

    /**
     * Constructs a FieldValue with an initial value.
     *
     * @param value the initial field value
     */
    public FieldValue(T value) {
        this.value = value;
    }

    /** @return the current value */
    public T getValue() {
        return value;
    }

    /** @param value the new value to set */
    public void setValue(T value) {
        this.value = value;
    }

    /**
     * Adds a validation error message to this field.
     *
     * @param error the error message to add
     */
    public void setError(String error) {
        if (error != null && !error.trim().isEmpty()) {
            errors.add(error);
        }
    }

    /** @return list of all error messages */
    public List<String> getErrors() {
        return errors;
    }

    /** @return true if this field has any errors */
    public boolean hasError() {
        return !errors.isEmpty();
    }

    /** @param override whether the field has been overridden */
    public void setOverride(boolean override) {
        this.override = override;
    }

    /** @return true if the field has been overridden */
    public boolean isOverride() {
        return override;
    }

    /** @return string representation of the value (or empty string if null) */
    @Override
    public String toString() {
        return value != null ? value.toString() : "";
    }
}
