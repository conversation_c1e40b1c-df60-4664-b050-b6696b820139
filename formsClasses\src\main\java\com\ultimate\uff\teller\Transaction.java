package com.ultimate.uff.teller;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Transaction extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String narrative;
    private final FieldValue<String> narrativeVal = new FieldValue<>(null);
    private String shortDesc;
    private final FieldValue<String> shortDescVal = new FieldValue<>(null);
    private String debitCreditInd;
    private final FieldValue<String> debitCreditIndVal = new FieldValue<>(null);
    private String turnoverCharge;
    private final FieldValue<String> turnoverChargeVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private List<String> recordAuthorizer = new ArrayList<>();
    private final FieldValue<List<String>> recordAuthorizerVal = new FieldValue<>(new ArrayList<>());
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public Transaction() {}

    public Transaction(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object narrativeObj = recordDetail.get("narrative");
        if (narrativeObj != null) {
            this.setNarrative(narrativeObj.toString());
        }
        Object shortDescObj = recordDetail.get("shortDesc");
        if (shortDescObj != null) {
            this.setShortDesc(shortDescObj.toString());
        }
        Object debitCreditIndObj = recordDetail.get("debitCreditInd");
        if (debitCreditIndObj != null) {
            this.setDebitCreditInd(debitCreditIndObj.toString());
        }
        Object turnoverChargeObj = recordDetail.get("turnoverCharge");
        if (turnoverChargeObj != null) {
            this.setTurnoverCharge(turnoverChargeObj.toString());
        }
        Object IDObj = recordDetail.get("ID");
        if (IDObj != null) {
            this.setID(IDObj.toString());
        }
        Object recordStatusObj = recordDetail.get("recordStatus");
        if (recordStatusObj != null) {
            this.setRecordStatus(recordStatusObj.toString());
        }
        Object recordInputterObj = recordDetail.get("recordInputter");
        if (recordInputterObj != null) {
            this.setRecordInputter(recordInputterObj.toString());
        }
        Object listObj = recordDetail.get("recordAuthorizer");
        if (listObj instanceof List<?>) {
            this.setRecordAuthorizer((List<String>) listObj);
        }
        Object recordCountObj = recordDetail.get("recordCount");
        if (recordCountObj != null) {
            this.setRecordCount(recordCountObj.toString());
        }
        Object dateTimeObj = recordDetail.get("dateTime");
        if (dateTimeObj != null) {
            this.setDateTime(dateTimeObj.toString());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (narrative != null) map.put("narrative", narrative);
        if (shortDesc != null) map.put("shortDesc", shortDesc);
        if (debitCreditInd != null) map.put("debitCreditInd", debitCreditInd);
        if (turnoverCharge != null) map.put("turnoverCharge", turnoverCharge);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "narrative", "shortDesc", "debitCreditInd", "turnoverCharge", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setNarrative(String narrative) {
        this.narrative = narrative;
        this.narrativeVal.setValue(narrative);
    }
    public String getNarrative() { return this.narrative; }
    public FieldValue<String> narrativeVal() { return narrativeVal; }
    public void setShortDesc(String shortDesc) {
        this.shortDesc = shortDesc;
        this.shortDescVal.setValue(shortDesc);
    }
    public String getShortDesc() { return this.shortDesc; }
    public FieldValue<String> shortDescVal() { return shortDescVal; }
    public void setDebitCreditInd(String debitCreditInd) {
        this.debitCreditInd = debitCreditInd;
        this.debitCreditIndVal.setValue(debitCreditInd);
    }
    public String getDebitCreditInd() { return this.debitCreditInd; }
    public FieldValue<String> debitCreditIndVal() { return debitCreditIndVal; }
    public void setTurnoverCharge(String turnoverCharge) {
        this.turnoverCharge = turnoverCharge;
        this.turnoverChargeVal.setValue(turnoverCharge);
    }
    public String getTurnoverCharge() { return this.turnoverCharge; }
    public FieldValue<String> turnoverChargeVal() { return turnoverChargeVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(List<String> recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public List<String> getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<List<String>> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}
