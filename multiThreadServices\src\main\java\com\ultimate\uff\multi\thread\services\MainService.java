package com.ultimate.uff.multi.thread.services;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ultimate.uff.service.MiltiService;
import com.ultimate.uff.uql.service.DatabaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class MainService {

    @Autowired
    private ApplicationContext context;

    @Autowired
    private DatabaseService databaseService;

    private final Map<String, Thread> runningAgents = new HashMap<>();
    private final Set<String> manuallyStopped = new HashSet<>();
    private final Map<String, Integer> agentNumbers = new HashMap<>();
    private int nextAgentNumber = 1;

    private volatile boolean watcherKeepRunning = false;
    private volatile boolean watcherRunning = false;
    private Thread watcherThread = null;

    // 🟢 Start agents + watcher
    public synchronized void startAgents() {
        if (watcherRunning) {
            System.out.println("🔁 Watcher already running.");
            return;
        }
        startBackgroundWatcher();
    }

    // 🛑 Stop agents + watcher
    public synchronized void stopAgents() {
        for (String name : runningAgents.keySet()) {
            AgentWorker agent = (AgentWorker) context.getBean(name);
            agent.stop();
            System.out.println("🛑 Agent " + agentNumbers.get(name) + " (" + name + ") stopped.");
            manuallyStopped.add(name);
        }
        runningAgents.clear();
        watcherKeepRunning = false;
        System.out.println("🛑 All agents manually stopped.");
    }

    // 🧹 Clear manual block
    public synchronized void clearManuallyStoppedAgents() {
        manuallyStopped.clear();
        System.out.println("🧹 Cleared manually stopped agent list.");
    }

    public Set<String> getRunningAgentNames() {
        return runningAgents.keySet();
    }

    public Set<String> getManuallyStoppedAgents() {
        return manuallyStopped;
    }

    public boolean isWatcherRunning() {
        return watcherRunning;
    }

    public Map<String, Integer> getAgentNumberMap() {
        return agentNumbers;
    }

    // 🔁 Background DB watcher
    private void startBackgroundWatcher() {
        watcherKeepRunning = true;
        watcherRunning = true;

        watcherThread = new Thread(() -> {
            while (watcherKeepRunning) {
                try {
                    syncWithDatabase();
                    Thread.sleep(10000);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            watcherRunning = false;
            System.out.println("🛑 Background watcher stopped.");
        });

        watcherThread.setName("e-Agent-Watcher");
        watcherThread.setDaemon(true);
        watcherThread.start();

        System.out.println("🔁 Background DB watcher started.");
    }

    // 🔄 Sync DB: start new, stop removed
    private synchronized void syncWithDatabase() throws Exception {
        Map<String, Object> criteria = Map.of(
            "status", Map.of("EQ", "run"));
            ArrayNode serviceNodes = databaseService.getRecordsByCriteria("service", criteria);
        Set<String> enabledServiceNames = new HashSet<>();

        for (JsonNode node : serviceNodes) {
            String id = node.get("ROW_ID").asText();
            MiltiService record = new MiltiService(databaseService.getRecordById("service", id));

            String beanName = record.getServiceName().getValue();
            String user = record.getUser().getValue();

            enabledServiceNames.add(beanName);

            if (!context.containsBean(beanName)) {
                System.err.println("❌ No agent bean: " + beanName);
                continue;
            }

            if (manuallyStopped.contains(beanName)) continue;

            AgentWorker agent = (AgentWorker) context.getBean(beanName);
            if (agent.isRunning()) continue;

            agent.setUser(user);
            Thread thread = new Thread(agent);
            thread.setDaemon(true);
            thread.start();

            runningAgents.put(beanName, thread);

            // Assign agent number if first time
            agentNumbers.putIfAbsent(beanName, nextAgentNumber++);
            int agentNumber = agentNumbers.get(beanName);

            System.out.println("🚀 Started Agent " + agentNumber + ": " + beanName + " for user: " + user);
        }

        // Auto-stop disabled services
        Set<String> currentlyRunning = new HashSet<>(runningAgents.keySet());
        for (String beanName : currentlyRunning) {
            if (!enabledServiceNames.contains(beanName)) {
                AgentWorker agent = (AgentWorker) context.getBean(beanName);
                agent.stop();
                runningAgents.remove(beanName);
                System.out.println("🛑 Auto-stopped Agent " + agentNumbers.get(beanName) + ": " + beanName + " (disabled in DB)");
            }
        }
    }
}
