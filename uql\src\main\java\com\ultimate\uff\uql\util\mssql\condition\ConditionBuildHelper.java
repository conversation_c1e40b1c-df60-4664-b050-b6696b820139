package com.ultimate.uff.uql.util.mssql.condition;

import com.ultimate.uff.uql.model.ParsedFilter;
import com.ultimate.uff.uql.util.mssql.MSSQLExpressionBuilder;
import com.ultimate.uff.uql.util.mssql.MSSQLUtils;

/**
 * Helper utilities for building SQL conditions across MultiField and
 * GroupField.
 */
public class ConditionBuildHelper {

    private ConditionBuildHelper() {
        // Utility class, no instantiation
    }

    /**
     * Wraps a WHERE condition inside EXISTS (SELECT 1 ...) for a given array/table
     * expression
     */
    public static String buildExists(String arrayExpr, String condition) {
        return "EXISTS (SELECT 1 FROM " + arrayExpr + " WHERE " + condition + ")";
    }

    public static String buildExists(ParsedFilter filter, String condition, boolean isDeep) {
        String existsClause;
        if (isDeep) {
            boolean lastFieldIsArray = filter.getField() != null && filter.getField().endsWith("[]");

            existsClause = "EXISTS (SELECT 1 FROM (" +
                    MSSQLExpressionBuilder.buildDeepGroupFieldSelector("JSON_ROW_DETAIL", filter.getGroupPathParts(),
                            lastFieldIsArray)
                    +
                    ") AS finalField WHERE " + condition + ")";

        } else {
            existsClause = "EXISTS (SELECT 1 FROM OPENJSON(JSON_QUERY(JSON_ROW_DETAIL, '$." +
                    filter.getBaseField() + "')) arr WHERE " + condition + ")";
        }

        return existsClause;
    }

    /** Builds simple LIKE or CT or DNBW or DNEW type conditions */
    public static String buildLikeCondition(String columnExpr, String pattern) {
        String castedExpr = "CAST(" + columnExpr + " AS NVARCHAR(MAX))";
        return castedExpr + " LIKE " + MSSQLUtils.likeQuote(pattern);
    }

    /**
     * Formats a value correctly, considering fallback for DATE simple format if
     * needed
     */
    public static String formatValue(Object value, boolean isDate) {
        if (isDate && value instanceof String s) {
            MSSQLUtils.validateDateFormat(s);
            if (MSSQLUtils.isSimpleDateFormat(s)) {
                return "CAST(" + MSSQLUtils.formatSqlValue(s, "STRING") + " AS DATE)";
            }
            return MSSQLUtils.formatSqlValue(s, "STRING");
        }
        return MSSQLUtils.formatSqlValue(value, "NVARCHAR(MAX)");
    }

    public static String buildILikeCondition(String columnExpr, String pattern) {
        return "LOWER(CAST(" + columnExpr + " AS NVARCHAR(MAX))) LIKE " + MSSQLUtils.likeQuote(pattern.toLowerCase());
    }

}
