package com.ultimate.uff.menuClasses;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class Menu {

    private String id;
    private String tableName;
    private String inputter;
    private String authorizer;
    private String inDate;
    private String inTime;
    private int recordCount;

    // Group "menus"
    private List<Menus> menusList;

    // Constructor that initializes Menu fields from the recordDetail map
    public Menu(Map<String, Object> recordDetail) {
        this.id = (String) recordDetail.get("ID");
        this.tableName = (String) recordDetail.get("table_name");
        this.inputter = (String) recordDetail.get("Inputter");
        this.authorizer = (String) recordDetail.get("Authorizer");
        this.inDate = (String) recordDetail.get("IN_DATE");
        this.inTime = (String) recordDetail.get("IN_TIME");

        // Handle record_count safely
        Object recordCountValue = recordDetail.get("record_count");
        if (recordCountValue instanceof Integer) {
            this.recordCount = (Integer) recordCountValue;
        } else if (recordCountValue instanceof String) {
            try {
                this.recordCount = Integer.parseInt((String) recordCountValue);
            } catch (NumberFormatException e) {
                this.recordCount = 0; // Default value
            }
        } else {
            this.recordCount = 0; // Default value
        }

        // Initialize and populate menusList from the map
        Object fieldNameValue = recordDetail.get("menus");
        this.menusList = new ArrayList<>();
        if (fieldNameValue instanceof List) {
            List<Map<String, Object>> fieldNameMaps = (List<Map<String, Object>>) fieldNameValue;
            for (Map<String, Object> fieldMap : fieldNameMaps) {
                this.menusList.add(new Menus(fieldMap));
            }
        }
    }

    // Method to update the recordDetail map and return the updated map
    public Map<String, Object> updateRecordDetail(Map<String, Object> recordDetail) {
        if (this.id != null) {
            recordDetail.put("ID", this.id);
        }
        if (this.tableName != null) {
            recordDetail.put("table_name", this.tableName);
        }
        if (this.inputter != null) {
            recordDetail.put("Inputter", this.inputter);
        }
        if (this.authorizer != null) {
            recordDetail.put("Authorizer", this.authorizer);
        }
        if (this.inDate != null) {
            recordDetail.put("IN_DATE", this.inDate);
        }
        if (this.inTime != null) {
            recordDetail.put("IN_TIME", this.inTime);
        }
        recordDetail.put("record_count", this.recordCount);

        // Convert menusList to List<Map> and update the map if non-null
        if (this.menusList != null && !this.menusList.isEmpty()) {
            List<Map<String, Object>> menusMaps = new ArrayList<>();
            for (Menus menu : this.menusList) {
                menusMaps.add(menu.toMap());
            }
            recordDetail.put("fieldName", menusMaps);
        }

        return recordDetail;
    }

    // Getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getInputter() {
        return inputter;
    }

    public void setInputter(String inputter) {
        this.inputter = inputter;
    }

    public String getAuthorizer() {
        return authorizer;
    }

    public void setAuthorizer(String authorizer) {
        this.authorizer = authorizer;
    }

    public String getInDate() {
        return inDate;
    }

    public void setInDate(String inDate) {
        this.inDate = inDate;
    }

    public String getInTime() {
        return inTime;
    }

    public void setInTime(String inTime) {
        this.inTime = inTime;
    }

    public int getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
    }

    public List<Menus> getMenusList() {
        return menusList;
    }

    public void setMenusList(List<Menus> menusList) {
        this.menusList = menusList;
    }
}
