package com.ultimate.uff.uql.util.mssql;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class MSSQLUtils {

    // == Input Validators == //

    public static void validateInput(String input, String name) {
        if (input == null || input.trim().isEmpty()) {
            throw new IllegalArgumentException(name + " cannot be null or empty.");
        }
    }

    public static void validateDateFormat(String value) {
        if (value == null || value.isBlank()) return;
        try {
            if (value.length() > 10) {
                OffsetDateTime.parse(value); // full ISO timestamp
            } else {
                LocalDate.parse(value); // simple date
            }
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format: " + value);
        }
    }

    public static boolean isSimpleDateFormat(String value) {
        return value != null && value.length() == 10 && value.charAt(4) == '-' && value.charAt(7) == '-';
    }

    public static boolean isDateOnly(Object value) {
        return value != null && value.toString().matches("\\d{4}-\\d{2}-\\d{2}");
    }

    // == SQL Escaping and Quoting == //

    public static String escapeIdentifier(String id) {
        validateInput(id, "Identifier");
        return "[" + id.replace("]", "]]") + "]";
    }

    public static String likeQuote(String value) {
        return "N'" + value.replace("'", "''") + "'";
    }

    // == Type Conversion Helpers == //

    public static String getSqlCastType(String type) {
        return switch (type.toUpperCase()) {
            case "INT"     -> "INT";
            case "DOUBLE"  -> "FLOAT";
            case "BOOLEAN" -> "BIT";
            case "DATE"    -> "DATETIME";
            default        -> "NVARCHAR(MAX)";
        };
    }

    public static String formatSqlValue(Object value, String type) {
        if (value == null) return "NULL";

        return switch (type.toUpperCase()) {
            case "INT", "DOUBLE", "BOOLEAN" -> value.toString();
            case "DATE" -> "'" + value.toString() + "'";
            default     -> "N'" + value.toString().replace("'", "''") + "'";
        };
    }

    // == JSON Field Access Helpers == //

    public static String escapeFieldExprByType(String fieldName) {
        String[] parts = fieldName.split(":");
        String base = parts[0];
        String type = parts.length > 1 ? parts[1].toUpperCase() : "STRING";
        String expr = "JSON_VALUE(JSON_ROW_DETAIL, '$." + base.replace("'", "''") + "')";

        return switch (type) {
            case "INT"     -> "TRY_CAST(" + expr + " AS INT)";
            case "DOUBLE"  -> "TRY_CAST(" + expr + " AS FLOAT)";
            case "BOOLEAN" -> "TRY_CAST(" + expr + " AS BIT)";
            case "DATE"    -> "TRY_CAST(" + expr + " AS DATETIME)";
            default        -> expr;
        };
    }

    public static String escapeOrderClauseJson(String raw) {
        String trimmed = raw.trim();
        boolean desc = trimmed.toUpperCase().endsWith(" DESC");
        boolean asc  = trimmed.toUpperCase().endsWith(" ASC");

        String baseField = desc ? trimmed.substring(0, trimmed.length() - 5).trim()
                                : asc  ? trimmed.substring(0, trimmed.length() - 4).trim()
                                       : trimmed;

        String escapedField = escapeFieldExprByType(baseField);
        return escapedField + (desc ? " DESC" : " ASC");
    }

    public static boolean isDateField(String fieldName) {
        if (fieldName == null) return false;
        String lower = fieldName.toLowerCase();
        return lower.endsWith(":date") || lower.contains("date") || lower.contains("timestamp") || lower.contains("time");
    }

    public static String normalizeDateTime(Object value) {
        if (value == null) return null;

        try {
            if (value instanceof java.sql.Timestamp ts) {
                return ts.toInstant().atOffset(ZoneOffset.UTC).toString(); // 2024-01-01T00:00:00Z
            } else if (value instanceof java.sql.Date date) {
                return date.toLocalDate().atStartOfDay().atOffset(ZoneOffset.UTC).toString();
            } else if (value instanceof String str) {
                // Try to parse known formats
                if (str.contains("T")) {
                    return OffsetDateTime.parse(str).toString();
                } else {
                    // Parse typical "yyyy-MM-dd HH:mm:ss[.SSS]" formats
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss[.SSS]");
                    LocalDateTime ldt = LocalDateTime.parse(str, formatter);
                    return ldt.atOffset(ZoneOffset.UTC).toString();
                }
            } else {
                return value.toString(); // fallback
            }
        } catch (Exception e) {
            return value.toString(); // fallback in case of parsing failure
        }
    }

}
