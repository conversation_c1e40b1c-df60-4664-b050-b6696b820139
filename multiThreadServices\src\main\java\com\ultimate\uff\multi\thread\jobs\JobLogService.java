package com.ultimate.uff.multi.thread.jobs;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ultimate.uff.multi.thread.generatedClass.ServiceLogs;
import com.ultimate.uff.multi.thread.services.LoggerClass;
import com.ultimate.uff.recordHandler.PostActionData;
import com.ultimate.uff.recordHandler.PostRecordService;
import com.ultimate.uff.validation.ReadOnlyDatabaseService;

import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;

@Service
public class JobLogService {
    //private static final Logger fileLogger = LoggerFactory.getLogger("ServiceLogFile");
    private Logger fileLogger;
    @Autowired
    private final PostRecordService postRecordService;
    private final ReadOnlyDatabaseService readOnlyDatabaseService;
    private final LoggerClass loggerClass;

    public JobLogService(PostRecordService postRecordService, LoggerClass loggerClass,ReadOnlyDatabaseService readOnlyDatabaseService) {
        this.postRecordService = postRecordService;
        this.loggerClass = loggerClass;
        this.readOnlyDatabaseService=readOnlyDatabaseService;
    }
    @PostConstruct
    public void init() {
        this.fileLogger = loggerClass.getLogger();
    }
    /*public void log(String type, String refId, String action, String message) {
        insertToDatabase(type, refId, action, message);
    }*/

    public void logInfo(String type, String refId, String action, String message) {
        insertToDatabase(type, refId, action, message);
    }

    public void logWarn(String type, String refId, String action, String message) {
        insertToDatabase(type, refId, action, message);
    }

    public void logError(String type, String refId, String action, String message) {
        insertToDatabase(type, refId, action, message);
    }

    /*public void log(String type, String refId, String action, String message) {
        if (fileLogger != null) {
            fileLogger.info("Type: {}, RefID: {}, Action: {}, Message: {}", type, refId, action, message);
        }
        insertToDatabase(type, refId, action, message);
    }
    public void logInfo(String type, String refId, String action, String message) {
        if (fileLogger != null) {
            fileLogger.info("Type: {}, RefID: {}, Action: {}, Message: {}", type, refId, action, message);
        }
        insertToDatabase(type, refId, action, message);
    }

    public void logDebug(String type, String refId, String action, String message) {
        if (fileLogger != null) {
            fileLogger.debug("Type: {}, RefID: {}, Action: {}, Message: {}", type, refId, action, message);
        }
    }

    public void logWarn(String type, String refId, String action, String message) {
        if (fileLogger != null) {
            fileLogger.warn("Type: {}, RefID: {}, Action: {}, Message: {}", type, refId, action, message);
        }
        insertToDatabase(type, refId, action, message);
    }

    public void logError(String type, String refId, String action, String message, Throwable exception) {
        if (fileLogger != null) {
            fileLogger.error("Type: {}, RefID: {}, Action: {}, Message: {}", type, refId, action, message, exception);
        }
        insertToDatabase(type, refId, action, message);
    }*/
    private void insertToDatabase(String type, String refId, String action, String message) {
        ServiceLogs serviceLogs = new ServiceLogs();
        serviceLogs.setID(UUID.randomUUID().toString());
        serviceLogs.setType(type);
        serviceLogs.setRefId(refId);
        serviceLogs.setAction(action);
        serviceLogs.setMessage(message);
        serviceLogs.setTimestamp(String.valueOf(System.currentTimeMillis()));

        PostActionData actionData = new PostActionData();
        actionData.setTable("serviceLogs");
        actionData.setFunction("insert");
        actionData.setId(serviceLogs.getID());
        actionData.setBody(serviceLogs.toMap());
        actionData.setAuthNum(0);
        postRecordService.handle(actionData);
    }
    public Optional<Map<String, Object>> getLatestLog(String type, String refId) {
        List<String> ids = readOnlyDatabaseService.getRecordIdsByCriteria("serviceLogs", Map.of(
            "type", type,
            "refId", refId
        ));
    
        if (ids.isEmpty()) return Optional.empty();
    
        Map<String, Object> latestLog = null;
        long latestTimestamp = Long.MIN_VALUE;
    
        for (String id : ids) {
            Map<String, Object> logData = readOnlyDatabaseService.getRecordById("serviceLogs", id);
            if (logData == null) continue;
    
            Object timestampObj = logData.get("timestamp");
            if (timestampObj == null) continue;
    
            long ts;
            try {
                ts = Long.parseLong(timestampObj.toString());
            } catch (NumberFormatException e) {
                continue;
            }
    
            if (ts > latestTimestamp) {
                latestTimestamp = ts;
                latestLog = logData;
            }
        }
        return Optional.ofNullable(latestLog);
    }
}

    //@Autowired
    //private PostRecordService postRecordService;

    /*TableCons table = new TableCons ();
table.setFieldNAme("value");
PostActionData action = new PostActionData();
		action.setTable(tableName);
		action.setFunction("insert");
		action.setId(id);
		action.setBody(emptyRecordDetail);
		action.setAuthNum(0);
		postRecordService.handle(action);*/


