package com.ultimate.uff.teller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.CustomValidator;
import jakarta.servlet.http.HttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class denomDefaulting implements CustomValidator {

    private static final Logger logger = LoggerFactory.getLogger(denomDefaulting.class);

    @Autowired
    private DatabaseService da;

    @Autowired
    private HttpSession sa;

    private static final String BASE_CURRENCY = "YER";
    private static final Map<String, Map<String, Double>> CURRENCY_RATES = Map.of(
            "USD", Map.of("buyRate", 550.0, "sellRate", 552.0, "midRate", 551.0),
            "EUR", Map.of("buyRate", 600.0, "sellRate", 603.0, "midRate", 601.5)
    );

    @Override
    public Map<String, Object> newData(Map<String, Object> recordDetail) {
        Teller teller = new Teller(recordDetail);
        String txnCode = teller.getTransactionCode();
        TellerTransaction transaction = new TellerTransaction(da.getRecordById("tellerTransaction", txnCode));

        String debitAccountType = transaction.getAccountType1().getValue();
        String creditAccountType = transaction.getAccountType2().getValue();

        // Fetch tillId and set debitTellerId and creditTellerId
        String tillId = getTillIdAndSetTellerIds(teller);

        if (tillId != null) {
            updateDenominations(teller, debitAccountType, creditAccountType);
            setAccounts(teller, tillId);
            adjustAmounts(teller, debitAccountType, creditAccountType);
        } else {
            logger.warn("Till ID not found. Skipping further processing.");
        }
        getTillId(teller);

        return teller.toMap();
    }

    private void getTillId(Teller teller) {
        Object user = sa.getAttribute("user");
        if (user instanceof JsonNode jsonUser) {
            JsonNode rowIdNode = jsonUser.get("user");
            if (rowIdNode != null && !rowIdNode.isNull()) {
                String userId = rowIdNode.asText();
                Map<String, Object> criteria = Map.of("user", Map.of("EQ", userId));
                ArrayNode result = da.getRecordsByCriteria("userTill", criteria);
                if (result != null && result.size() > 0) {
                	teller.setDebitTellerId(result.get(0).get("ROW_ID").asText());
                	teller.setCreditTellerId(result.get(0).get("ROW_ID").asText());
                }
            }
            
            
        }
    }

    private void setAccounts(Teller teller, String tillId) {
        String debitCurrency = teller.getDebitCurrency();
        String creditCurrency = teller.getCreditCurrency();

        String debitAccountId = debitCurrency + "10001" + tillId;
        String creditAccountId = creditCurrency + "10001" + tillId;

        teller.setDebitAccount(debitAccountId);
        teller.setCreditAccount(creditAccountId);
    }

    private void updateDenominations(Teller teller, String debitAccountType, String creditAccountType) {
        String debitCurrency = teller.getDebitCurrency();
        String creditCurrency = teller.getCreditCurrency();

        List<DebitUnit> debitDenomList = teller.getDebitUnitList();
        List<CreditUnit> creditDenomList = teller.getCreditUnitList();

        if ("internal".equals(debitAccountType)) {
            updateDebitDenominations(debitDenomList, debitCurrency);
        }

        if ("internal".equals(creditAccountType)) {
            updateCreditDenominations(creditDenomList, creditCurrency);
        }
    }

    private void updateDebitDenominations(List<DebitUnit> debitDenomList, String currency) {
        Map<String, Object> criteria = Map.of("ccy", Map.of("EQ", currency));
        ArrayNode denomRecords = da.getRecordsByCriteria("ccyDenom", criteria);

        if (denomRecords != null) {
            Map<String, DebitUnit> existingMap = debitDenomList.stream()
                    .collect(Collectors.toMap(DebitUnit::getDebitUnit, unit -> unit));

            debitDenomList.clear();
            for (JsonNode item : denomRecords) {
                String rowId = item.get("ROW_ID").asText();
                DebitUnit unit = existingMap.getOrDefault(rowId, new DebitUnit());
                unit.setDebitUnit(rowId);
                unit.setDebitUnitValue(existingMap.containsKey(rowId) ? existingMap.get(rowId).getDebitUnitValue() : 0.0);
                debitDenomList.add(unit);
            }
        }
    }

    private void updateCreditDenominations(List<CreditUnit> creditDenomList, String currency) {
        Map<String, Object> criteria = Map.of("ccy", Map.of("EQ", currency));
        ArrayNode denomRecords = da.getRecordsByCriteria("ccyDenom", criteria);

        if (denomRecords != null) {
            Map<String, CreditUnit> existingMap = creditDenomList.stream()
                    .collect(Collectors.toMap(CreditUnit::getCreditUnit, unit -> unit));

            creditDenomList.clear();
            for (JsonNode item : denomRecords) {
                String rowId = item.get("ROW_ID").asText();
                CreditUnit unit = existingMap.getOrDefault(rowId, new CreditUnit());
                unit.setCreditUnit(rowId);
                unit.setCreditUnitValue(existingMap.containsKey(rowId) ? existingMap.get(rowId).getCreditUnitValue() : 0.0);
                creditDenomList.add(unit);
            }
        }
    }

    private void adjustAmounts(Teller teller, String debitAccountType, String creditAccountType) {
        String debitCurrency = teller.getDebitCurrency();
        String creditCurrency = teller.getCreditCurrency();

        Double dealRate = 1.0;

        // Set the mid rates for foreign currencies
        Double debitMidRate = CURRENCY_RATES.getOrDefault(debitCurrency, Map.of("midRate", 1.0)).get("midRate");
        Double creditMidRate = CURRENCY_RATES.getOrDefault(creditCurrency, Map.of("midRate", 1.0)).get("midRate");

        // Set rates if foreign currency
        if (!BASE_CURRENCY.equals(debitCurrency)) {
            teller.setDebitRate(debitMidRate);
        }

        if (!BASE_CURRENCY.equals(creditCurrency)) {
            teller.setCreditRate(creditMidRate);
        }

        // Calculate deal rate only if currencies differ
        if (!debitCurrency.equals(creditCurrency)) {

            if (BASE_CURRENCY.equals(debitCurrency)) {
                dealRate = CURRENCY_RATES.getOrDefault(creditCurrency, Map.of("sellRate", 1.0)).get("sellRate");
            } 
            else if (BASE_CURRENCY.equals(creditCurrency)) {
                dealRate = CURRENCY_RATES.getOrDefault(debitCurrency, Map.of("buyRate", 1.0)).get("buyRate");
            } 
            else {
                Double debitBuyRate = CURRENCY_RATES.getOrDefault(debitCurrency, Map.of("buyRate", 1.0)).get("buyRate");
                Double creditSellRate = CURRENCY_RATES.getOrDefault(creditCurrency, Map.of("sellRate", 1.0)).get("sellRate");
                dealRate = debitBuyRate / creditSellRate;
            }

            teller.setDealRate(dealRate);
        }

        Double amountLocalDebit = teller.getDebitAmountLocal();
        Double amountForeignDebit = teller.getDebitAmountForeign();
        Double amountLocalCredit = teller.getCreditAmountLocal();
        Double amountForeignCredit = teller.getCreditAmountForeign();

        // SCENARIO 1: Debit is Base Currency (YER)
        if (BASE_CURRENCY.equals(debitCurrency)) {
            if (amountLocalDebit != null) {
                amountForeignCredit = amountLocalDebit / dealRate;
                teller.setCreditAmountForeign(amountForeignCredit);
                teller.setCreditAmountLocal(amountForeignCredit * creditMidRate);
            } 
            else if (amountForeignCredit != null) {
                amountLocalDebit = amountForeignCredit * dealRate;
                teller.setDebitAmountLocal(amountLocalDebit);
                teller.setCreditAmountLocal(amountForeignCredit * creditMidRate);
            }
        }

        // SCENARIO 2: Credit is Base Currency (YER)
        else if (BASE_CURRENCY.equals(creditCurrency)) {
            if (amountForeignDebit != null) {
                amountLocalCredit = amountForeignDebit * dealRate;
                teller.setCreditAmountLocal(amountLocalCredit);
                teller.setDebitAmountLocal(amountForeignDebit * debitMidRate);
            } 
            else if (amountLocalCredit != null) {
                amountForeignDebit = amountLocalCredit / dealRate;
                teller.setDebitAmountForeign(amountForeignDebit);
                teller.setDebitAmountLocal(amountForeignDebit * debitMidRate);
            }
        }

        // SCENARIO 3: Both Sides are Foreign Currencies
        else {
            if (amountForeignDebit != null) {
                amountForeignCredit = amountForeignDebit * dealRate;
                teller.setCreditAmountForeign(amountForeignCredit);
                teller.setDebitAmountLocal(amountForeignDebit * debitMidRate);
                teller.setCreditAmountLocal(amountForeignCredit * creditMidRate);
            } 
            else if (amountForeignCredit != null) {
                amountForeignDebit = amountForeignCredit / dealRate;
                teller.setDebitAmountForeign(amountForeignDebit);
                teller.setDebitAmountLocal(amountForeignDebit * debitMidRate);
                teller.setCreditAmountLocal(amountForeignCredit * creditMidRate);
            }
        }

        // SCENARIO 4: Same Currency (Either Base or Same Foreign Currency)
        if (debitCurrency.equals(creditCurrency)) {

            logger.info("Both currencies are the same: " + debitCurrency);

            // If both are base currency (YER)
            if (BASE_CURRENCY.equals(debitCurrency)) {
                if (amountLocalDebit != null && amountLocalCredit == null) {
                    teller.setCreditAmountLocal(amountLocalDebit);
                } 
                else if (amountLocalCredit != null && amountLocalDebit == null) {
                    teller.setDebitAmountLocal(amountLocalCredit);
                }
            }

            // If both are the same foreign currency
            else {
                if (amountForeignDebit != null && amountForeignCredit == null) {
                    teller.setCreditAmountForeign(amountForeignDebit);
                    teller.setDebitAmountLocal(amountForeignDebit * debitMidRate);
                    teller.setCreditAmountLocal(amountForeignDebit * creditMidRate);
                } 
                else if (amountForeignCredit != null && amountForeignDebit == null) {
                    teller.setDebitAmountForeign(amountForeignCredit);
                    teller.setDebitAmountLocal(amountForeignCredit * debitMidRate);
                    teller.setCreditAmountLocal(amountForeignCredit * creditMidRate);
                } 
                else if (amountForeignDebit != null && amountForeignCredit != null) {
                    if (!amountForeignDebit.equals(amountForeignCredit)) {
                        teller.setCreditAmountForeign(amountForeignDebit);
                    }

                    teller.setDebitAmountLocal(amountForeignDebit * debitMidRate);
                    teller.setCreditAmountLocal(amountForeignCredit * creditMidRate);
                }
            }
        }
    }

    private double calculateDealRate(String debitCurrency, String creditCurrency) {
        if (BASE_CURRENCY.equals(debitCurrency)) {
            return getSellRate(creditCurrency);
        } else if (BASE_CURRENCY.equals(creditCurrency)) {
            return getBuyRate(debitCurrency);
        } else {
            return getBuyRate(debitCurrency) / getSellRate(creditCurrency);
        }
    }

    private double getSellRate(String currency) {
        return CURRENCY_RATES.getOrDefault(currency, Map.of("sellRate", 1.0)).get("sellRate");
    }

    private double getBuyRate(String currency) {
        return CURRENCY_RATES.getOrDefault(currency, Map.of("buyRate", 1.0)).get("buyRate");
    }

    private double getMidRate(String currency) {
        return CURRENCY_RATES.getOrDefault(currency, Map.of("midRate", 1.0)).get("midRate");
    }
    
    private String getTillIdAndSetTellerIds(Teller teller) {
        Object user = sa.getAttribute("user");
        if (user instanceof JsonNode jsonUser) {
            JsonNode rowIdNode = jsonUser.get("user");
            if (rowIdNode != null && !rowIdNode.isNull()) {
                String userId = rowIdNode.asText();
                Map<String, Object> criteria = Map.of("user", Map.of("EQ", userId));
                ArrayNode result = da.getRecordsByCriteria("userTill", criteria);

                if (result != null && result.size() > 0) {
                    String tillId = result.get(0).get("ROW_ID").asText();

                    // Set debitTellerId and creditTellerId
                    teller.setDebitTellerId(tillId);
                    teller.setCreditTellerId(tillId);

                    logger.info("Set debitTellerId and creditTellerId to {}", tillId);
                    return tillId;
                }
            }
        }
        logger.warn("User till not found. debitTellerId and creditTellerId not set.");
        return null;
    }

}
