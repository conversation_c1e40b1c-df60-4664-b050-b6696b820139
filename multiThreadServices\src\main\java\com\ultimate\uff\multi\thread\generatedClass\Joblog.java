package com.ultimate.uff.multi.thread.generatedClass;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Joblog extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String jobId;
    private final FieldValue<String> jobIdVal = new FieldValue<>(null);
    private String logTime;
    private final FieldValue<String> logTimeVal = new FieldValue<>(null);
    private String level;
    private final FieldValue<String> levelVal = new FieldValue<>(null);
    private String message;
    private final FieldValue<String> messageVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private List<String> recordAuthorizer = new ArrayList<>();
    private final FieldValue<List<String>> recordAuthorizerVal = new FieldValue<>(new ArrayList<>());
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public Joblog() {}

    public Joblog(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object jobIdObj = recordDetail.get("jobId");
        if (jobIdObj != null) {
            this.setJobId(jobIdObj.toString());
        }
        Object logTimeObj = recordDetail.get("logTime");
        if (logTimeObj != null) {
            this.setLogTime(logTimeObj.toString());
        }
        Object levelObj = recordDetail.get("level");
        if (levelObj != null) {
            this.setLevel(levelObj.toString());
        }
        Object messageObj = recordDetail.get("message");
        if (messageObj != null) {
            this.setMessage(messageObj.toString());
        }
        Object IDObj = recordDetail.get("ID");
        if (IDObj != null) {
            this.setID(IDObj.toString());
        }
        Object recordStatusObj = recordDetail.get("recordStatus");
        if (recordStatusObj != null) {
            this.setRecordStatus(recordStatusObj.toString());
        }
        Object recordInputterObj = recordDetail.get("recordInputter");
        if (recordInputterObj != null) {
            this.setRecordInputter(recordInputterObj.toString());
        }
        Object listObj = recordDetail.get("recordAuthorizer");
        if (listObj instanceof List<?>) {
            this.setRecordAuthorizer((List<String>) listObj);
        }
        Object recordCountObj = recordDetail.get("recordCount");
        if (recordCountObj != null) {
            this.setRecordCount(recordCountObj.toString());
        }
        Object dateTimeObj = recordDetail.get("dateTime");
        if (dateTimeObj != null) {
            this.setDateTime(dateTimeObj.toString());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (jobId != null) map.put("jobId", jobId);
        if (logTime != null) map.put("logTime", logTime);
        if (level != null) map.put("level", level);
        if (message != null) map.put("message", message);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "jobId", "logTime", "level", "message", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
        this.jobIdVal.setValue(jobId);
    }
    public String getJobId() { return this.jobId; }
    public FieldValue<String> jobIdVal() { return jobIdVal; }
    public void setLogTime(String logTime) {
        this.logTime = logTime;
        this.logTimeVal.setValue(logTime);
    }
    public String getLogTime() { return this.logTime; }
    public FieldValue<String> logTimeVal() { return logTimeVal; }
    public void setLevel(String level) {
        this.level = level;
        this.levelVal.setValue(level);
    }
    public String getLevel() { return this.level; }
    public FieldValue<String> levelVal() { return levelVal; }
    public void setMessage(String message) {
        this.message = message;
        this.messageVal.setValue(message);
    }
    public String getMessage() { return this.message; }
    public FieldValue<String> messageVal() { return messageVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(List<String> recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public List<String> getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<List<String>> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}
