package com.ultimate.uff.validation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.tenant.RecordContext;
import com.ultimate.uff.uql.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Service responsible for inserting, authorizing, rejecting, deleting, and retrieving records.
 * Includes metadata management, history handling, and record locking.
 */
@Service
public class RecordInsertionService {

    private static final Logger logger = LoggerFactory.getLogger(RecordInsertionService.class);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final int AUTHOR_COUNT = 1;

    private final TenantAwareDatabaseService databaseService;
    private final DatabaseService mainDatabaseService;
    private final RecordLockService recordLockService;
    private final ObjectMapper objectMapper;
    @Autowired private IdGenerationService idGenerationService;

    public RecordInsertionService(TenantAwareDatabaseService databaseService,
                                  RecordLockService recordLockService, DatabaseService mainDatabaseService
,                                 ObjectMapper objectMapper) {
        this.databaseService = databaseService;
        this.recordLockService = recordLockService;
        this.mainDatabaseService=mainDatabaseService;
        this.objectMapper = objectMapper;
    }

    /**
     * Inserts a record into the $UNAU table or directly into the main table
     * based on the authorizeNumber. If authorizeNumber == 0, the record is considered
     * auto-authorized and inserted directly into the main table.
     *
     * @param tableName       Table name without suffix
     * @param recordDetail    Map of the record data
     * @param userId          ID of the user performing the action
     * @param authorizeNumber Number of required authorizers (0 = insert directly to live)
     * @return GlobalResponse indicating success or failure
     */
    public GlobalResponse insertRecord(String tableName, Map<String, Object> recordDetail, String userId, int authorizeNumber) {
        String id = (String) recordDetail.get("ID");
        if (id == null || id.isEmpty()) {
            return new GlobalResponse(false, "ID field is mandatory");
        }

        String currCom = RecordContext.getCurrentCompany();
        String primCom = RecordContext.getPrimaryCompany();
        boolean isTenantBased = RecordContext.isTenantBased();
        logger.info("Locking record [{}] in table [{}] by user [{}] for company [{}] (primary [{}])",
                    id, tableName, userId, currCom, primCom);

        recordLockService.lockRecord(tableName, id, userId);

        try {
            recordDetail.put("dateTime", FORMATTER.format(LocalDateTime.now()));
            recordDetail.put("recordInputter", userId);
            recordDetail.put("recordStatus", "UNAU");
            recordDetail.remove("recordAuthorizer");
            databaseService.insertRecord(primCom,tableName + "$UNAU", id, objectMapper.writeValueAsString(recordDetail));
            logger.info("Inserted record [{}] into [{}$UNAU] (authorization required)", id, tableName);

            return new GlobalResponse(true, "Record inserted to unauthorized table and awaiting authorization");

        } catch (Exception e) {
            logger.error("Error inserting record [{}]: {}", id, e.getMessage(), e);
            return new GlobalResponse(false, "Error inserting record: " + e.getMessage());
        } finally {
            recordLockService.unlockRecord(tableName, id);
            logger.info("Unlocked record [{}] after insertion", id);
        }
    }


    /**
     * Authorizes a record by adding the user to authorizer list and moving it to the live table if complete.
     *
     * @param tableName Table name
     * @param id        Record ID
     * @param userId    Authorizing user ID
     * @return GlobalResponse indicating success or failure
     */
	    public GlobalResponse authorizeRecord(String tableName, String id, String userId,int authorizeNumber) {
	        if (id == null || id.isEmpty()) return new GlobalResponse(false, "ID cannot be null or empty");
	
	        String currCom = RecordContext.getCurrentCompany();
	        String primCom = RecordContext.getPrimaryCompany();
	        boolean isTenantBased = RecordContext.isTenantBased();
	
	        recordLockService.lockRecord(tableName, id, userId);
	        logger.info("Authorizing record [{}] in table [{}] by [{}]", id, tableName, userId);
	
	        try {
	            Map<String, Object> record = databaseService.getRecordById(primCom,tableName + "$UNAU", id);
	            if (record == null) return new GlobalResponse(false, "Record not found");
	
	            String status = (String) record.get("recordStatus");
	            if ("REJ".equalsIgnoreCase(status)) {
	                return new GlobalResponse(false, "Cannot authorize a rejected record");
	            }
	
	            Object authorizerRaw = record.get("recordAuthorizer");
	            List<String> authorizers = authorizerRaw instanceof List
	                    ? new ArrayList<>((List<String>) authorizerRaw)
	                    : new ArrayList<>();

	            if (authorizeNumber == 0) {
	                // Set authorizer at index 0, initialize list with one element
	                if (authorizers.isEmpty()) {
	                    authorizers.add(userId);
	                } else {
	                    authorizers.set(0, userId);
	                }
	            } else {
	                // Ensure list has the correct size
	                while (authorizers.size() < authorizeNumber) {
	                    authorizers.add("");
	                }

	                // Determine index to set
	                int indexToSet = 0;
	                if (authorizeNumber > 1) {
	                    for (int i = 0; i < authorizeNumber; i++) {
	                        if (authorizers.get(i) == null || authorizers.get(i).isBlank()) {
	                            indexToSet = i;
	                            break;
	                        }
	                    }
	                }
	                authorizers.set(indexToSet, userId);
	            }
	            record.put("recordAuthorizer", authorizers);
	            record.put("dateTime", FORMATTER.format(LocalDateTime.now()));
	
	            int currentCount = (int) authorizers.stream().filter(s -> s != null && !s.isBlank()).count();
	            if (currentCount < authorizeNumber) {
	                record.put("recordStatus", "UNA" + currentCount);
	                databaseService.insertRecord(primCom, tableName + "$UNAU", id, objectMapper.writeValueAsString(record));
	                return new GlobalResponse(true, "Partial authorization done: " + currentCount + "/" + authorizeNumber);
	            }
	
	            Object recordCountObj = record.get("recordCount");
	            int recordCount = getRecordCount(recordCountObj);
	
	            record.put("recordCount", recordCount + 1);
	
	            Map<String, Object> oldRecord = databaseService.getRecordById(primCom,tableName, id);
	            if (oldRecord != null) {
	                databaseService.insertRecord(primCom,tableName + "$HIST", id + "_"+recordCount, objectMapper.writeValueAsString(oldRecord));
	            }
	
	            record.remove("recordStatus");
	            databaseService.insertRecord(primCom,tableName, id, objectMapper.writeValueAsString(record));
	            databaseService.deleteRecordById(primCom,tableName + "$UNAU", id);
	
	            return new GlobalResponse(true, "Record fully authorized and moved to main table");
	
	        } catch (Exception e) {
	            logger.error("Authorization failed for [{}]: {}", id, e.getMessage(), e);
	            return new GlobalResponse(false, "Authorization error: " + e.getMessage());
	        } finally {
	            recordLockService.unlockRecord(tableName, id);
	        }
	    }

    /**
     * Deletes a record from the $UNAU table.
     *
     * @param tableName Table name
     * @param id        Record ID
     * @param userId    Deleting user ID
     * @return GlobalResponse
     */
    public GlobalResponse deleteRecord(String tableName, String id, String userId,int authorizeNumber) {
        if (id == null || id.isEmpty()) return new GlobalResponse(false, "ID cannot be null or empty");
        String primCom = RecordContext.getPrimaryCompany();

        if (recordLockService.isRecordLockedByAnotherUser(tableName, id, userId)) {
            return new GlobalResponse(false, "Record is locked by another user");
        }

        recordLockService.lockRecord(tableName, id, userId);
        try {
            Map<String, Object> record = databaseService.getRecordById(primCom,tableName + "$UNAU", id);
            if (record == null) return new GlobalResponse(false, "Record not found");

            databaseService.deleteRecordById(primCom,tableName + "$UNAU", id);
            return new GlobalResponse(true, "Record deleted successfully");

        } catch (Exception e) {
            logger.error("Delete failed: {}", e.getMessage(), e);
            return new GlobalResponse(false, "Delete error: " + e.getMessage());
        } finally {
            recordLockService.unlockRecord(tableName, id);
        }
    }

    /**
     * Rejects a record in the $UNAU table.
     *
     * @param tableName Table name
     * @param id        Record ID
     * @param userId    Rejecting user ID
     * @return GlobalResponse
     */
    public GlobalResponse rejectRecord(String tableName, String id, String userId,int authorizeNumber) {
        if (id == null || id.isEmpty()) return new GlobalResponse(false, "ID cannot be null or empty");
        String primCom = RecordContext.getPrimaryCompany();

        if (recordLockService.isRecordLockedByAnotherUser(tableName, id, userId)) {
            return new GlobalResponse(false, "Record is locked by another user");
        }

        recordLockService.lockRecord(tableName, id, userId);
        try {
            Map<String, Object> record = databaseService.getRecordById(primCom,tableName + "$UNAU", id);
            if (record == null) return new GlobalResponse(false, "Record not found");

            record.put("recordStatus", "REJ");
            record.put("recordRejecter", userId);
            record.put("dateTime", FORMATTER.format(LocalDateTime.now()));

            databaseService.insertRecord(primCom,tableName + "$UNAU", id, objectMapper.writeValueAsString(record));
            return new GlobalResponse(true, "Record rejected successfully");

        } catch (Exception e) {
            logger.error("Rejection failed: {}", e.getMessage(), e);
            return new GlobalResponse(false, "Rejection error: " + e.getMessage());
        } finally {
            recordLockService.unlockRecord(tableName, id);
        }
    }

    /**
     * Retrieves metadata for a form, with global fields merged. Cached for performance.
     *
     * @param tableName Table name
     * @return GlobalResponse with metadata
     */
    //@Cacheable(value = "tableMetadata", key = "#tableName")
    public GlobalResponse getTableMetadata(String tableName) {
        try {
            Map<String, Object> metadata = mainDatabaseService.getRecordById("formDefinition", tableName);
            Map<String, Object> globalMetadata = mainDatabaseService.getRecordById("globalFields", "global");

            if (metadata != null && globalMetadata != null) {
                List<Map<String, Object>> fields = (List<Map<String, Object>>) metadata.get("fieldName");
                List<Map<String, Object>> globalFields = (List<Map<String, Object>>) globalMetadata.get("fieldName");

                if (fields != null && globalFields != null) {
                    fields.addAll(globalFields);
                    metadata.put("fieldName", fields);
                }

                return new GlobalResponse(true, "Metadata retrieved successfully", metadata);
            } else {
                return new GlobalResponse(false, "Metadata not found for the given table");
            }

        } catch (Exception e) {
            logger.error("Metadata fetch failed: {}", e.getMessage(), e);
            return new GlobalResponse(false, "Error fetching metadata: " + e.getMessage());
        }
    }

    /**
     * Retrieves a record from either $UNAU or the main table.
     *
     * @param tableName Table name
     * @param id        Record ID
     * @return GlobalResponse with the record
     */
    public GlobalResponse getRecord(String tableName, String id) {
        String primCom = RecordContext.getPrimaryCompany();

        try {
            Map<String, Object> record = databaseService.getRecordById(primCom,tableName + "$UNAU", id);
            if (record != null) return new GlobalResponse(true, "Record retrieved successfully", record);

            Map<String, Object> live = databaseService.getRecordById(primCom,tableName, id);
            return new GlobalResponse(true, "Record retrieved from main table", live);

        } catch (Exception e) {
            logger.error("Failed to retrieve record [{}]: {}", id, e.getMessage(), e);
            return new GlobalResponse(false, "Error retrieving record: " + e.getMessage());
        }
    }
    
    public GlobalResponse generateNextId(String tableName) {
        try {
            Map<String, Object> generatorConfig = mainDatabaseService.getRecordById("idGenerator", tableName);
            if (generatorConfig == null) {
                return new GlobalResponse(false, "No ID generator config found for: " + tableName);
            }

            idGenerationService.generateAndSetNextId(generatorConfig, "idGenerator");
            String nextId = (String) generatorConfig.get("nextId");

            // You may want to persist this updated config if needed:
            mainDatabaseService.insertRecord("idGenerator", tableName, objectMapper.writeValueAsString(generatorConfig));

            return new GlobalResponse(true, "ID generated successfully", Map.of("nextId", nextId));

        } catch (Exception e) {
            logger.error("ID generation failed for [{}]: {}", tableName, e.getMessage(), e);
            return new GlobalResponse(false, "Failed to generate ID: " + e.getMessage());
        }
    }

    
    /**
     * Safely parses the recordCount field, supporting int or string inputs.
     */
    private int getRecordCount(Object recordCountObj) {
        if (recordCountObj instanceof Integer) return (Integer) recordCountObj;
        if (recordCountObj instanceof String str) {
            try {
                return str.isEmpty() ? 0 : Integer.parseInt(str);
            } catch (NumberFormatException e) {
                logger.warn("Failed to parse recordCount: {}", str);
            }
        }
        return 0;
    }
    
  

}
