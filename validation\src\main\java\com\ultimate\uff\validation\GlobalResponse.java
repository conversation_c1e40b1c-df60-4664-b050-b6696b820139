package com.ultimate.uff.validation;

import java.util.Map;

/**
 * Represents a standard response structure used across the application
 * to convey the result of operations, including success status, message,
 * and optional data payload.
 */
public class GlobalResponse {

    /** Indicates whether the operation was successful */
    private boolean success;

    /** Descriptive message related to the operation result */
    private String message;

    /** Optional map to hold extra data (e.g., IDs, objects, info) */
    private Map<String, Object> data;

    /**
     * Constructs a response with success flag and message only.
     * Useful when no additional data is required.
     *
     * @param success true if the operation was successful
     * @param message a human-readable message
     */
    public GlobalResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
        this.data = null;
    }

    /**
     * Constructs a response with success flag, message, and extra data.
     *
     * @param success true if the operation was successful
     * @param message a descriptive message
     * @param data additional payload (can be null)
     */
    public GlobalResponse(boolean success, String message, Map<String, Object> data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }

    /** @return whether the operation was successful */
    public boolean isSuccess() {
        return success;
    }

    /** @return operation message */
    public String getMessage() {
        return message;
    }

    /** @return additional response data (if any) */
    public Map<String, Object> getData() {
        return data;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }
}
