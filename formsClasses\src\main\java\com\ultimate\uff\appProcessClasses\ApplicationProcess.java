package com.ultimate.uff.appProcessClasses;


import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ApplicationProcess {

    private String id;
    private String tableName;
    private List<String> recordValidation; // Changed to List for multi-value
    private List<String> nauSave; // Changed to List for multi-value
    private List<String> idValidation; // Changed to List for multi-value
    private List<String> defultation; // Changed to List for multi-value
    private List<String> afterAuth; // Changed to List for multi-value
    private int recordCount;
    private String inputter;
    private String authorizer;
    private String inDate;
    private String inTime;

    // Constructor that initializes ApplicationProcess fields from the recordDetail map
    public ApplicationProcess(Map<String, Object> recordDetail) {
        this.id = (String) recordDetail.get("ID");
        this.tableName = (String) recordDetail.get("table_name");

        // Initialize multi-value fields as lists
        this.recordValidation = getFieldList(recordDetail, "recordValidation");
        this.nauSave = getFieldList(recordDetail, "nauSave");
        this.idValidation = getFieldList(recordDetail, "idValidation");
        this.defultation = getFieldList(recordDetail, "defultation");
        this.afterAuth = getFieldList(recordDetail, "afterAuth");

        // Handle record_count safely
        this.recordCount = (Integer) recordDetail.getOrDefault("record_count", 0);

        this.inputter = (String) recordDetail.get("Inputter");
        this.authorizer = (String) recordDetail.get("Authorizer");
        this.inDate = (String) recordDetail.get("IN_DATE");
        this.inTime = (String) recordDetail.get("IN_TIME");
    }

    // Helper method to get field value as a List safely
    private List<String> getFieldList(Map<String, Object> recordDetail, String fieldName) {
        List<String> list = new ArrayList<>();
        Object value = recordDetail.get(fieldName);
        if (value instanceof List) {
            for (Object obj : (List<?>) value) {
                if (obj instanceof String) {
                    list.add((String) obj);
                }
            }
        }
        return list;
    }

    // Method to update the recordDetail map and return the updated map
    public Map<String, Object> updateRecordDetail(Map<String, Object> recordDetail) {
        recordDetail.put("ID", this.id);
        recordDetail.put("table_name", this.tableName);
        recordDetail.put("recordValidation", this.recordValidation);
        recordDetail.put("nauSave", this.nauSave);
        recordDetail.put("idValidation", this.idValidation);
        recordDetail.put("defultation", this.defultation);
        recordDetail.put("afterAuth", this.afterAuth);
        recordDetail.put("record_count", this.recordCount);
        recordDetail.put("Inputter", this.inputter);
        recordDetail.put("Authorizer", this.authorizer);
        recordDetail.put("IN_DATE", this.inDate);
        recordDetail.put("IN_TIME", this.inTime);

        return recordDetail;
    }

    // Getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getRecordValidation() {
        return recordValidation;
    }

    public void setRecordValidation(List<String> recordValidation) {
        this.recordValidation = recordValidation;
    }

    public List<String> getNauSave() {
        return nauSave;
    }

    public void setNauSave(List<String> nauSave) {
        this.nauSave = nauSave;
    }

    public List<String> getIdValidation() {
        return idValidation;
    }

    public void setIdValidation(List<String> idValidation) {
        this.idValidation = idValidation;
    }

    public List<String> getDefultation() {
        return defultation;
    }

    public void setDefultation(List<String> defultation) {
        this.defultation = defultation;
    }

    public List<String> getAfterAuth() {
        return afterAuth;
    }

    public void setAfterAuth(List<String> afterAuth) {
        this.afterAuth = afterAuth;
    }

    public int getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
    }

    public String getInputter() {
        return inputter;
    }

    public void setInputter(String inputter) {
        this.inputter = inputter;
    }

    public String getAuthorizer() {
        return authorizer;
    }

    public void setAuthorizer(String authorizer) {
        this.authorizer = authorizer;
    }

    public String getInDate() {
        return inDate;
    }

    public void setInDate(String inDate) {
        this.inDate = inDate;
    }

    public String getInTime() {
        return inTime;
    }

    public void setInTime(String inTime) {
        this.inTime = inTime;
    }
}

