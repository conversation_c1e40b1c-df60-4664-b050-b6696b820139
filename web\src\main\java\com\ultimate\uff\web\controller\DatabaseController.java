package com.ultimate.uff.web.controller;

import com.ultimate.uff.uql.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/database")
public class DatabaseController {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseController.class);

    private final DatabaseService databaseService;

    @Autowired
    public DatabaseController(DatabaseService databaseService) {
        this.databaseService = databaseService;
    }

    /**
     * Endpoint to create a view in the database based on the specified table's metadata.
     *
     * @param tableName the name of the table whose metadata will be used to create the view
     * @return ResponseEntity with the result of the operation
     */
    @PostMapping("/createView")
    public ResponseEntity<String> createView(@RequestParam String tableName) {
        try {
            databaseService.createView(tableName);
            String successMessage = "View created successfully for table: " + tableName;
            logger.info(successMessage);
            return ResponseEntity.ok(successMessage);
        } catch (Exception e) {
            // Log the error and return its message directly in the response
            logger.error("Failed to create view for table: {}", tableName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
}
