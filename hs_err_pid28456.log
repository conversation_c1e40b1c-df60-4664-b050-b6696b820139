#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1221776 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=28456, tid=22048
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4e7d2523ade161ac9ce8aa01dc802f4d-sock

Host: Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz, 8 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.1)
Time: Tue May 27 14:25:55 2025 Egypt Daylight Time elapsed time: 458.819004 seconds (0d 0h 7m 38s)

---------------  T H R E A D  ---------------

Current thread (0x000002bdffc66510):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=22048, stack(0x000000c45ed00000,0x000000c45ee00000) (1024K)]


Current CompileTask:
C2:458819 21438       4       org.eclipse.jdt.internal.compiler.lookup.AnnotatableTypeSystem::getParameterizedType (123 bytes)

Stack: [0x000000c45ed00000,0x000000c45ee00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b68f2]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x43340]
C  [KERNEL32.DLL+0x31fd7]
C  [ntdll.dll+0x6d7d0]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002bdd7a3bfe0, length=59, elements={
0x000002bdfa89ad80, 0x000002bdffc4de60, 0x000002bdffc513a0, 0x000002bdffc542a0,
0x000002bdffc54ef0, 0x000002bdffc5a110, 0x000002bdffc5e190, 0x000002bdffc66510,
0x000002bdffc6bb60, 0x000002bdfa908db0, 0x000002bdd19627b0, 0x000002bdd6282d90,
0x000002bdd62d1090, 0x000002bdd62a9d70, 0x000002bdd62aa400, 0x000002bdd62aaa90,
0x000002bdd62ab120, 0x000002bdd62ac4d0, 0x000002bdd62a96e0, 0x000002bdd7042d10,
0x000002bdd70433a0, 0x000002bdd70440c0, 0x000002bdd7045470, 0x000002bdd7041ff0,
0x000002bdd7045b00, 0x000002bdd7046820, 0x000002bdd7046190, 0x000002bdd7046eb0,
0x000002bdd7043a30, 0x000002bdd7047bd0, 0x000002bdd7047540, 0x000002bdd7048f80,
0x000002bdd7049610, 0x000002bdd7048260, 0x000002bdd9a54990, 0x000002bdd9a59ee0,
0x000002bdd9a591c0, 0x000002bdd9a59850, 0x000002bdd62ab7b0, 0x000002bdd62abe40,
0x000002bddb76cfc0, 0x000002bddb76e370, 0x000002bddb76b580, 0x000002bddb76aef0,
0x000002bddb770440, 0x000002bddb76f720, 0x000002bddb76ea00, 0x000002bddb76fdb0,
0x000002bddb771e80, 0x000002bddb770ad0, 0x000002bddb771160, 0x000002bdd9f0ff30,
0x000002bdd9f12000, 0x000002bdd9f0f210, 0x000002bdd9f105c0, 0x000002bdd9f10c50,
0x000002bdd9f112e0, 0x000002bdd9f11970, 0x000002bdd6fc14b0
}

Java Threads: ( => current thread )
  0x000002bdfa89ad80 JavaThread "main"                              [_thread_blocked, id=25640, stack(0x000000c45e300000,0x000000c45e400000) (1024K)]
  0x000002bdffc4de60 JavaThread "Reference Handler"          daemon [_thread_blocked, id=13916, stack(0x000000c45e700000,0x000000c45e800000) (1024K)]
  0x000002bdffc513a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=27140, stack(0x000000c45e800000,0x000000c45e900000) (1024K)]
  0x000002bdffc542a0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=25460, stack(0x000000c45e900000,0x000000c45ea00000) (1024K)]
  0x000002bdffc54ef0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=20716, stack(0x000000c45ea00000,0x000000c45eb00000) (1024K)]
  0x000002bdffc5a110 JavaThread "Service Thread"             daemon [_thread_blocked, id=29772, stack(0x000000c45eb00000,0x000000c45ec00000) (1024K)]
  0x000002bdffc5e190 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=25096, stack(0x000000c45ec00000,0x000000c45ed00000) (1024K)]
=>0x000002bdffc66510 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=22048, stack(0x000000c45ed00000,0x000000c45ee00000) (1024K)]
  0x000002bdffc6bb60 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=25824, stack(0x000000c45ee00000,0x000000c45ef00000) (1024K)]
  0x000002bdfa908db0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=31776, stack(0x000000c45ef00000,0x000000c45f000000) (1024K)]
  0x000002bdd19627b0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=22120, stack(0x000000c45f000000,0x000000c45f100000) (1024K)]
  0x000002bdd6282d90 JavaThread "Active Thread: Equinox Container: 4d027562-cee6-43fc-a2a5-7ecb4f549587"        [_thread_blocked, id=26504, stack(0x000000c45fd00000,0x000000c45fe00000) (1024K)]
  0x000002bdd62d1090 JavaThread "Framework Event Dispatcher: Equinox Container: 4d027562-cee6-43fc-a2a5-7ecb4f549587" daemon [_thread_blocked, id=23296, stack(0x000000c45ff00000,0x000000c460000000) (1024K)]
  0x000002bdd62a9d70 JavaThread "Start Level: Equinox Container: 4d027562-cee6-43fc-a2a5-7ecb4f549587" daemon [_thread_blocked, id=25036, stack(0x000000c460000000,0x000000c460100000) (1024K)]
  0x000002bdd62aa400 JavaThread "Refresh Thread: Equinox Container: 4d027562-cee6-43fc-a2a5-7ecb4f549587" daemon [_thread_blocked, id=24896, stack(0x000000c45fe00000,0x000000c45ff00000) (1024K)]
  0x000002bdd62aaa90 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=22240, stack(0x000000c460500000,0x000000c460600000) (1024K)]
  0x000002bdd62ab120 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=30448, stack(0x000000c460200000,0x000000c460300000) (1024K)]
  0x000002bdd62ac4d0 JavaThread "Worker-JM"                         [_thread_blocked, id=22252, stack(0x000000c460700000,0x000000c460800000) (1024K)]
  0x000002bdd62a96e0 JavaThread "Worker-0"                          [_thread_blocked, id=31860, stack(0x000000c460800000,0x000000c460900000) (1024K)]
  0x000002bdd7042d10 JavaThread "Java indexing"              daemon [_thread_blocked, id=24496, stack(0x000000c460b00000,0x000000c460c00000) (1024K)]
  0x000002bdd70433a0 JavaThread "Worker-3"                          [_thread_blocked, id=28576, stack(0x000000c460c00000,0x000000c460d00000) (1024K)]
  0x000002bdd70440c0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=24996, stack(0x000000c460d00000,0x000000c460e00000) (1024K)]
  0x000002bdd7045470 JavaThread "Worker-4"                          [_thread_blocked, id=12916, stack(0x000000c461000000,0x000000c461100000) (1024K)]
  0x000002bdd7041ff0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=23656, stack(0x000000c461200000,0x000000c461300000) (1024K)]
  0x000002bdd7045b00 JavaThread "Thread-3"                   daemon [_thread_in_native, id=11568, stack(0x000000c461300000,0x000000c461400000) (1024K)]
  0x000002bdd7046820 JavaThread "Thread-4"                   daemon [_thread_in_native, id=17100, stack(0x000000c461400000,0x000000c461500000) (1024K)]
  0x000002bdd7046190 JavaThread "Thread-5"                   daemon [_thread_in_native, id=21720, stack(0x000000c461500000,0x000000c461600000) (1024K)]
  0x000002bdd7046eb0 JavaThread "Thread-6"                   daemon [_thread_in_native, id=24380, stack(0x000000c461600000,0x000000c461700000) (1024K)]
  0x000002bdd7043a30 JavaThread "Thread-7"                   daemon [_thread_in_native, id=23316, stack(0x000000c461700000,0x000000c461800000) (1024K)]
  0x000002bdd7047bd0 JavaThread "Thread-8"                   daemon [_thread_in_native, id=8320, stack(0x000000c461800000,0x000000c461900000) (1024K)]
  0x000002bdd7047540 JavaThread "Thread-9"                   daemon [_thread_in_native, id=17080, stack(0x000000c461900000,0x000000c461a00000) (1024K)]
  0x000002bdd7048f80 JavaThread "Thread-10"                  daemon [_thread_in_native, id=27260, stack(0x000000c461a00000,0x000000c461b00000) (1024K)]
  0x000002bdd7049610 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=1160, stack(0x000000c461b00000,0x000000c461c00000) (1024K)]
  0x000002bdd7048260 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=22440, stack(0x000000c461c00000,0x000000c461d00000) (1024K)]
  0x000002bdd9a54990 JavaThread "Worker-6: Java indexing... "        [_thread_blocked, id=15672, stack(0x000000c45e000000,0x000000c45e100000) (1024K)]
  0x000002bdd9a59ee0 JavaThread "Worker-7"                          [_thread_blocked, id=23888, stack(0x000000c45e100000,0x000000c45e200000) (1024K)]
  0x000002bdd9a591c0 JavaThread "Worker-8: Check JDK Index"         [_thread_blocked, id=25436, stack(0x000000c45f800000,0x000000c45f900000) (1024K)]
  0x000002bdd9a59850 JavaThread "Worker-9"                          [_thread_blocked, id=23632, stack(0x000000c45f700000,0x000000c45f800000) (1024K)]
  0x000002bdd62ab7b0 JavaThread "Worker-10"                         [_thread_blocked, id=33268, stack(0x000000c45fb00000,0x000000c45fc00000) (1024K)]
  0x000002bdd62abe40 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=18708, stack(0x000000c460400000,0x000000c460500000) (1024K)]
  0x000002bddb76cfc0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=15404, stack(0x000000c460900000,0x000000c460a00000) (1024K)]
  0x000002bddb76e370 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=22424, stack(0x000000c45fc00000,0x000000c45fd00000) (1024K)]
  0x000002bddb76b580 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=23476, stack(0x000000c460300000,0x000000c460400000) (1024K)]
  0x000002bddb76aef0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=33936, stack(0x000000c460a00000,0x000000c460b00000) (1024K)]
  0x000002bddb770440 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=25304, stack(0x000000c460e00000,0x000000c460f00000) (1024K)]
  0x000002bddb76f720 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=12028, stack(0x000000c460f00000,0x000000c461000000) (1024K)]
  0x000002bddb76ea00 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=19504, stack(0x000000c461e00000,0x000000c461f00000) (1024K)]
  0x000002bddb76fdb0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=20268, stack(0x000000c461f00000,0x000000c462000000) (1024K)]
  0x000002bddb771e80 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=22816, stack(0x000000c462000000,0x000000c462100000) (1024K)]
  0x000002bddb770ad0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=28056, stack(0x000000c462100000,0x000000c462200000) (1024K)]
  0x000002bddb771160 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=11896, stack(0x000000c462200000,0x000000c462300000) (1024K)]
  0x000002bdd9f0ff30 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=13220, stack(0x000000c462500000,0x000000c462600000) (1024K)]
  0x000002bdd9f12000 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=19888, stack(0x000000c462600000,0x000000c462700000) (1024K)]
  0x000002bdd9f0f210 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=19924, stack(0x000000c462700000,0x000000c462800000) (1024K)]
  0x000002bdd9f105c0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=23040, stack(0x000000c462800000,0x000000c462900000) (1024K)]
  0x000002bdd9f10c50 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=31468, stack(0x000000c462900000,0x000000c462a00000) (1024K)]
  0x000002bdd9f112e0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=19520, stack(0x000000c462a00000,0x000000c462b00000) (1024K)]
  0x000002bdd9f11970 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=34444, stack(0x000000c462b00000,0x000000c462c00000) (1024K)]
  0x000002bdd6fc14b0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=27028, stack(0x000000c45e200000,0x000000c45e300000) (1024K)]
Total: 59

Other Threads:
  0x000002bdffc2fba0 VMThread "VM Thread"                           [id=3508, stack(0x000000c45e600000,0x000000c45e700000) (1024K)]
  0x000002bdffb6ba00 WatcherThread "VM Periodic Task Thread"        [id=27448, stack(0x000000c45e500000,0x000000c45e600000) (1024K)]
  0x000002bdfa8b94c0 WorkerThread "GC Thread#0"                     [id=23392, stack(0x000000c45e400000,0x000000c45e500000) (1024K)]
  0x000002bdd65d1d70 WorkerThread "GC Thread#1"                     [id=12276, stack(0x000000c45f100000,0x000000c45f200000) (1024K)]
  0x000002bdd645bab0 WorkerThread "GC Thread#2"                     [id=18564, stack(0x000000c45f200000,0x000000c45f300000) (1024K)]
  0x000002bdd645be50 WorkerThread "GC Thread#3"                     [id=17360, stack(0x000000c45f300000,0x000000c45f400000) (1024K)]
  0x000002bdd645c1f0 WorkerThread "GC Thread#4"                     [id=15012, stack(0x000000c45f400000,0x000000c45f500000) (1024K)]
  0x000002bdd645c590 WorkerThread "GC Thread#5"                     [id=22516, stack(0x000000c45f500000,0x000000c45f600000) (1024K)]
  0x000002bdd631d8e0 WorkerThread "GC Thread#6"                     [id=21892, stack(0x000000c45f600000,0x000000c45f700000) (1024K)]
  0x000002bdd68d5b40 WorkerThread "GC Thread#7"                     [id=20012, stack(0x000000c460100000,0x000000c460200000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  458856 21438       4       org.eclipse.jdt.internal.compiler.lookup.AnnotatableTypeSystem::getParameterizedType (123 bytes)
C2 CompilerThread1  458856 21508       4       org.eclipse.jdt.internal.compiler.parser.Scanner::internalScanIdentifierOrKeyword (5041 bytes)
Total: 2

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd98c5e308] Threads_lock - owner thread: 0x000002bdffc2fba0
[0x00007ffd98c5e408] Heap_lock - owner thread: 0x000002bdd7042d10

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002bd8f000000-0x000002bd8fba0000-0x000002bd8fba0000), size 12189696, SharedBaseAddress: 0x000002bd8f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002bd90000000-0x000002bdd0000000, reserved size: 1073741824
Narrow klass base: 0x000002bd8f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 8066M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 3584K, used 1008K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 1024K, 98% used [0x00000000eae80000,0x00000000eaf7c010,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 294912K, used 294670K [0x00000000c0000000, 0x00000000d2000000, 0x00000000eab00000)
  object space 294912K, 99% used [0x00000000c0000000,0x00000000d1fc3ac0,0x00000000d2000000)
 Metaspace       used 79706K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K

Card table byte_map: [0x000002bdfa240000,0x000002bdfa450000] _byte_map_base: 0x000002bdf9c40000

Marking Bits: (ParMarkBitMap*) 0x00007ffd98c631f0
 Begin Bits: [0x000002bdfda90000, 0x000002bdfea90000)
 End Bits:   [0x000002bdfea90000, 0x000002bdffa90000)

Polling page: 0x000002bdf86c0000

Metaspace:

Usage:
  Non-class:     70.12 MB used.
      Class:      7.72 MB used.
       Both:     77.84 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      71.06 MB ( 56%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       8.44 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      79.50 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  8.89 MB
       Class:  7.63 MB
        Both:  16.52 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 132.50 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1398.
num_arena_deaths: 34.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1274.
num_space_uncommitted: 2.
num_chunks_returned_to_freelist: 66.
num_chunks_taken_from_freelist: 5025.
num_chunk_merges: 22.
num_chunk_splits: 3066.
num_chunks_enlarged: 1767.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=16548Kb max_used=18917Kb free=103451Kb
 bounds [0x000002bd87ad0000, 0x000002bd88d70000, 0x000002bd8f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=36876Kb max_used=44747Kb free=83123Kb
 bounds [0x000002bd80000000, 0x000002bd82bc0000, 0x000002bd87530000]
CodeHeap 'non-nmethods': size=5760Kb used=1486Kb max_used=1622Kb free=4273Kb
 bounds [0x000002bd87530000, 0x000002bd877a0000, 0x000002bd87ad0000]
 total_blobs=17314 nmethods=16507 adapters=710
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 457.671 Thread 0x000002bdffc6bb60 21469       3       org.eclipse.jdt.internal.compiler.lookup.ParameterizedGenericMethodBinding::computeCompatibleMethod (299 bytes)
Event: 457.720 Thread 0x000002bdffc6bb60 nmethod 21469 0x000002bd807a5d90 code [0x000002bd807a60c0, 0x000002bd807a7528]
Event: 457.720 Thread 0x000002bdffc6bb60 21470       3       org.eclipse.jdt.internal.compiler.lookup.TypeSystem::fixTypeVariableDeclaringElement (83 bytes)
Event: 457.720 Thread 0x000002bdffc6bb60 nmethod 21470 0x000002bd80131990 code [0x000002bd80131b60, 0x000002bd80132050]
Event: 457.724 Thread 0x000002bdffc6bb60 21472       3       org.eclipse.jdt.internal.compiler.lookup.InferenceContext18::addConstraintsToC (194 bytes)
Event: 457.726 Thread 0x000002bdffc6bb60 nmethod 21472 0x000002bd806a4510 code [0x000002bd806a4780, 0x000002bd806a5188]
Event: 457.726 Thread 0x000002bdffc6bb60 21473       3       org.eclipse.jdt.internal.compiler.lookup.ConstraintTypeFormula::create (24 bytes)
Event: 457.727 Thread 0x000002bdffc6bb60 nmethod 21473 0x000002bd8014de10 code [0x000002bd8014dfe0, 0x000002bd8014e378]
Event: 457.729 Thread 0x000002bdffc6bb60 21474       3       org.eclipse.jdt.internal.compiler.lookup.InferenceContext18::addJDK_8153748ConstraintsFromInvocation (110 bytes)
Event: 457.730 Thread 0x000002bdffc6bb60 nmethod 21474 0x000002bd80109690 code [0x000002bd801098a0, 0x000002bd80109f28]
Event: 457.730 Thread 0x000002bdffc6bb60 21475   !   3       org.eclipse.jdt.internal.compiler.lookup.ConstraintExpressionFormula::canBePolyExpression (44 bytes)
Event: 457.730 Thread 0x000002bdffc6bb60 nmethod 21475 0x000002bd803c5310 code [0x000002bd803c5500, 0x000002bd803c5a88]
Event: 457.732 Thread 0x000002bdffc6bb60 21476       3       org.eclipse.jdt.internal.compiler.lookup.TypeConstants$BoundCheckStatus::isOKbyJLS (36 bytes)
Event: 457.732 Thread 0x000002bdffc6bb60 nmethod 21476 0x000002bd801ab990 code [0x000002bd801abb40, 0x000002bd801abd50]
Event: 457.734 Thread 0x000002bdffc6bb60 21477       3       org.eclipse.jdt.internal.compiler.lookup.MethodBinding::redeclaresPublicObjectMethod (98 bytes)
Event: 457.736 Thread 0x000002bdffc6bb60 nmethod 21477 0x000002bd81be4810 code [0x000002bd81be4ac0, 0x000002bd81be5720]
Event: 457.736 Thread 0x000002bdffc6bb60 21478       3       org.eclipse.jdt.internal.compiler.lookup.BlockScope::addSubscope (57 bytes)
Event: 457.736 Thread 0x000002bdffc6bb60 nmethod 21478 0x000002bd80090e10 code [0x000002bd80090fe0, 0x000002bd80091410]
Event: 457.737 Thread 0x000002bdffc6bb60 21479       3       org.eclipse.jdt.internal.compiler.parser.Parser::markEnclosingMemberWithLocalOrFunctionalType (230 bytes)
Event: 457.739 Thread 0x000002bdffc6bb60 nmethod 21479 0x000002bd80d41510 code [0x000002bd80d41780, 0x000002bd80d42490]

GC Heap History (20 events):
Event: 456.689 GC heap before
{Heap before GC invocations=1858 (full 5):
 PSYoungGen      total 2560K, used 2559K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 99% used [0x00000000eab00000,0x00000000eacffca8,0x00000000ead00000)
  from space 512K, 100% used [0x00000000ead80000,0x00000000eae00000,0x00000000eae00000)
  to   space 1536K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eb000000)
 ParOldGen       total 288768K, used 288696K [0x00000000c0000000, 0x00000000d1a00000, 0x00000000eab00000)
  object space 288768K, 99% used [0x00000000c0000000,0x00000000d19ee2b0,0x00000000d1a00000)
 Metaspace       used 79625K, committed 81408K, reserved 1179648K
  class space    used 7903K, committed 8640K, reserved 1048576K
}
Event: 456.691 GC heap after
{Heap after GC invocations=1858 (full 5):
 PSYoungGen      total 2560K, used 288K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 56% used [0x00000000eae80000,0x00000000eaec8000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 289280K, used 288832K [0x00000000c0000000, 0x00000000d1a80000, 0x00000000eab00000)
  object space 289280K, 99% used [0x00000000c0000000,0x00000000d1a102b0,0x00000000d1a80000)
 Metaspace       used 79625K, committed 81408K, reserved 1179648K
  class space    used 7903K, committed 8640K, reserved 1048576K
}
Event: 456.700 GC heap before
{Heap before GC invocations=1859 (full 5):
 PSYoungGen      total 2560K, used 2336K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 56% used [0x00000000eae80000,0x00000000eaec8000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 289280K, used 288832K [0x00000000c0000000, 0x00000000d1a80000, 0x00000000eab00000)
  object space 289280K, 99% used [0x00000000c0000000,0x00000000d1a102b0,0x00000000d1a80000)
 Metaspace       used 79625K, committed 81408K, reserved 1179648K
  class space    used 7903K, committed 8640K, reserved 1048576K
}
Event: 456.702 GC heap after
{Heap after GC invocations=1859 (full 5):
 PSYoungGen      total 3072K, used 320K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 31% used [0x00000000ead00000,0x00000000ead50000,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 289280K, used 289034K [0x00000000c0000000, 0x00000000d1a80000, 0x00000000eab00000)
  object space 289280K, 99% used [0x00000000c0000000,0x00000000d1a42888,0x00000000d1a80000)
 Metaspace       used 79625K, committed 81408K, reserved 1179648K
  class space    used 7903K, committed 8640K, reserved 1048576K
}
Event: 456.839 GC heap before
{Heap before GC invocations=1860 (full 5):
 PSYoungGen      total 3072K, used 2368K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 31% used [0x00000000ead00000,0x00000000ead50000,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 289280K, used 289034K [0x00000000c0000000, 0x00000000d1a80000, 0x00000000eab00000)
  object space 289280K, 99% used [0x00000000c0000000,0x00000000d1a42888,0x00000000d1a80000)
 Metaspace       used 79626K, committed 81408K, reserved 1179648K
  class space    used 7903K, committed 8640K, reserved 1048576K
}
Event: 456.840 GC heap after
{Heap after GC invocations=1860 (full 5):
 PSYoungGen      total 2560K, used 288K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 56% used [0x00000000eae80000,0x00000000eaec8000,0x00000000eaf00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 289280K, used 289263K [0x00000000c0000000, 0x00000000d1a80000, 0x00000000eab00000)
  object space 289280K, 99% used [0x00000000c0000000,0x00000000d1a7be90,0x00000000d1a80000)
 Metaspace       used 79626K, committed 81408K, reserved 1179648K
  class space    used 7903K, committed 8640K, reserved 1048576K
}
Event: 457.408 GC heap before
{Heap before GC invocations=1861 (full 5):
 PSYoungGen      total 2560K, used 2336K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 56% used [0x00000000eae80000,0x00000000eaec8000,0x00000000eaf00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 289280K, used 289263K [0x00000000c0000000, 0x00000000d1a80000, 0x00000000eab00000)
  object space 289280K, 99% used [0x00000000c0000000,0x00000000d1a7be90,0x00000000d1a80000)
 Metaspace       used 79646K, committed 81408K, reserved 1179648K
  class space    used 7904K, committed 8640K, reserved 1048576K
}
Event: 457.411 GC heap after
{Heap after GC invocations=1861 (full 5):
 PSYoungGen      total 2560K, used 484K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 94% used [0x00000000eae00000,0x00000000eae792e0,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 289792K, used 289591K [0x00000000c0000000, 0x00000000d1b00000, 0x00000000eab00000)
  object space 289792K, 99% used [0x00000000c0000000,0x00000000d1acde90,0x00000000d1b00000)
 Metaspace       used 79646K, committed 81408K, reserved 1179648K
  class space    used 7904K, committed 8640K, reserved 1048576K
}
Event: 457.427 GC heap before
{Heap before GC invocations=1862 (full 5):
 PSYoungGen      total 2560K, used 2532K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 94% used [0x00000000eae00000,0x00000000eae792e0,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 289792K, used 289591K [0x00000000c0000000, 0x00000000d1b00000, 0x00000000eab00000)
  object space 289792K, 99% used [0x00000000c0000000,0x00000000d1acde90,0x00000000d1b00000)
 Metaspace       used 79646K, committed 81408K, reserved 1179648K
  class space    used 7904K, committed 8640K, reserved 1048576K
}
Event: 457.431 GC heap after
{Heap after GC invocations=1862 (full 5):
 PSYoungGen      total 2560K, used 512K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000eae80000,0x00000000eaf00000,0x00000000eaf00000)
  to   space 1536K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae80000)
 ParOldGen       total 290304K, used 290122K [0x00000000c0000000, 0x00000000d1b80000, 0x00000000eab00000)
  object space 290304K, 99% used [0x00000000c0000000,0x00000000d1b528b8,0x00000000d1b80000)
 Metaspace       used 79646K, committed 81408K, reserved 1179648K
  class space    used 7904K, committed 8640K, reserved 1048576K
}
Event: 457.533 GC heap before
{Heap before GC invocations=1863 (full 5):
 PSYoungGen      total 2560K, used 2560K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000eae80000,0x00000000eaf00000,0x00000000eaf00000)
  to   space 1536K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae80000)
 ParOldGen       total 290304K, used 290122K [0x00000000c0000000, 0x00000000d1b80000, 0x00000000eab00000)
  object space 290304K, 99% used [0x00000000c0000000,0x00000000d1b528b8,0x00000000d1b80000)
 Metaspace       used 79672K, committed 81408K, reserved 1179648K
  class space    used 7904K, committed 8640K, reserved 1048576K
}
Event: 457.536 GC heap after
{Heap after GC invocations=1863 (full 5):
 PSYoungGen      total 3584K, used 1056K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1536K, 68% used [0x00000000ead00000,0x00000000eae08000,0x00000000eae80000)
  to   space 1536K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eb000000)
 ParOldGen       total 290816K, used 290646K [0x00000000c0000000, 0x00000000d1c00000, 0x00000000eab00000)
  object space 290816K, 99% used [0x00000000c0000000,0x00000000d1bd58d8,0x00000000d1c00000)
 Metaspace       used 79672K, committed 81408K, reserved 1179648K
  class space    used 7904K, committed 8640K, reserved 1048576K
}
Event: 457.554 GC heap before
{Heap before GC invocations=1864 (full 5):
 PSYoungGen      total 3584K, used 3103K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 99% used [0x00000000eab00000,0x00000000eacfffe8,0x00000000ead00000)
  from space 1536K, 68% used [0x00000000ead00000,0x00000000eae08000,0x00000000eae80000)
  to   space 1536K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eb000000)
 ParOldGen       total 290816K, used 290646K [0x00000000c0000000, 0x00000000d1c00000, 0x00000000eab00000)
  object space 290816K, 99% used [0x00000000c0000000,0x00000000d1bd58d8,0x00000000d1c00000)
 Metaspace       used 79680K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K
}
Event: 457.559 GC heap after
{Heap after GC invocations=1864 (full 5):
 PSYoungGen      total 3072K, used 688K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 67% used [0x00000000eae80000,0x00000000eaf2c010,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 291840K, used 291581K [0x00000000c0000000, 0x00000000d1d00000, 0x00000000eab00000)
  object space 291840K, 99% used [0x00000000c0000000,0x00000000d1cbf480,0x00000000d1d00000)
 Metaspace       used 79680K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K
}
Event: 457.603 GC heap before
{Heap before GC invocations=1865 (full 5):
 PSYoungGen      total 3072K, used 2736K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 67% used [0x00000000eae80000,0x00000000eaf2c010,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 291840K, used 291581K [0x00000000c0000000, 0x00000000d1d00000, 0x00000000eab00000)
  object space 291840K, 99% used [0x00000000c0000000,0x00000000d1cbf480,0x00000000d1d00000)
 Metaspace       used 79682K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K
}
Event: 457.606 GC heap after
{Heap after GC invocations=1865 (full 5):
 PSYoungGen      total 3072K, used 928K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 90% used [0x00000000ead80000,0x00000000eae681e0,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 292352K, used 292213K [0x00000000c0000000, 0x00000000d1d80000, 0x00000000eab00000)
  object space 292352K, 99% used [0x00000000c0000000,0x00000000d1d5d4b0,0x00000000d1d80000)
 Metaspace       used 79682K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K
}
Event: 457.639 GC heap before
{Heap before GC invocations=1866 (full 5):
 PSYoungGen      total 3072K, used 2976K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 90% used [0x00000000ead80000,0x00000000eae681e0,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 292352K, used 292213K [0x00000000c0000000, 0x00000000d1d80000, 0x00000000eab00000)
  object space 292352K, 99% used [0x00000000c0000000,0x00000000d1d5d4b0,0x00000000d1d80000)
 Metaspace       used 79682K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K
}
Event: 457.642 GC heap after
{Heap after GC invocations=1866 (full 5):
 PSYoungGen      total 2560K, used 512K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000eae80000,0x00000000eaf00000,0x00000000eaf00000)
  to   space 1536K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae80000)
 ParOldGen       total 293376K, used 292933K [0x00000000c0000000, 0x00000000d1e80000, 0x00000000eab00000)
  object space 293376K, 99% used [0x00000000c0000000,0x00000000d1e114b0,0x00000000d1e80000)
 Metaspace       used 79682K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K
}
Event: 457.742 GC heap before
{Heap before GC invocations=1867 (full 5):
 PSYoungGen      total 2560K, used 2560K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000eae80000,0x00000000eaf00000,0x00000000eaf00000)
  to   space 1536K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae80000)
 ParOldGen       total 293376K, used 292933K [0x00000000c0000000, 0x00000000d1e80000, 0x00000000eab00000)
  object space 293376K, 99% used [0x00000000c0000000,0x00000000d1e114b0,0x00000000d1e80000)
 Metaspace       used 79688K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K
}
Event: 458.694 GC heap after
{Heap after GC invocations=1867 (full 5):
 PSYoungGen      total 3584K, used 448K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1536K, 29% used [0x00000000ead00000,0x00000000ead70000,0x00000000eae80000)
  to   space 1024K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf80000)
 ParOldGen       total 293888K, used 293385K [0x00000000c0000000, 0x00000000d1f00000, 0x00000000eab00000)
  object space 293888K, 99% used [0x00000000c0000000,0x00000000d1e824c0,0x00000000d1f00000)
 Metaspace       used 79688K, committed 81408K, reserved 1179648K
  class space    used 7906K, committed 8640K, reserved 1048576K
}

Dll operation events (14 events):
Event: 0.014 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.147 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.190 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.198 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.202 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.208 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.239 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.448 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 5.064 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 8.877 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 8.882 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
Event: 11.334 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna17819487717957553593.dll
Event: 12.162 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
Event: 12.474 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll

Deoptimization events (20 events):
Event: 457.189 Thread 0x000002bdd7042d10 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002bd88c8aa20 relative=0x0000000000001cc0
Event: 457.189 Thread 0x000002bdd7042d10 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002bd88c8aa20 method=org.eclipse.jdt.internal.compiler.parser.Scanner.internalScanIdentifierOrKeyword(II[C)Lorg/eclipse/jdt/internal/compiler/parser/TerminalToken; @ 2960 c2
Event: 457.189 Thread 0x000002bdd7042d10 DEOPT PACKING pc=0x000002bd88c8aa20 sp=0x000000c460bfebe0
Event: 457.189 Thread 0x000002bdd7042d10 DEOPT UNPACKING pc=0x000002bd87583aa2 sp=0x000000c460bfeb80 mode 2
Event: 457.634 Thread 0x000002bdd7042d10 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000002bd87c63244 relative=0x0000000000000464
Event: 457.634 Thread 0x000002bdd7042d10 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000002bd87c63244 method=org.eclipse.jdt.internal.compiler.lookup.UnresolvedReferenceBinding.addWrapper(Lorg/eclipse/jdt/internal/compiler/lookup/HotSwappable;Lorg/eclipse/jdt/internal/compiler/
Event: 457.634 Thread 0x000002bdd7042d10 DEOPT PACKING pc=0x000002bd87c63244 sp=0x000000c460bfc740
Event: 457.634 Thread 0x000002bdd7042d10 DEOPT UNPACKING pc=0x000002bd87583aa2 sp=0x000000c460bfc6f0 mode 2
Event: 457.634 Thread 0x000002bdd7042d10 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000002bd87c63244 relative=0x0000000000000464
Event: 457.634 Thread 0x000002bdd7042d10 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000002bd87c63244 method=org.eclipse.jdt.internal.compiler.lookup.UnresolvedReferenceBinding.addWrapper(Lorg/eclipse/jdt/internal/compiler/lookup/HotSwappable;Lorg/eclipse/jdt/internal/compiler/
Event: 457.634 Thread 0x000002bdd7042d10 DEOPT PACKING pc=0x000002bd87c63244 sp=0x000000c460bfc700
Event: 457.634 Thread 0x000002bdd7042d10 DEOPT UNPACKING pc=0x000002bd87583aa2 sp=0x000000c460bfc6b0 mode 2
Event: 457.635 Thread 0x000002bdd7042d10 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000002bd87c63244 relative=0x0000000000000464
Event: 457.635 Thread 0x000002bdd7042d10 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000002bd87c63244 method=org.eclipse.jdt.internal.compiler.lookup.UnresolvedReferenceBinding.addWrapper(Lorg/eclipse/jdt/internal/compiler/lookup/HotSwappable;Lorg/eclipse/jdt/internal/compiler/
Event: 457.635 Thread 0x000002bdd7042d10 DEOPT PACKING pc=0x000002bd87c63244 sp=0x000000c460bfc000
Event: 457.635 Thread 0x000002bdd7042d10 DEOPT UNPACKING pc=0x000002bd87583aa2 sp=0x000000c460bfbfb0 mode 2
Event: 457.635 Thread 0x000002bdd7042d10 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x000002bd87c63244 relative=0x0000000000000464
Event: 457.635 Thread 0x000002bdd7042d10 Uncommon trap: reason=array_check action=maybe_recompile pc=0x000002bd87c63244 method=org.eclipse.jdt.internal.compiler.lookup.UnresolvedReferenceBinding.addWrapper(Lorg/eclipse/jdt/internal/compiler/lookup/HotSwappable;Lorg/eclipse/jdt/internal/compiler/
Event: 457.635 Thread 0x000002bdd7042d10 DEOPT PACKING pc=0x000002bd87c63244 sp=0x000000c460bfc080
Event: 457.635 Thread 0x000002bdd7042d10 DEOPT UNPACKING pc=0x000002bd87583aa2 sp=0x000000c460bfc030 mode 2

Classes loaded (20 events):
Event: 384.328 Loading class java/util/TreeMap$NavigableSubMap$EntrySetView done
Event: 384.328 Loading class java/util/TreeMap$AscendingSubMap$AscendingEntrySetView done
Event: 384.328 Loading class java/util/TreeMap$NavigableSubMap$SubMapEntryIterator
Event: 384.328 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator
Event: 384.328 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator done
Event: 384.328 Loading class java/util/TreeMap$NavigableSubMap$SubMapEntryIterator done
Event: 387.046 Loading class java/io/CharArrayWriter
Event: 387.047 Loading class java/io/CharArrayWriter done
Event: 387.540 Loading class java/util/stream/ReduceOps$5
Event: 387.541 Loading class java/util/stream/ReduceOps$5 done
Event: 387.541 Loading class java/util/stream/ReduceOps$CountingSink$OfRef
Event: 387.542 Loading class java/util/stream/ReduceOps$CountingSink
Event: 387.597 Loading class java/util/stream/ReduceOps$CountingSink done
Event: 387.597 Loading class java/util/stream/ReduceOps$CountingSink$OfRef done
Event: 389.783 Loading class java/nio/BufferMismatch
Event: 389.786 Loading class java/nio/BufferMismatch done
Event: 390.900 Loading class java/util/stream/ReduceOps$1
Event: 390.900 Loading class java/util/stream/ReduceOps$1 done
Event: 390.900 Loading class java/util/stream/ReduceOps$1ReducingSink
Event: 390.902 Loading class java/util/stream/ReduceOps$1ReducingSink done

Classes unloaded (20 events):
Event: 374.569 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd90823878 'org/apache/maven/shared/filtering/DefaultFilterInfo'
Event: 374.569 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd90823578 'org/apache/maven/plugins/resources/TestResourcesMojo'
Event: 374.569 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd90823300 'org/apache/maven/plugins/maven_resources_plugin/HelpMojo'
Event: 374.569 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd90823000 'org/apache/maven/plugins/resources/CopyResourcesMojo'
Event: 374.569 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd90822c00 'org/apache/maven/plugins/resources/ResourcesMojo'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083d8a8 'org/springframework/boot/configurationprocessor/fieldvalues/FieldValuesParser$$Lambda+0x000002bd9083d8a8'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083d698 'org/springframework/boot/configurationprocessor/fieldvalues/javac/Trees'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083d488 'org/springframework/boot/configurationprocessor/fieldvalues/javac/ReflectionWrapper'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083d298 'org/springframework/boot/configurationprocessor/fieldvalues/javac/TreeVisitor'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083d078 'org/springframework/boot/configurationprocessor/fieldvalues/javac/JavaCompilerFieldValuesParser'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083ce88 'org/springframework/boot/configurationprocessor/fieldvalues/FieldValuesParser'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083cbd8 'org/springframework/boot/configurationprocessor/MetadataGenerationEnvironment'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083c9c0 'org/springframework/boot/configurationprocessor/TypeUtils$$Lambda+0x000002bd9083c9c0'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083c6a8 'org/springframework/boot/configurationprocessor/TypeUtils$TypeExtractor'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083c480 'org/springframework/boot/configurationprocessor/TypeUtils$$Lambda+0x000002bd9083c480'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083c230 'org/springframework/boot/configurationprocessor/TypeUtils'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd9083c000 'org/springframework/boot/configurationprocessor/MetadataCollector'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd90836d20 'org/springframework/boot/configurationprocessor/MetadataStore'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd90836ac8 'org/springframework/boot/configurationprocessor/metadata/InvalidConfigurationMetadataException'
Event: 446.723 Thread 0x000002bdffc2fba0 Unloading class 0x000002bd90836800 'org/springframework/boot/configurationprocessor/ConfigurationMetadataAnnotationProcessor'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 456.680 Thread 0x000002bdd7042d10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabc39f0}> (0x00000000eabc39f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 456.733 Thread 0x000002bdd9a591c0 Exception <a 'java/io/IOException'{0x00000000eabf9808}> (0x00000000eabf9808) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 456.733 Thread 0x000002bdd9a591c0 Exception <a 'java/io/IOException'{0x00000000eac06f08}> (0x00000000eac06f08) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 456.827 Thread 0x000002bdd7042d10 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabf13c8}> (0x00000000eabf13c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 457.108 Thread 0x000002bdd9a591c0 Exception <a 'java/io/IOException'{0x00000000eabdb328}> (0x00000000eabdb328) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 457.108 Thread 0x000002bdd9a591c0 Exception <a 'java/io/IOException'{0x00000000eabe89c8}> (0x00000000eabe89c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 457.404 Thread 0x000002bdd9a591c0 Exception <a 'java/io/IOException'{0x00000000eaceb680}> (0x00000000eaceb680) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 457.405 Thread 0x000002bdd9a591c0 Exception <a 'java/io/IOException'{0x00000000eacf8830}> (0x00000000eacf8830) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 457.653 Thread 0x000002bdd7042d10 Implicit null exception at 0x000002bd80ef8c9b to 0x000002bd80efc4fb
Event: 457.653 Thread 0x000002bdd7042d10 Exception <a 'java/lang/NullPointerException'{0x00000000eab13ad8}> (0x00000000eab13ad8) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 457.665 Thread 0x000002bdd7042d10 Implicit null exception at 0x000002bd80ef8c9b to 0x000002bd80efc4fb
Event: 457.665 Thread 0x000002bdd7042d10 Exception <a 'java/lang/NullPointerException'{0x00000000eab5dec8}> (0x00000000eab5dec8) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 457.726 Thread 0x000002bdd7042d10 Implicit null exception at 0x000002bd80ef8c9b to 0x000002bd80efc4fb
Event: 457.726 Thread 0x000002bdd7042d10 Exception <a 'java/lang/NullPointerException'{0x00000000eac0ad38}> (0x00000000eac0ad38) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 457.726 Thread 0x000002bdd7042d10 Implicit null exception at 0x000002bd80ef8c9b to 0x000002bd80efc4fb
Event: 457.727 Thread 0x000002bdd7042d10 Exception <a 'java/lang/NullPointerException'{0x00000000eac0f070}> (0x00000000eac0f070) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 457.735 Thread 0x000002bdd7042d10 Implicit null exception at 0x000002bd80ef8c9b to 0x000002bd80efc4fb
Event: 457.735 Thread 0x000002bdd7042d10 Exception <a 'java/lang/NullPointerException'{0x00000000eac41e28}> (0x00000000eac41e28) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]
Event: 457.737 Thread 0x000002bdd7042d10 Implicit null exception at 0x000002bd80ef8c9b to 0x000002bd80efc4fb
Event: 457.737 Thread 0x000002bdd7042d10 Exception <a 'java/lang/NullPointerException'{0x00000000eac6da10}> (0x00000000eac6da10) 
thrown [s\src\hotspot\share\runtime\sharedRuntime.cpp, line 625]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 456.689 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 456.691 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 456.700 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 456.702 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 456.839 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 456.840 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 457.408 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 457.411 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 457.427 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 457.431 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 457.533 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 457.536 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 457.554 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 457.559 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 457.603 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 457.606 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 457.639 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 457.643 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 457.742 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 458.695 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd825f6c90
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd825f9210
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd825fc290
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd825fe790
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd825ffe10
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd82600310
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd8263d910
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd8264ab90
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd82678c10
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd82679610
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd8267c790
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd8267db90
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd82680510
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd8268a110
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd82716190
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd82751810
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd8276a710
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd8276c890
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd827d0c90
Event: 446.974 Thread 0x000002bdffc2fba0 flushing  nmethod 0x000002bd82809210

Events (20 events):
Event: 384.335 Thread 0x000002bdd6fbc9c0 Thread exited: 0x000002bdd6fbc9c0
Event: 384.418 Thread 0x000002bdffc6bb60 Thread added: 0x000002bdd6fbd760
Event: 384.449 Thread 0x000002bdffc6bb60 Thread added: 0x000002bdd6fbf970
Event: 385.696 Thread 0x000002bdd6fbf970 Thread exited: 0x000002bdd6fbf970
Event: 385.931 Thread 0x000002bdffc6bb60 Thread added: 0x000002bdd6fbc9c0
Event: 387.309 Thread 0x000002bdd6fbc9c0 Thread exited: 0x000002bdd6fbc9c0
Event: 388.523 Thread 0x000002bdd6fbd760 Thread exited: 0x000002bdd6fbd760
Event: 388.952 Thread 0x000002bdffc6bb60 Thread added: 0x000002bdd6fbf970
Event: 389.563 Thread 0x000002bdd6fbf970 Thread exited: 0x000002bdd6fbf970
Event: 410.382 Thread 0x000002bdffc66510 Thread added: 0x000002bdd6fbf970
Event: 411.662 Thread 0x000002bdd6fbf970 Thread exited: 0x000002bdd6fbf970
Event: 422.026 Thread 0x000002bdd70488f0 Thread exited: 0x000002bdd70488f0
Event: 422.418 Thread 0x000002bdffc6bb60 Thread added: 0x000002bdd6fbebd0
Event: 430.262 Thread 0x000002bdd6fbebd0 Thread exited: 0x000002bdd6fbebd0
Event: 430.351 Thread 0x000002bdd9a570f0 Thread exited: 0x000002bdd9a570f0
Event: 442.959 Thread 0x000002bdffc66510 Thread added: 0x000002bdd6fbebd0
Event: 444.020 Thread 0x000002bdd6fbebd0 Thread exited: 0x000002bdd6fbebd0
Event: 444.337 Thread 0x000002bdd7042680 Thread exited: 0x000002bdd7042680
Event: 446.101 Thread 0x000002bddb76c930 Thread exited: 0x000002bddb76c930
Event: 457.307 Thread 0x000002bdffc6bb60 Thread added: 0x000002bdd6fc14b0


Dynamic libraries:
0x00007ff70c070000 - 0x00007ff70c07e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe0b1b0000 - 0x00007ffe0b405000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe0a820000 - 0x00007ffe0a8e7000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe088b0000 - 0x00007ffe08c50000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe08760000 - 0x00007ffe088aa000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe01b30000 - 0x00007ffe01b48000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe02010000 - 0x00007ffe0202e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe0a2e0000 - 0x00007ffe0a49f000 	C:\Windows\System32\USER32.dll
0x00007ffe08730000 - 0x00007ffe08757000 	C:\Windows\System32\win32u.dll
0x00007ffdebb00000 - 0x00007ffdebd90000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076\COMCTL32.dll
0x00007ffe09840000 - 0x00007ffe0986a000 	C:\Windows\System32\GDI32.dll
0x00007ffe08600000 - 0x00007ffe08721000 	C:\Windows\System32\gdi32full.dll
0x00007ffe09b90000 - 0x00007ffe09c39000 	C:\Windows\System32\msvcrt.dll
0x00007ffe08c60000 - 0x00007ffe08d03000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe0a570000 - 0x00007ffe0a59f000 	C:\Windows\System32\IMM32.DLL
0x00007ffe020d0000 - 0x00007ffe020dc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffdeacb0000 - 0x00007ffdead3d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffd97fb0000 - 0x00007ffd98d40000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe0a4b0000 - 0x00007ffe0a562000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe09fd0000 - 0x00007ffe0a076000 	C:\Windows\System32\sechost.dll
0x00007ffe098f0000 - 0x00007ffe09a09000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe09870000 - 0x00007ffe098e4000 	C:\Windows\System32\WS2_32.dll
0x00007ffe07e80000 - 0x00007ffe07ece000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffe01030000 - 0x00007ffe0103b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffded780000 - 0x00007ffded7b6000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe07e60000 - 0x00007ffe07e74000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe074a0000 - 0x00007ffe074ba000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe02000000 - 0x00007ffe0200a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffdec8b0000 - 0x00007ffdecaf1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe0a8f0000 - 0x00007ffe0ac65000 	C:\Windows\System32\combase.dll
0x00007ffe0a6a0000 - 0x00007ffe0a776000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdef220000 - 0x00007ffdef259000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe08fd0000 - 0x00007ffe09069000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe01b90000 - 0x00007ffe01b9f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffdf6f10000 - 0x00007ffdf6f2f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe09150000 - 0x00007ffe09836000 	C:\Windows\System32\SHELL32.dll
0x00007ffe06430000 - 0x00007ffe06c5b000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffe09a20000 - 0x00007ffe09af3000 	C:\Windows\System32\SHCORE.dll
0x00007ffe09070000 - 0x00007ffe090cd000 	C:\Windows\System32\shlwapi.dll
0x00007ffe08520000 - 0x00007ffe08544000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdf6ef0000 - 0x00007ffdf6f08000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffdfa0f0000 - 0x00007ffdfa100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe05e30000 - 0x00007ffe05f4d000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffe07940000 - 0x00007ffe079a8000 	C:\Windows\system32\mswsock.dll
0x00007ffdeae20000 - 0x00007ffdeae36000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffdf7ba0000 - 0x00007ffdf7bb0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffde9ba0000 - 0x00007ffde9be5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffe0a140000 - 0x00007ffe0a2d5000 	C:\Windows\System32\ole32.dll
0x00007ffdec4e0000 - 0x00007ffdec4ea000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ffdeab40000 - 0x00007ffdeab4b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ffe0a810000 - 0x00007ffe0a818000 	C:\Windows\System32\PSAPI.DLL
0x00007ffe07ce0000 - 0x00007ffe07cfc000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffe07400000 - 0x00007ffe07438000 	C:\Windows\system32\rsaenh.dll
0x00007ffe079e0000 - 0x00007ffe07a0b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffe07f70000 - 0x00007ffe07f96000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffe07b60000 - 0x00007ffe07b6c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe06ee0000 - 0x00007ffe06f10000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffe0a130000 - 0x00007ffe0a13a000 	C:\Windows\System32\NSI.dll
0x00007ffdddeb0000 - 0x00007ffdddef9000 	C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna17819487717957553593.dll
0x00007ffe003e0000 - 0x00007ffe003fc000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffe05af0000 - 0x00007ffe05b12000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffe03490000 - 0x00007ffe0349e000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
0x00007ffe08dd0000 - 0x00007ffe08f46000 	C:\Windows\System32\CRYPT32.dll
0x00007ffe07f30000 - 0x00007ffe07f60000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffe07ee0000 - 0x00007ffe07f1f000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffe06f60000 - 0x00007ffe0707b000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffe06c60000 - 0x00007ffe06c6b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffe05b50000 - 0x00007ffe05bd5000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffe020f0000 - 0x00007ffe020f9000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll
0x00007ffe05fb0000 - 0x00007ffe05fe5000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-63116079

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4e7d2523ade161ac9ce8aa01dc802f4d-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.1)
OS uptime: 0 days 3:27 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 1992, Current Mhz: 1792, Mhz Limit: 1792

Memory: 4k page, system-wide physical 8066M (752M free)
TotalPageFile size 14210M (AvailPageFile size 1M)
current process WorkingSet (physical memory assigned to process): 634M, peak: 1022M
current process commit charge ("private bytes"): 629M, peak: 1025M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
