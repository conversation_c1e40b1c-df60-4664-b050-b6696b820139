package com.ultimate.uff.multi.thread.generatedClass;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class JobDetails extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String jobId;
    private final FieldValue<String> jobIdVal = new FieldValue<>(null);
    private String dependencyJobId;
    private final FieldValue<String> dependencyJobIdVal = new FieldValue<>(null);

    public JobDetails() {}

    public JobDetails(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object jobIdObj = recordDetail.get("jobId");
        if (jobIdObj != null) {
            this.setJobId(jobIdObj.toString());
        }
        Object dependencyJobIdObj = recordDetail.get("dependencyJobId");
        if (dependencyJobIdObj != null) {
            this.setDependencyJobId(dependencyJobIdObj.toString());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (jobId != null) map.put("jobId", jobId);
        if (dependencyJobId != null) map.put("dependencyJobId", dependencyJobId);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "jobId", "dependencyJobId" -> true;
            default -> false;
        };
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
        this.jobIdVal.setValue(jobId);
    }
    public String getJobId() { return this.jobId; }
    public FieldValue<String> jobIdVal() { return jobIdVal; }
    public void setDependencyJobId(String dependencyJobId) {
        this.dependencyJobId = dependencyJobId;
        this.dependencyJobIdVal.setValue(dependencyJobId);
    }
    public String getDependencyJobId() { return this.dependencyJobId; }
    public FieldValue<String> dependencyJobIdVal() { return dependencyJobIdVal; }
}
