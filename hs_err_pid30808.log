#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 3338736 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=30808, tid=26336
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-3c13938d2f00705369675e91812301d6-sock

Host: Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz, 8 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.1)
Time: Tue May 27 14:29:23 2025 Egypt Daylight Time elapsed time: 74.794244 seconds (0d 0h 1m 14s)

---------------  T H R E A D  ---------------

Current thread (0x000002f34d99db50):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=26336, stack(0x000000fe99a00000,0x000000fe99b00000) (1024K)]


Current CompileTask:
C2:74794 18515       4       org.eclipse.jdt.internal.compiler.lookup.LookupEnvironment::getTypeFromTypeSignature (770 bytes)

Stack: [0x000000fe99a00000,0x000000fe99b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x1e0029]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x43340]
C  [KERNEL32.DLL+0x31fd7]
C  [ntdll.dll+0x6d7d0]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002f398404430, length=68, elements={
0x000002f3395db850, 0x000002f34d98ecd0, 0x000002f34d992280, 0x000002f34d994a40,
0x000002f34d995690, 0x000002f34d999500, 0x000002f34d999f50, 0x000002f34d99db50,
0x000002f390468920, 0x000002f390527820, 0x000002f39084e810, 0x000002f3957db270,
0x000002f3955c6780, 0x000002f395bdce00, 0x000002f3955c6de0, 0x000002f395e9d430,
0x000002f3907ce7c0, 0x000002f395d64aa0, 0x000002f395d63d80, 0x000002f395d63060,
0x000002f395d636f0, 0x000002f395d61620, 0x000002f395d61cb0, 0x000002f395d64410,
0x000002f395d629d0, 0x000002f395d62340, 0x000002f398c2f780, 0x000002f398c2d6b0,
0x000002f398c2f0f0, 0x000002f398c2fe10, 0x000002f398c30b30, 0x000002f398c304a0,
0x000002f398c2e3d0, 0x000002f398c2ea60, 0x000002f398c311c0, 0x000002f398c2dd40,
0x000002f398c33fb0, 0x000002f398c34cd0, 0x000002f398c33920, 0x000002f398280b30,
0x000002f3982811c0, 0x000002f3982859f0, 0x000002f398283fb0, 0x000002f398283920,
0x000002f398285360, 0x000002f398284cd0, 0x000002f398286080, 0x000002f398286da0,
0x000002f398287430, 0x000002f397a913a0, 0x000002f397a920c0, 0x000002f397a92750,
0x000002f397a92de0, 0x000002f397a93470, 0x000002f397a8fff0, 0x000002f397a93b00,
0x000002f397a90d10, 0x000002f397a94820, 0x000002f397a95540, 0x000002f397a95bd0,
0x000002f397afe3f0, 0x000002f398899130, 0x000002f397afbc90, 0x000002f397aff7a0,
0x000002f397b004c0, 0x000002f397affe30, 0x000002f397afd040, 0x000002f397afd6d0
}

Java Threads: ( => current thread )
  0x000002f3395db850 JavaThread "main"                              [_thread_blocked, id=28780, stack(0x000000fe99000000,0x000000fe99100000) (1024K)]
  0x000002f34d98ecd0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=33604, stack(0x000000fe99400000,0x000000fe99500000) (1024K)]
  0x000002f34d992280 JavaThread "Finalizer"                  daemon [_thread_blocked, id=17808, stack(0x000000fe99500000,0x000000fe99600000) (1024K)]
  0x000002f34d994a40 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=18536, stack(0x000000fe99600000,0x000000fe99700000) (1024K)]
  0x000002f34d995690 JavaThread "Attach Listener"            daemon [_thread_blocked, id=22864, stack(0x000000fe99700000,0x000000fe99800000) (1024K)]
  0x000002f34d999500 JavaThread "Service Thread"             daemon [_thread_blocked, id=28984, stack(0x000000fe99800000,0x000000fe99900000) (1024K)]
  0x000002f34d999f50 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=33232, stack(0x000000fe99900000,0x000000fe99a00000) (1024K)]
=>0x000002f34d99db50 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=26336, stack(0x000000fe99a00000,0x000000fe99b00000) (1024K)]
  0x000002f390468920 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=2272, stack(0x000000fe99b00000,0x000000fe99c00000) (1024K)]
  0x000002f390527820 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=3992, stack(0x000000fe99c00000,0x000000fe99d00000) (1024K)]
  0x000002f39084e810 JavaThread "Notification Thread"        daemon [_thread_blocked, id=29348, stack(0x000000fe99e00000,0x000000fe99f00000) (1024K)]
  0x000002f3957db270 JavaThread "Active Thread: Equinox Container: 013af714-fec1-4041-ba4f-826f20976f9d"        [_thread_blocked, id=34948, stack(0x000000fe9a600000,0x000000fe9a700000) (1024K)]
  0x000002f3955c6780 JavaThread "Refresh Thread: Equinox Container: 013af714-fec1-4041-ba4f-826f20976f9d" daemon [_thread_blocked, id=31196, stack(0x000000fe9a700000,0x000000fe9a800000) (1024K)]
  0x000002f395bdce00 JavaThread "Framework Event Dispatcher: Equinox Container: 013af714-fec1-4041-ba4f-826f20976f9d" daemon [_thread_blocked, id=4584, stack(0x000000fe9a900000,0x000000fe9aa00000) (1024K)]
  0x000002f3955c6de0 JavaThread "Start Level: Equinox Container: 013af714-fec1-4041-ba4f-826f20976f9d" daemon [_thread_blocked, id=30524, stack(0x000000fe9aa00000,0x000000fe9ab00000) (1024K)]
  0x000002f395e9d430 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=29948, stack(0x000000fe9ab00000,0x000000fe9ac00000) (1024K)]
  0x000002f3907ce7c0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=35300, stack(0x000000fe99f00000,0x000000fe9a000000) (1024K)]
  0x000002f395d64aa0 JavaThread "Worker-JM"                         [_thread_blocked, id=32856, stack(0x000000fe9ad00000,0x000000fe9ae00000) (1024K)]
  0x000002f395d63d80 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=27056, stack(0x000000fe9af00000,0x000000fe9b000000) (1024K)]
  0x000002f395d63060 JavaThread "Worker-0: Initialize Workspace"        [_thread_blocked, id=5920, stack(0x000000fe9b000000,0x000000fe9b100000) (1024K)]
  0x000002f395d636f0 JavaThread "Worker-1"                          [_thread_blocked, id=22908, stack(0x000000fe9b100000,0x000000fe9b200000) (1024K)]
  0x000002f395d61620 JavaThread "Java indexing"              daemon [_thread_in_native, id=31952, stack(0x000000fe9b200000,0x000000fe9b300000) (1024K)]
  0x000002f395d61cb0 JavaThread "Worker-2: Initialize workspace"        [_thread_blocked, id=28948, stack(0x000000fe9b300000,0x000000fe9b400000) (1024K)]
  0x000002f395d64410 JavaThread "Worker-3: Java indexing... "        [_thread_blocked, id=19284, stack(0x000000fe9b600000,0x000000fe9b700000) (1024K)]
  0x000002f395d629d0 JavaThread "Worker-4"                          [_thread_blocked, id=22272, stack(0x000000fe9b700000,0x000000fe9b800000) (1024K)]
  0x000002f395d62340 JavaThread "Thread-2"                   daemon [_thread_in_native, id=28240, stack(0x000000fe9b800000,0x000000fe9b900000) (1024K)]
  0x000002f398c2f780 JavaThread "Thread-3"                   daemon [_thread_in_native, id=23200, stack(0x000000fe9b900000,0x000000fe9ba00000) (1024K)]
  0x000002f398c2d6b0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=29852, stack(0x000000fe9ba00000,0x000000fe9bb00000) (1024K)]
  0x000002f398c2f0f0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=31172, stack(0x000000fe9bb00000,0x000000fe9bc00000) (1024K)]
  0x000002f398c2fe10 JavaThread "Thread-6"                   daemon [_thread_in_native, id=16780, stack(0x000000fe9bc00000,0x000000fe9bd00000) (1024K)]
  0x000002f398c30b30 JavaThread "Thread-7"                   daemon [_thread_in_native, id=26868, stack(0x000000fe9bd00000,0x000000fe9be00000) (1024K)]
  0x000002f398c304a0 JavaThread "Thread-8"                   daemon [_thread_in_native, id=31360, stack(0x000000fe9be00000,0x000000fe9bf00000) (1024K)]
  0x000002f398c2e3d0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=31348, stack(0x000000fe9bf00000,0x000000fe9c000000) (1024K)]
  0x000002f398c2ea60 JavaThread "Thread-10"                  daemon [_thread_in_native, id=32512, stack(0x000000fe9c000000,0x000000fe9c100000) (1024K)]
  0x000002f398c311c0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=5428, stack(0x000000fe9c100000,0x000000fe9c200000) (1024K)]
  0x000002f398c2dd40 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=14560, stack(0x000000fe9c200000,0x000000fe9c300000) (1024K)]
  0x000002f398c33fb0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=35056, stack(0x000000fe9c300000,0x000000fe9c400000) (1024K)]
  0x000002f398c34cd0 JavaThread "Worker-5"                          [_thread_blocked, id=27272, stack(0x000000fe9c400000,0x000000fe9c500000) (1024K)]
  0x000002f398c33920 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=32716, stack(0x000000fe99d00000,0x000000fe99e00000) (1024K)]
  0x000002f398280b30 JavaThread "Compiler Processing Task"   daemon [_thread_blocked, id=29328, stack(0x000000fe9c500000,0x000000fe9c600000) (1024K)]
  0x000002f3982811c0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=30212, stack(0x000000fe9c600000,0x000000fe9c700000) (1024K)]
  0x000002f3982859f0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=22956, stack(0x000000fe98d00000,0x000000fe98e00000) (1024K)]
  0x000002f398283fb0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=13624, stack(0x000000fe9ae00000,0x000000fe9af00000) (1024K)]
  0x000002f398283920 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=33032, stack(0x000000fe9c700000,0x000000fe9c800000) (1024K)]
  0x000002f398285360 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=24840, stack(0x000000fe9c800000,0x000000fe9c900000) (1024K)]
  0x000002f398284cd0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=13788, stack(0x000000fe9c900000,0x000000fe9ca00000) (1024K)]
  0x000002f398286080 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=10872, stack(0x000000fe9ca00000,0x000000fe9cb00000) (1024K)]
  0x000002f398286da0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=32964, stack(0x000000fe9cb00000,0x000000fe9cc00000) (1024K)]
  0x000002f398287430 JavaThread "Worker-6"                          [_thread_blocked, id=32756, stack(0x000000fe9cc00000,0x000000fe9cd00000) (1024K)]
  0x000002f397a913a0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=32900, stack(0x000000fe9cd00000,0x000000fe9ce00000) (1024K)]
  0x000002f397a920c0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=30784, stack(0x000000fe9ce00000,0x000000fe9cf00000) (1024K)]
  0x000002f397a92750 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=23856, stack(0x000000fe9cf00000,0x000000fe9d000000) (1024K)]
  0x000002f397a92de0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=33736, stack(0x000000fe9d000000,0x000000fe9d100000) (1024K)]
  0x000002f397a93470 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=33448, stack(0x000000fe9d100000,0x000000fe9d200000) (1024K)]
  0x000002f397a8fff0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=15152, stack(0x000000fe9d200000,0x000000fe9d300000) (1024K)]
  0x000002f397a93b00 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=12976, stack(0x000000fe9d300000,0x000000fe9d400000) (1024K)]
  0x000002f397a90d10 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=29812, stack(0x000000fe9d400000,0x000000fe9d500000) (1024K)]
  0x000002f397a94820 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=34468, stack(0x000000fe9d500000,0x000000fe9d600000) (1024K)]
  0x000002f397a95540 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=28564, stack(0x000000fe9d600000,0x000000fe9d700000) (1024K)]
  0x000002f397a95bd0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=6312, stack(0x000000fe9d700000,0x000000fe9d800000) (1024K)]
  0x000002f397afe3f0 JavaThread "Worker-7"                          [_thread_blocked, id=7500, stack(0x000000fe9d800000,0x000000fe9d900000) (1024K)]
  0x000002f398899130 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=30228, stack(0x000000fe98e00000,0x000000fe98f00000) (1024K)]
  0x000002f397afbc90 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=22532, stack(0x000000fe9ac00000,0x000000fe9ad00000) (1024K)]
  0x000002f397aff7a0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=708, stack(0x000000fe9d900000,0x000000fe9da00000) (1024K)]
  0x000002f397b004c0 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=34012, stack(0x000000fe9da00000,0x000000fe9db00000) (1024K)]
  0x000002f397affe30 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=14780, stack(0x000000fe9db00000,0x000000fe9dc00000) (1024K)]
  0x000002f397afd040 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=6400, stack(0x000000fe9dc00000,0x000000fe9dd00000) (1024K)]
  0x000002f397afd6d0 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=16020, stack(0x000000fe9dd00000,0x000000fe9de00000) (1024K)]
Total: 68

Other Threads:
  0x000002f34d9725f0 VMThread "VM Thread"                           [id=26552, stack(0x000000fe99300000,0x000000fe99400000) (1024K)]
  0x000002f34d8aca00 WatcherThread "VM Periodic Task Thread"        [id=30316, stack(0x000000fe99200000,0x000000fe99300000) (1024K)]
  0x000002f3395faa90 WorkerThread "GC Thread#0"                     [id=27764, stack(0x000000fe99100000,0x000000fe99200000) (1024K)]
  0x000002f3952cba70 WorkerThread "GC Thread#1"                     [id=17912, stack(0x000000fe9a000000,0x000000fe9a100000) (1024K)]
  0x000002f395728210 WorkerThread "GC Thread#2"                     [id=18512, stack(0x000000fe9a100000,0x000000fe9a200000) (1024K)]
  0x000002f3954d66c0 WorkerThread "GC Thread#3"                     [id=27876, stack(0x000000fe9a200000,0x000000fe9a300000) (1024K)]
  0x000002f39586c770 WorkerThread "GC Thread#4"                     [id=13060, stack(0x000000fe9a300000,0x000000fe9a400000) (1024K)]
  0x000002f39586cb10 WorkerThread "GC Thread#5"                     [id=11644, stack(0x000000fe9a400000,0x000000fe9a500000) (1024K)]
  0x000002f3956c1a00 WorkerThread "GC Thread#6"                     [id=33552, stack(0x000000fe9a500000,0x000000fe9a600000) (1024K)]
  0x000002f395467c30 WorkerThread "GC Thread#7"                     [id=35264, stack(0x000000fe9a800000,0x000000fe9a900000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  74840 18515       4       org.eclipse.jdt.internal.compiler.lookup.LookupEnvironment::getTypeFromTypeSignature (770 bytes)
C2 CompilerThread1  74840 19033       4       java.lang.reflect.Field::get (39 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002f34e000000-0x000002f34eba0000-0x000002f34eba0000), size 12189696, SharedBaseAddress: 0x000002f34e000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002f34f000000-0x000002f38f000000, reserved size: 1073741824
Narrow klass base: 0x000002f34e000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 8066M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 4096K, used 2240K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 3072K, 55% used [0x00000000eab00000,0x00000000eaca99e8,0x00000000eae00000)
  from space 1024K, 52% used [0x00000000eae00000,0x00000000eae866f0,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eb000000)
 ParOldGen       total 484352K, used 484103K [0x00000000c0000000, 0x00000000dd900000, 0x00000000eab00000)
  object space 484352K, 99% used [0x00000000c0000000,0x00000000dd8c1c30,0x00000000dd900000)
 Metaspace       used 70106K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K

Card table byte_map: [0x000002f338f80000,0x000002f339190000] _byte_map_base: 0x000002f338980000

Marking Bits: (ParMarkBitMap*) 0x00007ffd98c631f0
 Begin Bits: [0x000002f34b7d0000, 0x000002f34c7d0000)
 End Bits:   [0x000002f34c7d0000, 0x000002f34d7d0000)

Polling page: 0x000002f3373c0000

Metaspace:

Usage:
  Non-class:     61.74 MB used.
      Class:      6.72 MB used.
       Both:     68.46 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      62.56 MB ( 98%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       7.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      70.00 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  1.19 MB
       Class:  8.42 MB
        Both:  9.61 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 115.44 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 11.
num_arena_births: 1390.
num_arena_deaths: 22.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1124.
num_space_uncommitted: 3.
num_chunks_returned_to_freelist: 70.
num_chunks_taken_from_freelist: 4888.
num_chunk_merges: 24.
num_chunk_splits: 2950.
num_chunks_enlarged: 1617.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=14342Kb max_used=14342Kb free=105657Kb
 bounds [0x000002f3442a0000, 0x000002f3450b0000, 0x000002f34b7d0000]
CodeHeap 'profiled nmethods': size=120000Kb used=40026Kb max_used=40026Kb free=79973Kb
 bounds [0x000002f33c7d0000, 0x000002f33eef0000, 0x000002f343d00000]
CodeHeap 'non-nmethods': size=5760Kb used=1442Kb max_used=1551Kb free=4318Kb
 bounds [0x000002f343d00000, 0x000002f343f70000, 0x000002f3442a0000]
 total_blobs=17393 nmethods=16641 adapters=655
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 73.606 Thread 0x000002f390468920 19052       3       lombok.eclipse.agent.PatchVal::is (182 bytes)
Event: 73.607 Thread 0x000002f390468920 nmethod 19052 0x000002f33d526990 code [0x000002f33d526c00, 0x000002f33d5278d0]
Event: 73.608 Thread 0x000002f390468920 19053       2       org.eclipse.jdt.internal.compiler.lookup.AnnotationBinding::resolve (1 bytes)
Event: 73.608 Thread 0x000002f390468920 nmethod 19053 0x000002f33d1df590 code [0x000002f33d1df720, 0x000002f33d1df828]
Event: 73.608 Thread 0x000002f390468920 19054       3       java.util.Spliterators$IteratorSpliterator::tryAdvance (77 bytes)
Event: 73.609 Thread 0x000002f390468920 nmethod 19054 0x000002f33d525e90 code [0x000002f33d5260c0, 0x000002f33d526798]
Event: 73.617 Thread 0x000002f390468920 19055       2       org.eclipse.jdt.internal.core.search.indexing.SourceIndexer::usedDomBasedIndexing (16 bytes)
Event: 73.618 Thread 0x000002f390468920 nmethod 19055 0x000002f33d525290 code [0x000002f33d525500, 0x000002f33d525a28]
Event: 73.625 Thread 0x000002f390468920 19056       2       org.eclipse.jdt.internal.compiler.parser.Parser::consumeReferenceType2 (25 bytes)
Event: 73.630 Thread 0x000002f390468920 nmethod 19056 0x000002f33d675c10 code [0x000002f33d675dc0, 0x000002f33d675f60]
Event: 73.632 Thread 0x000002f390468920 19057       2       org.eclipse.jdt.internal.compiler.SourceElementNotifier$LocalDeclarationVisitor::visit (11 bytes)
Event: 73.632 Thread 0x000002f390468920 nmethod 19057 0x000002f33d675710 code [0x000002f33d6758c0, 0x000002f33d675a08]
Event: 73.632 Thread 0x000002f390468920 19058       2       org.eclipse.jdt.internal.compiler.SourceElementNotifier::notifySourceElementRequestor (64 bytes)
Event: 73.632 Thread 0x000002f390468920 nmethod 19058 0x000002f33d524d90 code [0x000002f33d524f60, 0x000002f33d525148]
Event: 73.634 Thread 0x000002f390468920 19059   !   3       lombok.eclipse.HandlerLibrary::handleAnnotation (224 bytes)
Event: 73.637 Thread 0x000002f390468920 nmethod 19059 0x000002f33d95af90 code [0x000002f33d95b400, 0x000002f33d95d5c0]
Event: 73.637 Thread 0x000002f390468920 19060       3       java.net.URI::equal (19 bytes)
Event: 73.637 Thread 0x000002f390468920 nmethod 19060 0x000002f33d524990 code [0x000002f33d524b40, 0x000002f33d524cb0]
Event: 73.671 Thread 0x000002f390468920 19061       3       org.eclipse.jdt.internal.compiler.lookup.BinaryTypeBinding::getMemberType (170 bytes)
Event: 73.672 Thread 0x000002f390468920 nmethod 19061 0x000002f33d523510 code [0x000002f33d5237c0, 0x000002f33d5244c8]

GC Heap History (20 events):
Event: 73.344 GC heap after
{Heap after GC invocations=1701 (full 4):
 PSYoungGen      total 3072K, used 416K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 40% used [0x00000000eae00000,0x00000000eae68000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 478720K, used 478443K [0x00000000c0000000, 0x00000000dd380000, 0x00000000eab00000)
  object space 478720K, 99% used [0x00000000c0000000,0x00000000dd33af08,0x00000000dd380000)
 Metaspace       used 70027K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.435 GC heap before
{Heap before GC invocations=1702 (full 4):
 PSYoungGen      total 3072K, used 2464K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 40% used [0x00000000eae00000,0x00000000eae68000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 478720K, used 478443K [0x00000000c0000000, 0x00000000dd380000, 0x00000000eab00000)
  object space 478720K, 99% used [0x00000000c0000000,0x00000000dd33af08,0x00000000dd380000)
 Metaspace       used 70033K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.437 GC heap after
{Heap after GC invocations=1702 (full 4):
 PSYoungGen      total 3072K, used 394K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 38% used [0x00000000ead00000,0x00000000ead62830,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 478720K, used 478716K [0x00000000c0000000, 0x00000000dd380000, 0x00000000eab00000)
  object space 478720K, 99% used [0x00000000c0000000,0x00000000dd37f0c8,0x00000000dd380000)
 Metaspace       used 70033K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.506 GC heap before
{Heap before GC invocations=1703 (full 4):
 PSYoungGen      total 3072K, used 2442K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 38% used [0x00000000ead00000,0x00000000ead62830,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 478720K, used 478716K [0x00000000c0000000, 0x00000000dd380000, 0x00000000eab00000)
  object space 478720K, 99% used [0x00000000c0000000,0x00000000dd37f0c8,0x00000000dd380000)
 Metaspace       used 70040K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.509 GC heap after
{Heap after GC invocations=1703 (full 4):
 PSYoungGen      total 2560K, used 420K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 82% used [0x00000000eae00000,0x00000000eae692e0,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 479232K, used 478974K [0x00000000c0000000, 0x00000000dd400000, 0x00000000eab00000)
  object space 479232K, 99% used [0x00000000c0000000,0x00000000dd3bf8f8,0x00000000dd400000)
 Metaspace       used 70040K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.533 GC heap before
{Heap before GC invocations=1704 (full 4):
 PSYoungGen      total 2560K, used 2468K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 82% used [0x00000000eae00000,0x00000000eae692e0,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 479232K, used 478974K [0x00000000c0000000, 0x00000000dd400000, 0x00000000eab00000)
  object space 479232K, 99% used [0x00000000c0000000,0x00000000dd3bf8f8,0x00000000dd400000)
 Metaspace       used 70040K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.536 GC heap after
{Heap after GC invocations=1704 (full 4):
 PSYoungGen      total 2560K, used 512K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000ead80000,0x00000000eae00000,0x00000000eae00000)
  to   space 1536K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eb000000)
 ParOldGen       total 479744K, used 479410K [0x00000000c0000000, 0x00000000dd480000, 0x00000000eab00000)
  object space 479744K, 99% used [0x00000000c0000000,0x00000000dd42cbe8,0x00000000dd480000)
 Metaspace       used 70040K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.542 GC heap before
{Heap before GC invocations=1705 (full 4):
 PSYoungGen      total 2560K, used 2560K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000ead80000,0x00000000eae00000,0x00000000eae00000)
  to   space 1536K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eb000000)
 ParOldGen       total 479744K, used 479410K [0x00000000c0000000, 0x00000000dd480000, 0x00000000eab00000)
  object space 479744K, 99% used [0x00000000c0000000,0x00000000dd42cbe8,0x00000000dd480000)
 Metaspace       used 70040K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.547 GC heap after
{Heap after GC invocations=1705 (full 4):
 PSYoungGen      total 3072K, used 864K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 84% used [0x00000000eae80000,0x00000000eaf58000,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 480256K, used 479894K [0x00000000c0000000, 0x00000000dd500000, 0x00000000eab00000)
  object space 480256K, 99% used [0x00000000c0000000,0x00000000dd4a58d0,0x00000000dd500000)
 Metaspace       used 70040K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.571 GC heap before
{Heap before GC invocations=1706 (full 4):
 PSYoungGen      total 3072K, used 2912K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 84% used [0x00000000eae80000,0x00000000eaf58000,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 480256K, used 479894K [0x00000000c0000000, 0x00000000dd500000, 0x00000000eab00000)
  object space 480256K, 99% used [0x00000000c0000000,0x00000000dd4a58d0,0x00000000dd500000)
 Metaspace       used 70051K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.575 GC heap after
{Heap after GC invocations=1706 (full 4):
 PSYoungGen      total 3072K, used 674K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 65% used [0x00000000ead80000,0x00000000eae28bc8,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 480768K, used 480718K [0x00000000c0000000, 0x00000000dd580000, 0x00000000eab00000)
  object space 480768K, 99% used [0x00000000c0000000,0x00000000dd5738e0,0x00000000dd580000)
 Metaspace       used 70051K, committed 71616K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.592 GC heap before
{Heap before GC invocations=1707 (full 4):
 PSYoungGen      total 3072K, used 2722K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 65% used [0x00000000ead80000,0x00000000eae28bc8,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 480768K, used 480718K [0x00000000c0000000, 0x00000000dd580000, 0x00000000eab00000)
  object space 480768K, 99% used [0x00000000c0000000,0x00000000dd5738e0,0x00000000dd580000)
 Metaspace       used 70061K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.596 GC heap after
{Heap after GC invocations=1707 (full 4):
 PSYoungGen      total 2560K, used 512K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000eae80000,0x00000000eaf00000,0x00000000eaf00000)
  to   space 1536K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae80000)
 ParOldGen       total 481792K, used 481321K [0x00000000c0000000, 0x00000000dd680000, 0x00000000eab00000)
  object space 481792K, 99% used [0x00000000c0000000,0x00000000dd60a4a8,0x00000000dd680000)
 Metaspace       used 70061K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.626 GC heap before
{Heap before GC invocations=1708 (full 4):
 PSYoungGen      total 2560K, used 2560K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000eae80000,0x00000000eaf00000,0x00000000eaf00000)
  to   space 1536K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae80000)
 ParOldGen       total 481792K, used 481321K [0x00000000c0000000, 0x00000000dd680000, 0x00000000eab00000)
  object space 481792K, 99% used [0x00000000c0000000,0x00000000dd60a4a8,0x00000000dd680000)
 Metaspace       used 70070K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.630 GC heap after
{Heap after GC invocations=1708 (full 4):
 PSYoungGen      total 3072K, used 569K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 55% used [0x00000000ead00000,0x00000000ead8e6f0,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf00000)
 ParOldGen       total 481792K, used 481677K [0x00000000c0000000, 0x00000000dd680000, 0x00000000eab00000)
  object space 481792K, 99% used [0x00000000c0000000,0x00000000dd6634b8,0x00000000dd680000)
 Metaspace       used 70070K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.640 GC heap before
{Heap before GC invocations=1709 (full 4):
 PSYoungGen      total 3072K, used 2617K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 55% used [0x00000000ead00000,0x00000000ead8e6f0,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf00000)
 ParOldGen       total 481792K, used 481677K [0x00000000c0000000, 0x00000000dd680000, 0x00000000eab00000)
  object space 481792K, 99% used [0x00000000c0000000,0x00000000dd6634b8,0x00000000dd680000)
 Metaspace       used 70072K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.644 GC heap after
{Heap after GC invocations=1709 (full 4):
 PSYoungGen      total 3072K, used 704K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 68% used [0x00000000eae00000,0x00000000eaeb0000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 482304K, used 482108K [0x00000000c0000000, 0x00000000dd700000, 0x00000000eab00000)
  object space 482304K, 99% used [0x00000000c0000000,0x00000000dd6cf038,0x00000000dd700000)
 Metaspace       used 70072K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.653 GC heap before
{Heap before GC invocations=1710 (full 4):
 PSYoungGen      total 3072K, used 2752K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 68% used [0x00000000eae00000,0x00000000eaeb0000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 482304K, used 482108K [0x00000000c0000000, 0x00000000dd700000, 0x00000000eab00000)
  object space 482304K, 99% used [0x00000000c0000000,0x00000000dd6cf038,0x00000000dd700000)
 Metaspace       used 70072K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.657 GC heap after
{Heap after GC invocations=1710 (full 4):
 PSYoungGen      total 3072K, used 994K [0x00000000eab00000, 0x00000000eb180000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 97% used [0x00000000ead00000,0x00000000eadf8bc8,0x00000000eae00000)
  to   space 2048K, 0% used [0x00000000eaf80000,0x00000000eaf80000,0x00000000eb180000)
 ParOldGen       total 482816K, used 482732K [0x00000000c0000000, 0x00000000dd780000, 0x00000000eab00000)
  object space 482816K, 99% used [0x00000000c0000000,0x00000000dd76b058,0x00000000dd780000)
 Metaspace       used 70072K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}
Event: 73.673 GC heap before
{Heap before GC invocations=1711 (full 4):
 PSYoungGen      total 3072K, used 3042K [0x00000000eab00000, 0x00000000eb180000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 97% used [0x00000000ead00000,0x00000000eadf8bc8,0x00000000eae00000)
  to   space 2048K, 0% used [0x00000000eaf80000,0x00000000eaf80000,0x00000000eb180000)
 ParOldGen       total 482816K, used 482732K [0x00000000c0000000, 0x00000000dd780000, 0x00000000eab00000)
  object space 482816K, 99% used [0x00000000c0000000,0x00000000dd76b058,0x00000000dd780000)
 Metaspace       used 70072K, committed 71680K, reserved 1114112K
  class space    used 6884K, committed 7616K, reserved 1048576K
}

Dll operation events (12 events):
Event: 0.010 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.151 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.179 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.182 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.184 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.187 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.208 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.285 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.797 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.510 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna10992244763178060026.dll
Event: 6.797 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 6.803 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 73.303 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38d148 sp=0x000000fe9b2fef10
Event: 73.303 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fe430 mode 0
Event: 73.327 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38d148 sp=0x000000fe9b2fef10
Event: 73.327 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fe430 mode 0
Event: 73.374 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38d148 sp=0x000000fe9b2fef10
Event: 73.374 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fe430 mode 0
Event: 73.390 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38d148 sp=0x000000fe9b2fef10
Event: 73.390 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fe430 mode 0
Event: 73.423 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38c7c2 sp=0x000000fe9b2fef10
Event: 73.423 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fe428 mode 0
Event: 73.449 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38c7c2 sp=0x000000fe9b2fef10
Event: 73.449 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fe428 mode 0
Event: 73.485 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38c7c2 sp=0x000000fe9b2fef10
Event: 73.486 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fe428 mode 0
Event: 73.506 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38c7c2 sp=0x000000fe9b2fef10
Event: 73.506 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fe428 mode 0
Event: 73.605 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38c7f6 sp=0x000000fe9b2fda20
Event: 73.605 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fcf38 mode 0
Event: 73.651 Thread 0x000002f395d61620 DEOPT PACKING pc=0x000002f33d38c7c2 sp=0x000000fe9b2fe590
Event: 73.651 Thread 0x000002f395d61620 DEOPT UNPACKING pc=0x000002f343d54242 sp=0x000000fe9b2fdaa8 mode 0

Classes loaded (20 events):
Event: 43.682 Loading class java/util/EnumMap$EntrySet
Event: 43.682 Loading class java/util/EnumMap$EntrySet done
Event: 43.682 Loading class java/util/EnumMap$EntryIterator
Event: 43.682 Loading class java/util/EnumMap$EnumMapIterator
Event: 43.682 Loading class java/util/EnumMap$EnumMapIterator done
Event: 43.683 Loading class java/util/EnumMap$EntryIterator done
Event: 43.683 Loading class java/util/EnumMap$EntryIterator$Entry
Event: 43.684 Loading class java/util/EnumMap$EntryIterator$Entry done
Event: 43.806 Loading class java/util/stream/ReferencePipeline$4
Event: 43.807 Loading class java/util/stream/ReferencePipeline$4 done
Event: 43.808 Loading class java/util/stream/Nodes$IntFixedNodeBuilder
Event: 43.808 Loading class java/util/stream/Node$Builder$OfInt
Event: 43.808 Loading class java/util/stream/Node$Builder$OfInt done
Event: 43.808 Loading class java/util/stream/Nodes$IntArrayNode
Event: 43.809 Loading class java/util/stream/Nodes$IntArrayNode done
Event: 43.809 Loading class java/util/stream/Nodes$IntFixedNodeBuilder done
Event: 43.809 Loading class java/util/stream/ReferencePipeline$4$1
Event: 43.809 Loading class java/util/stream/ReferencePipeline$4$1 done
Event: 49.606 Loading class java/nio/BufferMismatch
Event: 49.606 Loading class java/nio/BufferMismatch done

Classes unloaded (20 events):
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a44f8 'org/apache/maven/shared/filtering/MavenReaderFilterRequest'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a41f0 'org/apache/maven/shared/filtering/MavenFileFilterRequest'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a4000 'org/codehaus/plexus/interpolation/Interpolator'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a2df0 'org/codehaus/plexus/interpolation/BasicInterpolator'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a2c00 'org/codehaus/plexus/interpolation/ValueSource'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a3bc8 'org/apache/maven/shared/filtering/MavenResourcesExecution'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a3900 'org/apache/maven/shared/filtering/AbstractMavenFilteringRequest'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a36b0 'org/apache/maven/shared/filtering/MavenFilteringException'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a3470 'org/apache/maven/shared/filtering/DefaultMavenResourcesFiltering'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a3280 'org/apache/maven/shared/filtering/MavenResourcesFiltering'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a3000 'org/apache/maven/shared/filtering/DefaultMavenReaderFilter'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a24d0 'org/apache/maven/shared/filtering/MavenReaderFilter'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a2240 'org/apache/maven/shared/filtering/DefaultMavenFileFilter'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a2000 'org/apache/maven/shared/filtering/BaseFilter'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a1d68 'org/apache/maven/shared/filtering/MavenFileFilter'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a1b78 'org/apache/maven/shared/filtering/DefaultFilterInfo'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a1878 'org/apache/maven/plugins/resources/TestResourcesMojo'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a1600 'org/apache/maven/plugins/maven_resources_plugin/HelpMojo'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a1300 'org/apache/maven/plugins/resources/CopyResourcesMojo'
Event: 57.552 Thread 0x000002f34d9725f0 Unloading class 0x000002f34f6a1000 'org/apache/maven/plugins/resources/ResourcesMojo'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 64.986 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac92190}> (0x00000000eac92190) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.066 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac29a68}> (0x00000000eac29a68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.133 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabebe70}> (0x00000000eabebe70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.186 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac4ca70}> (0x00000000eac4ca70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.249 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab7b550}> (0x00000000eab7b550) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.305 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac210f0}> (0x00000000eac210f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.617 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac7d350}> (0x00000000eac7d350) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.751 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab43388}> (0x00000000eab43388) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.854 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab9e318}> (0x00000000eab9e318) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 65.981 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac6e018}> (0x00000000eac6e018) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 66.039 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eacdd058}> (0x00000000eacdd058) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 66.290 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabfa2f0}> (0x00000000eabfa2f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 66.400 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eacc5bc0}> (0x00000000eacc5bc0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 66.489 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab12c20}> (0x00000000eab12c20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 66.572 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab09878}> (0x00000000eab09878) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 67.767 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eababe58}> (0x00000000eababe58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 67.804 Thread 0x000002f395d61620 Implicit null exception at 0x000002f344e35d97 to 0x000002f344e37334
Event: 67.804 Thread 0x000002f395d61620 Implicit null exception at 0x000002f344bf41a0 to 0x000002f344bf64b8
Event: 73.482 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac2c218}> (0x00000000eac2c218) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 73.503 Thread 0x000002f395d61620 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eace6008}> (0x00000000eace6008) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 73.344 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.435 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.437 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.506 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.509 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.533 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.536 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.542 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.547 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.571 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.575 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.591 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.596 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.626 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.630 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.640 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.644 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.653 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 73.658 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 73.673 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db37890
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db3f310
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db41b10
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db45c10
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db46210
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db48110
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db4b790
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db4ff10
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db5a190
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db77010
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db7dc10
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db80210
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db80990
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33db82310
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33dbada10
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33dbbaa90
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33dbbbb10
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33dcfc390
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33dd72390
Event: 57.627 Thread 0x000002f34d9725f0 flushing  nmethod 0x000002f33dd76590

Events (20 events):
Event: 45.175 Thread 0x000002f398c34cd0 Thread added: 0x000002f397120430
Event: 45.175 Thread 0x000002f398c34cd0 Thread added: 0x000002f39711d640
Event: 45.175 Thread 0x000002f398c34cd0 Thread added: 0x000002f39711fda0
Event: 45.175 Thread 0x000002f398c34cd0 Thread added: 0x000002f39711dcd0
Event: 45.209 Thread 0x000002f39711fda0 Thread exited: 0x000002f39711fda0
Event: 45.209 Thread 0x000002f39711d640 Thread exited: 0x000002f39711d640
Event: 45.209 Thread 0x000002f39711dcd0 Thread exited: 0x000002f39711dcd0
Event: 45.209 Thread 0x000002f397a96f80 Thread exited: 0x000002f397a96f80
Event: 45.209 Thread 0x000002f397120430 Thread exited: 0x000002f397120430
Event: 49.611 Thread 0x000002f398899130 Thread exited: 0x000002f398899130
Event: 57.896 Thread 0x000002f390468920 Thread added: 0x000002f398899130
Event: 57.903 Thread 0x000002f395d629d0 Thread added: 0x000002f397afe3f0
Event: 58.295 Thread 0x000002f398899130 Thread exited: 0x000002f398899130
Event: 67.810 Thread 0x000002f390468920 Thread added: 0x000002f398899130
Event: 68.123 Thread 0x000002f395d61620 Thread added: 0x000002f397afbc90
Event: 69.841 Thread 0x000002f397afbc90 Thread added: 0x000002f397aff7a0
Event: 69.842 Thread 0x000002f397aff7a0 Thread added: 0x000002f397b004c0
Event: 69.867 Thread 0x000002f397aff7a0 Thread added: 0x000002f397affe30
Event: 70.501 Thread 0x000002f397affe30 Thread added: 0x000002f397afd040
Event: 70.501 Thread 0x000002f397b004c0 Thread added: 0x000002f397afd6d0


Dynamic libraries:
0x00007ff70c070000 - 0x00007ff70c07e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe0b1b0000 - 0x00007ffe0b405000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe0a820000 - 0x00007ffe0a8e7000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe088b0000 - 0x00007ffe08c50000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe08760000 - 0x00007ffe088aa000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe01b30000 - 0x00007ffe01b48000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe02010000 - 0x00007ffe0202e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe0a2e0000 - 0x00007ffe0a49f000 	C:\Windows\System32\USER32.dll
0x00007ffe08730000 - 0x00007ffe08757000 	C:\Windows\System32\win32u.dll
0x00007ffdebb00000 - 0x00007ffdebd90000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076\COMCTL32.dll
0x00007ffe09840000 - 0x00007ffe0986a000 	C:\Windows\System32\GDI32.dll
0x00007ffe09b90000 - 0x00007ffe09c39000 	C:\Windows\System32\msvcrt.dll
0x00007ffe08600000 - 0x00007ffe08721000 	C:\Windows\System32\gdi32full.dll
0x00007ffe08c60000 - 0x00007ffe08d03000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe0a570000 - 0x00007ffe0a59f000 	C:\Windows\System32\IMM32.DLL
0x00007ffe020d0000 - 0x00007ffe020dc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffdeacb0000 - 0x00007ffdead3d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffd97fb0000 - 0x00007ffd98d40000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe0a4b0000 - 0x00007ffe0a562000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe09fd0000 - 0x00007ffe0a076000 	C:\Windows\System32\sechost.dll
0x00007ffe098f0000 - 0x00007ffe09a09000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe09870000 - 0x00007ffe098e4000 	C:\Windows\System32\WS2_32.dll
0x00007ffe07e80000 - 0x00007ffe07ece000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffe01030000 - 0x00007ffe0103b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffded780000 - 0x00007ffded7b6000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe07e60000 - 0x00007ffe07e74000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe074a0000 - 0x00007ffe074ba000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe02000000 - 0x00007ffe0200a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffdec8b0000 - 0x00007ffdecaf1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe0a8f0000 - 0x00007ffe0ac65000 	C:\Windows\System32\combase.dll
0x00007ffe0a6a0000 - 0x00007ffe0a776000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdef220000 - 0x00007ffdef259000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe08fd0000 - 0x00007ffe09069000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe01b90000 - 0x00007ffe01b9f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffdf6f10000 - 0x00007ffdf6f2f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe09150000 - 0x00007ffe09836000 	C:\Windows\System32\SHELL32.dll
0x00007ffe06430000 - 0x00007ffe06c5b000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffe09a20000 - 0x00007ffe09af3000 	C:\Windows\System32\SHCORE.dll
0x00007ffe09070000 - 0x00007ffe090cd000 	C:\Windows\System32\shlwapi.dll
0x00007ffe08520000 - 0x00007ffe08544000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdf6ef0000 - 0x00007ffdf6f08000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffdfa0f0000 - 0x00007ffdfa100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe05e30000 - 0x00007ffe05f4d000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffe07940000 - 0x00007ffe079a8000 	C:\Windows\system32\mswsock.dll
0x00007ffdeae20000 - 0x00007ffdeae36000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffdf7ba0000 - 0x00007ffdf7bb0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffddde70000 - 0x00007ffdddeb5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffe0a140000 - 0x00007ffe0a2d5000 	C:\Windows\System32\ole32.dll
0x00007ffe07ce0000 - 0x00007ffe07cfc000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffe07400000 - 0x00007ffe07438000 	C:\Windows\system32\rsaenh.dll
0x00007ffe079e0000 - 0x00007ffe07a0b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffe07f70000 - 0x00007ffe07f96000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffe07b60000 - 0x00007ffe07b6c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe06ee0000 - 0x00007ffe06f10000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffe0a130000 - 0x00007ffe0a13a000 	C:\Windows\System32\NSI.dll
0x00007ffdd3780000 - 0x00007ffdd37c9000 	C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna10992244763178060026.dll
0x00007ffe0a810000 - 0x00007ffe0a818000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdec4e0000 - 0x00007ffdec4ea000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ffdeab40000 - 0x00007ffdeab4b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ffe003e0000 - 0x00007ffe003fc000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffe05af0000 - 0x00007ffe05b12000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffe05fb0000 - 0x00007ffe05fe5000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-63116079

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-3c13938d2f00705369675e91812301d6-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.1)
OS uptime: 0 days 3:31 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 1992, Current Mhz: 1792, Mhz Limit: 1792

Memory: 4k page, system-wide physical 8066M (422M free)
TotalPageFile size 14210M (AvailPageFile size 6M)
current process WorkingSet (physical memory assigned to process): 814M, peak: 944M
current process commit charge ("private bytes"): 787M, peak: 973M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
