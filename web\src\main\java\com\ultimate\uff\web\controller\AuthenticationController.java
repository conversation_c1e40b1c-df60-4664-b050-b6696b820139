package com.ultimate.uff.web.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.ultimate.uff.service.UserService;
import com.ultimate.uff.web.config.LoginRequest;

import jakarta.servlet.http.HttpSession;


@RestController
@RequestMapping("/auth")
public class AuthenticationController {

    @Autowired
    private UserService userService;
    @PostMapping("/login")
    public ResponseEntity<?> login(@RequestBody LoginRequest loginRequest, HttpSession session) throws JsonProcessingException {
        try {
            JsonNode userRecord = userService.authenticate(loginRequest.getUsername(), loginRequest.getPassword());
            if (userRecord != null) {
                session.setAttribute("user", userRecord);  // Optional: keep user in session if needed

                // Get privileges list from session (with company info)
                Object privileges = session.getAttribute("privileges");

                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "Login successful");
                response.put("privileges", privileges);  // Only privileges returned, no user object

                return ResponseEntity.ok(response);
            } else {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "Invalid credentials");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
            }
        } catch (UsernameNotFoundException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
        }
    }




    @PostMapping("/logout")
    public ResponseEntity<?> logout(HttpSession session) {
        session.invalidate();
        return ResponseEntity.ok().body("Logout successful");
    }
    
    
    @PostMapping("/set-password")
    public ResponseEntity<?> setPassword(@RequestBody Map<String, String> requestBody) {
        String username = requestBody.get("username");
        String password = requestBody.get("password");
        String confirmPassword = requestBody.get("confirmPassword");

        Map<String, Object> response = userService.setPassword(username, password, confirmPassword);

        if ((boolean) response.get("success")) {
            return ResponseEntity.ok(response);
        } else if ("Passwords do not match".equals(response.get("message"))) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } else if ("User not found".equals(response.get("message"))) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/validate")
    public ResponseEntity<?> validateSession(HttpSession session) {
        JsonNode userRecord = (JsonNode) session.getAttribute("user");
        
        if (userRecord != null) {
            // User is logged in, perform validation
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "User is authenticated");
            response.put("user", userRecord); // You can include user details if needed
            return ResponseEntity.ok(response);
        } else {
            // User is not logged in
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "User is not authenticated");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
        }
    }
}
