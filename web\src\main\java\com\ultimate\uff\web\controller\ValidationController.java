package com.ultimate.uff.web.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.ultimate.uff.validation.DefaultingService;
import com.ultimate.uff.validation.GlobalResponse;
import com.ultimate.uff.validation.IdValidationService;
import com.ultimate.uff.validation.RecordLockService;
import com.ultimate.uff.validation.RecordValidationService;

import jakarta.servlet.http.HttpSession;



@RestController
@RequestMapping("/api/validation")
public class ValidationController {
    @Autowired
    private RecordLockService recordLockService;
    @Autowired
    private IdValidationService idValidationService;
    @Autowired
    private DefaultingService defaultingService;
    @Autowired
    private HttpSession session;
    @GetMapping("/validate-id")
    public GlobalResponse validateId(@RequestParam String tableName, @RequestParam String id) {
        JsonNode userRecord = (JsonNode) session.getAttribute("user");
        String userId = userRecord.get("user").asText();

        // Step 1: Validate the ID first
        GlobalResponse validationResponse = idValidationService.validateId(tableName, id);
        
        // If validation fails, return the error response without locking
        if (!validationResponse.isSuccess()) {
            return validationResponse;
        }

        // Step 2: Check if the record is locked by another user
        if (recordLockService.isRecordLockedByAnotherUser(tableName, id, userId)) {
            return new GlobalResponse(false, "Record is currently locked by another user");
        }

        // Step 3: Lock the record for the current user
        boolean locked = recordLockService.lockRecord(tableName, id, userId);
        if (!locked) {
            return new GlobalResponse(false, "Failed to lock record. It might be locked by another process.");
        }

        // Step 4: Return success message including validation and lock confirmation
        return new GlobalResponse(true, "ID is valid and record locked successfully");
    }

    @Autowired
    private RecordValidationService recordValidationService;

 /*   @PostMapping("/validate-record")
    public String validateRecord(@RequestBody Map<String, Object> request) {
        String tableName = (String) request.get("tableName");
        Map<String, Object> recordDetail = (Map<String, Object>) request.get("recordDetail");
        return recordValidationService.validateRecord(tableName, recordDetail);
    }
    */
    @PostMapping("/apply-defaults")
    public Map<String, Object> applyDefaults(@RequestBody Map<String, Object> payload) {
        String tableName = (String) payload.get("tableName");
        Map<String, Object> recordDetail = (Map<String, Object>) payload.get("recordDetail");
        return defaultingService.applyDefaults(tableName, recordDetail);
    }
}
