package com.ultimate.uff.validation;

import com.ultimate.uff.uql.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Service responsible for executing any custom logic required
 * after a record is fully authorized and moved to the live table.
 * <p>
 * This version includes idempotency check via a `postAuthCompleted` flag.
 */
@Service
public class AfterAuthorizationService {

    private static final Logger logger = LoggerFactory.getLogger(AfterAuthorizationService.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private DatabaseService databaseService;

    /**
     * Applies post-authorization logic for a given record.
     * It checks if a `postAuthCompleted` flag is set to true
     * to ensure idempotent execution (safe to re-trigger).
     *
     * @param tableName    Name of the main table (e.g. "customer")
     * @param recordId     The ID of the authorized record
     * @param recordDetail Full map of the authorized record's fields
     * @return GlobalResponse containing success or failure message
     */
    public GlobalResponse applyPostAuthorizationLogic(String tableName, String recordId, Map<String, Object> recordDetail) {
        logger.info("🛠️ Applying post-authorization logic for table [{}], ID [{}]", tableName, recordId);

        try {
            // ✅ Check if already executed
            if (Boolean.TRUE.equals(recordDetail.get("postAuthCompleted"))) {
                logger.info("🔁 Post-authorization logic already completed for [{}:{}]", tableName, recordId);
                return new GlobalResponse(true, "Post-authorization already completed. Skipping.");
            }

            // ✅ Lookup post-auth config from application_process
            Map<String, Object> config = databaseService.getRecordById("application_process", tableName);
            if (config == null || config.isEmpty()) {
                logger.info("✅ No post-authorization config found for [{}]", tableName);
                return new GlobalResponse(true, "No post-authorization configuration found.");
            }

            List<String> afterAuthClasses = (List<String>) config.get("afterAuth");

            if (afterAuthClasses == null || afterAuthClasses.isEmpty()) {
                logger.info("✅ No post-auth classes listed in config.");
                return new GlobalResponse(true, "No post-authorization handlers configured.");
            }

            for (String className : afterAuthClasses) {
                if (className == null || className.isBlank()) continue;

                try {
                    Class<?> clazz = Class.forName(className);
                    CustomValidator handler = (CustomValidator) applicationContext.getBean(clazz);

                    logger.info("⚙️ Executing post-auth logic via: {}", className);
                    handler.authorize(recordId, recordDetail);

                } catch (ClassNotFoundException e) {
                    logger.warn("⚠️ Post-auth class not found: {}", className);
                } catch (Exception e) {
                    logger.error("❌ Failed handler [{}]: {}", className, e.getMessage());
                    return new GlobalResponse(false, "Handler failed: " + className + " - " + e.getMessage());
                }
            }

            // ✅ Mark record as completed (can also persist if needed)
            recordDetail.put("postAuthCompleted", true);
            logger.info("✅ Post-authorization logic completed successfully for [{}]", recordId);

            return new GlobalResponse(true, "Post-authorization logic executed successfully.");

        } catch (Exception ex) {
            logger.error("❌ Unexpected post-authorization failure: {}", ex.getMessage(), ex);
            return new GlobalResponse(false, "Unexpected error in post-authorization: " + ex.getMessage());
        }
    }
}
