#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1943056 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=16580, tid=15392
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-090397e9d4cbcd20a9f47dc3a18c3716-sock

Host: Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz, 8 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.1)
Time: Tue May 27 14:29:57 2025 Egypt Daylight Time elapsed time: 33.952761 seconds (0d 0h 0m 33s)

---------------  T H R E A D  ---------------

Current thread (0x000002511a3972c0):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=15392, stack(0x0000009fe7100000,0x0000009fe7200000) (1024K)]


Current CompileTask:
C2:33952 14693       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)

Stack: [0x0000009fe7100000,0x0000009fe7200000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x1e0029]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x43340]
C  [KERNEL32.DLL+0x31fd7]
C  [ntdll.dll+0x6d7d0]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002511bbe3090, length=57, elements={
0x00000250bdeec240, 0x00000250bdfa8960, 0x00000250bdfac0e0, 0x00000250d22a4880,
0x00000250d22a54d0, 0x00000250d22ae1c0, 0x00000250d22b4840, 0x00000250d22b8230,
0x00000250d22be2f0, 0x00000251190beb10, 0x00000251190bd760, 0x00000251190bf1a0,
0x00000251190bddf0, 0x00000251190be480, 0x00000251190bf830, 0x00000251190bc3b0,
0x00000251190bca40, 0x000002511a231450, 0x000002511a232170, 0x000002511a3972c0,
0x000002511a231ae0, 0x000002511a2300a0, 0x0000025119d82c30, 0x000002511a232800,
0x000002511a232e90, 0x000002511a233520, 0x000002511a233bb0, 0x000002511a230730,
0x000002511a234240, 0x000002511a230dc0, 0x000002511a2348d0, 0x000002511a234f60,
0x000002511a2355f0, 0x000002511a237030, 0x000002511a235c80, 0x000002511a2369a0,
0x000002511a2376c0, 0x000002511a236310, 0x000002511c76c910, 0x000002511c76d630,
0x000002511c76dcc0, 0x000002511c76b560, 0x000002511c76bbf0, 0x000002511c76f700,
0x000002511c76cfa0, 0x000002511c771e60, 0x000002511c770ab0, 0x000002511c7724f0,
0x00000251190bd0d0, 0x000002511bc4fb10, 0x000002511bc50830, 0x000002511bc4f480,
0x000002511bc51be0, 0x000002511bc52270, 0x000002511bc549d0, 0x000002511bc51550,
0x000002511bc55060
}

Java Threads: ( => current thread )
  0x00000250bdeec240 JavaThread "main"                              [_thread_blocked, id=23904, stack(0x0000009fe5300000,0x0000009fe5400000) (1024K)]
  0x00000250bdfa8960 JavaThread "Reference Handler"          daemon [_thread_blocked, id=32928, stack(0x0000009fe5700000,0x0000009fe5800000) (1024K)]
  0x00000250bdfac0e0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=31392, stack(0x0000009fe5800000,0x0000009fe5900000) (1024K)]
  0x00000250d22a4880 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=31460, stack(0x0000009fe5900000,0x0000009fe5a00000) (1024K)]
  0x00000250d22a54d0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=19816, stack(0x0000009fe5a00000,0x0000009fe5b00000) (1024K)]
  0x00000250d22ae1c0 JavaThread "Service Thread"             daemon [_thread_blocked, id=24928, stack(0x0000009fe5b00000,0x0000009fe5c00000) (1024K)]
  0x00000250d22b4840 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=34100, stack(0x0000009fe5c00000,0x0000009fe5d00000) (1024K)]
  0x00000250d22b8230 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=14904, stack(0x0000009fe5d00000,0x0000009fe5e00000) (1024K)]
  0x00000250d22be2f0 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=20984, stack(0x0000009fe5e00000,0x0000009fe5f00000) (1024K)]
  0x00000251190beb10 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=15776, stack(0x0000009fe5f00000,0x0000009fe6000000) (1024K)]
  0x00000251190bd760 JavaThread "Notification Thread"        daemon [_thread_blocked, id=19628, stack(0x0000009fe6100000,0x0000009fe6200000) (1024K)]
  0x00000251190bf1a0 JavaThread "Active Thread: Equinox Container: 4b4758b5-cacf-44d4-b8cd-29fe0a6f5b40"        [_thread_blocked, id=29140, stack(0x0000009fe6900000,0x0000009fe6a00000) (1024K)]
  0x00000251190bddf0 JavaThread "Refresh Thread: Equinox Container: 4b4758b5-cacf-44d4-b8cd-29fe0a6f5b40" daemon [_thread_blocked, id=4004, stack(0x0000009fe6a00000,0x0000009fe6b00000) (1024K)]
  0x00000251190be480 JavaThread "Framework Event Dispatcher: Equinox Container: 4b4758b5-cacf-44d4-b8cd-29fe0a6f5b40" daemon [_thread_blocked, id=33732, stack(0x0000009fe6c00000,0x0000009fe6d00000) (1024K)]
  0x00000251190bf830 JavaThread "Start Level: Equinox Container: 4b4758b5-cacf-44d4-b8cd-29fe0a6f5b40" daemon [_thread_blocked, id=28764, stack(0x0000009fe6200000,0x0000009fe6300000) (1024K)]
  0x00000251190bc3b0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=32300, stack(0x0000009fe6e00000,0x0000009fe6f00000) (1024K)]
  0x00000251190bca40 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=3560, stack(0x0000009fe6f00000,0x0000009fe7000000) (1024K)]
  0x000002511a231450 JavaThread "Worker-JM"                         [_thread_blocked, id=13372, stack(0x0000009fe6d00000,0x0000009fe6e00000) (1024K)]
  0x000002511a232170 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=15200, stack(0x0000009fe6000000,0x0000009fe6100000) (1024K)]
=>0x000002511a3972c0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=15392, stack(0x0000009fe7100000,0x0000009fe7200000) (1024K)]
  0x000002511a231ae0 JavaThread "Worker-0"                          [_thread_blocked, id=29304, stack(0x0000009fe7200000,0x0000009fe7300000) (1024K)]
  0x000002511a2300a0 JavaThread "Worker-1: Initialize workspace"        [_thread_blocked, id=31720, stack(0x0000009fe7300000,0x0000009fe7400000) (1024K)]
  0x0000025119d82c30 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=15568, stack(0x0000009fe7400000,0x0000009fe7500000) (1024K)]
  0x000002511a232800 JavaThread "Java indexing"              daemon [_thread_in_native, id=13600, stack(0x0000009fe7500000,0x0000009fe7600000) (1024K)]
  0x000002511a232e90 JavaThread "Worker-2"                          [_thread_blocked, id=23768, stack(0x0000009fe7900000,0x0000009fe7a00000) (1024K)]
  0x000002511a233520 JavaThread "Worker-3: Java indexing... "        [_thread_blocked, id=3372, stack(0x0000009fe7a00000,0x0000009fe7b00000) (1024K)]
  0x000002511a233bb0 JavaThread "Worker-4"                          [_thread_blocked, id=34788, stack(0x0000009fe7b00000,0x0000009fe7c00000) (1024K)]
  0x000002511a230730 JavaThread "Thread-2"                   daemon [_thread_in_native, id=15808, stack(0x0000009fe7c00000,0x0000009fe7d00000) (1024K)]
  0x000002511a234240 JavaThread "Thread-3"                   daemon [_thread_in_native, id=20460, stack(0x0000009fe7d00000,0x0000009fe7e00000) (1024K)]
  0x000002511a230dc0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=14844, stack(0x0000009fe7e00000,0x0000009fe7f00000) (1024K)]
  0x000002511a2348d0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=10996, stack(0x0000009fe7f00000,0x0000009fe8000000) (1024K)]
  0x000002511a234f60 JavaThread "Thread-6"                   daemon [_thread_in_native, id=30460, stack(0x0000009fe8000000,0x0000009fe8100000) (1024K)]
  0x000002511a2355f0 JavaThread "Thread-7"                   daemon [_thread_in_vm, id=15488, stack(0x0000009fe8100000,0x0000009fe8200000) (1024K)] _threads_hazard_ptr=0x000002511bbe3090
  0x000002511a237030 JavaThread "Thread-8"                   daemon [_thread_in_native, id=24804, stack(0x0000009fe8200000,0x0000009fe8300000) (1024K)]
  0x000002511a235c80 JavaThread "Thread-9"                   daemon [_thread_in_native, id=33532, stack(0x0000009fe8300000,0x0000009fe8400000) (1024K)]
  0x000002511a2369a0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=14124, stack(0x0000009fe8400000,0x0000009fe8500000) (1024K)]
  0x000002511a2376c0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=32092, stack(0x0000009fe8500000,0x0000009fe8600000) (1024K)]
  0x000002511a236310 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=32180, stack(0x0000009fe8600000,0x0000009fe8700000) (1024K)]
  0x000002511c76c910 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=16388, stack(0x0000009fe8700000,0x0000009fe8800000) (1024K)]
  0x000002511c76d630 JavaThread "Worker-5: Building"                [_thread_blocked, id=27192, stack(0x0000009fe8800000,0x0000009fe8900000) (1024K)]
  0x000002511c76dcc0 JavaThread "Worker-6: Updating workspace"        [_thread_in_native, id=17068, stack(0x0000009fe8900000,0x0000009fe8a00000) (1024K)]
  0x000002511c76b560 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=35672, stack(0x0000009fe7000000,0x0000009fe7100000) (1024K)]
  0x000002511c76bbf0 JavaThread "Compiler Processing Task"   daemon [_thread_blocked, id=16744, stack(0x0000009fe8a00000,0x0000009fe8b00000) (1024K)]
  0x000002511c76f700 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=15264, stack(0x0000009fe8b00000,0x0000009fe8c00000) (1024K)]
  0x000002511c76cfa0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=22780, stack(0x0000009fe8c00000,0x0000009fe8d00000) (1024K)]
  0x000002511c771e60 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=18548, stack(0x0000009fe8d00000,0x0000009fe8e00000) (1024K)]
  0x000002511c770ab0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=24012, stack(0x0000009fe8e00000,0x0000009fe8f00000) (1024K)]
  0x000002511c7724f0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=15724, stack(0x0000009fe8f00000,0x0000009fe9000000) (1024K)]
  0x00000251190bd0d0 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_blocked, id=17636, stack(0x0000009fe9000000,0x0000009fe9100000) (1024K)]
  0x000002511bc4fb10 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=15356, stack(0x0000009fe9100000,0x0000009fe9200000) (1024K)]
  0x000002511bc50830 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=29244, stack(0x0000009fe9200000,0x0000009fe9300000) (1024K)]
  0x000002511bc4f480 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=18776, stack(0x0000009fe9300000,0x0000009fe9400000) (1024K)]
  0x000002511bc51be0 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=12560, stack(0x0000009fe9400000,0x0000009fe9500000) (1024K)]
  0x000002511bc52270 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=34584, stack(0x0000009fe9500000,0x0000009fe9600000) (1024K)]
  0x000002511bc549d0 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=16212, stack(0x0000009fe9600000,0x0000009fe9700000) (1024K)]
  0x000002511bc51550 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=6776, stack(0x0000009fe9700000,0x0000009fe9800000) (1024K)]
  0x000002511bc55060 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=20184, stack(0x0000009fe9800000,0x0000009fe9900000) (1024K)]
Total: 57

Other Threads:
  0x00000250d22a2c50 VMThread "VM Thread"                           [id=33524, stack(0x0000009fe5600000,0x0000009fe5700000) (1024K)]
  0x00000250d21bba00 WatcherThread "VM Periodic Task Thread"        [id=29664, stack(0x0000009fe5500000,0x0000009fe5600000) (1024K)]
  0x00000250bdf0b650 WorkerThread "GC Thread#0"                     [id=20140, stack(0x0000009fe5400000,0x0000009fe5500000) (1024K)]
  0x0000025119563a90 WorkerThread "GC Thread#1"                     [id=33640, stack(0x0000009fe6300000,0x0000009fe6400000) (1024K)]
  0x0000025119999740 WorkerThread "GC Thread#2"                     [id=20936, stack(0x0000009fe6400000,0x0000009fe6500000) (1024K)]
  0x0000025119990510 WorkerThread "GC Thread#3"                     [id=34132, stack(0x0000009fe6500000,0x0000009fe6600000) (1024K)]
  0x00000251199908b0 WorkerThread "GC Thread#4"                     [id=16148, stack(0x0000009fe6600000,0x0000009fe6700000) (1024K)]
  0x0000025119990c50 WorkerThread "GC Thread#5"                     [id=16668, stack(0x0000009fe6700000,0x0000009fe6800000) (1024K)]
  0x0000025119d0cb60 WorkerThread "GC Thread#6"                     [id=5740, stack(0x0000009fe6800000,0x0000009fe6900000) (1024K)]
  0x000002511a0aab90 WorkerThread "GC Thread#7"                     [id=32568, stack(0x0000009fe6b00000,0x0000009fe6c00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  33981 14838 % !   4       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> @ 81 (2135 bytes)
C1 CompilerThread0  33981 14899       2       com.sun.org.apache.xerces.internal.impl.XMLEntityManager::getEntityScanner (54 bytes)
C2 CompilerThread1  33981 14693       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)
C2 CompilerThread2  33981 14861       4       org.eclipse.jdt.internal.core.index.DiskIndex::writeDocumentNumbers (384 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffd98c5ce88] MethodCompileQueue_lock - owner thread: 0x00000250d22be2f0
[0x00007ffd98c5e288] CompiledMethod_lock - owner thread: 0x0000000000000000

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000250d3000000-0x00000250d3ba0000-0x00000250d3ba0000), size 12189696, SharedBaseAddress: 0x00000250d3000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000250d4000000-0x0000025114000000, reserved size: 1073741824
Narrow klass base: 0x00000250d3000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 8066M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 7168K, used 2485K [0x00000000eab00000, 0x00000000eb480000, 0x0000000100000000)
  eden space 6656K, 33% used [0x00000000eab00000,0x00000000ead2de90,0x00000000eb180000)
  from space 512K, 50% used [0x00000000eb180000,0x00000000eb1c0000,0x00000000eb200000)
  to   space 1024K, 0% used [0x00000000eb380000,0x00000000eb380000,0x00000000eb480000)
 ParOldGen       total 497664K, used 497386K [0x00000000c0000000, 0x00000000de600000, 0x00000000eab00000)
  object space 497664K, 99% used [0x00000000c0000000,0x00000000de5baa88,0x00000000de600000)
 Metaspace       used 66876K, committed 68480K, reserved 1114112K
  class space    used 6784K, committed 7488K, reserved 1048576K

Card table byte_map: [0x00000250bd890000,0x00000250bdaa0000] _byte_map_base: 0x00000250bd290000

Marking Bits: (ParMarkBitMap*) 0x00007ffd98c631f0
 Begin Bits: [0x00000250d00e0000, 0x00000250d10e0000)
 End Bits:   [0x00000250d10e0000, 0x00000250d20e0000)

Polling page: 0x00000250bd4a0000

Metaspace:

Usage:
  Non-class:     58.68 MB used.
      Class:      6.63 MB used.
       Both:     65.31 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      59.56 MB ( 93%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       7.31 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      66.88 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  4.09 MB
       Class:  8.56 MB
        Both:  12.66 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.81 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 13.
num_arena_births: 1264.
num_arena_deaths: 16.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1070.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 30.
num_chunks_taken_from_freelist: 4443.
num_chunk_merges: 19.
num_chunk_splits: 2736.
num_chunks_enlarged: 1510.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=11064Kb max_used=11064Kb free=108936Kb
 bounds [0x00000250c8bb0000, 0x00000250c9680000, 0x00000250d00e0000]
CodeHeap 'profiled nmethods': size=120000Kb used=32429Kb max_used=32429Kb free=87570Kb
 bounds [0x00000250c10e0000, 0x00000250c3090000, 0x00000250c8610000]
CodeHeap 'non-nmethods': size=5760Kb used=1446Kb max_used=1573Kb free=4313Kb
 bounds [0x00000250c8610000, 0x00000250c8880000, 0x00000250c8bb0000]
 total_blobs=14789 nmethods=14024 adapters=670
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 31.726 Thread 0x00000250d22be2f0 nmethod 14854 0x00000250c307c790 code [0x00000250c307ca00, 0x00000250c307d4f8]
Event: 31.726 Thread 0x00000250d22be2f0 14857       2       org.eclipse.jdt.ls.core.internal.handlers.ProgressReporterManager$ProgressReporter::<init> (36 bytes)
Event: 31.726 Thread 0x00000250d22be2f0 nmethod 14857 0x00000250c307db10 code [0x00000250c307dcc0, 0x00000250c307de80]
Event: 31.726 Thread 0x00000250d22be2f0 14855       2       org.apache.maven.artifact.DefaultArtifact::setBaseVersion (6 bytes)
Event: 31.726 Thread 0x00000250d22be2f0 nmethod 14855 0x00000250c307df90 code [0x00000250c307e140, 0x00000250c307e288]
Event: 31.728 Thread 0x00000250d22be2f0 14858       1       java.util.concurrent.ThreadPoolExecutor::getThreadFactory (5 bytes)
Event: 31.728 Thread 0x00000250d22be2f0 nmethod 14858 0x00000250c9675e90 code [0x00000250c9676020, 0x00000250c96760e8]
Event: 31.823 Thread 0x00000250d22be2f0 14862       1       org.apache.maven.execution.DefaultMavenExecutionRequest::isUpdateSnapshots (5 bytes)
Event: 31.823 Thread 0x00000250d22be2f0 nmethod 14862 0x00000250c9676190 code [0x00000250c9676320, 0x00000250c96763f0]
Event: 31.825 Thread 0x00000250d22be2f0 14863   !   2       org.eclipse.m2e.core.internal.project.registry.ProjectRegistryManager::getMavenProject (64 bytes)
Event: 31.826 Thread 0x00000250d22be2f0 nmethod 14863 0x00000250c307e390 code [0x00000250c307e5a0, 0x00000250c307e978]
Event: 31.826 Thread 0x00000250d22be2f0 14864       1       java.util.Calendar::setZoneShared (6 bytes)
Event: 31.826 Thread 0x00000250d22be2f0 nmethod 14864 0x00000250c9676490 code [0x00000250c9676620, 0x00000250c96766f0]
Event: 31.826 Thread 0x00000250d22be2f0 14865       1       java.text.DateFormat::setCalendar (6 bytes)
Event: 31.826 Thread 0x00000250d22be2f0 nmethod 14865 0x00000250c9676790 code [0x00000250c9676920, 0x00000250c9676a00]
Event: 32.295 Thread 0x00000250d22be2f0 14866  s    1       org.eclipse.jdt.internal.core.search.processing.JobManager::getProcessingThread (5 bytes)
Event: 32.923 Thread 0x00000250d22be2f0 nmethod 14866 0x00000250c9676a90 code [0x00000250c9676c20, 0x00000250c9676da8]
Event: 32.938 Thread 0x00000250d22be2f0 14867       1       org.eclipse.jdt.ls.core.internal.managers.StandardProjectsManager::isBuildFinished (5 bytes)
Event: 32.938 Thread 0x00000250d22be2f0 nmethod 14867 0x00000250c9676e10 code [0x00000250c9676fa0, 0x00000250c9677070]
Event: 33.002 Thread 0x00000250d22be2f0 14868       1       org.eclipse.core.internal.watson.ElementTree::getDataTree (5 bytes)

GC Heap History (20 events):
Event: 31.355 GC heap before
{Heap before GC invocations=776 (full 3):
 PSYoungGen      total 9216K, used 9088K [0x00000000eab00000, 0x00000000eb580000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 512K, 75% used [0x00000000eb500000,0x00000000eb560000,0x00000000eb580000)
  to   space 1024K, 0% used [0x00000000eb380000,0x00000000eb380000,0x00000000eb480000)
 ParOldGen       total 487424K, used 487315K [0x00000000c0000000, 0x00000000ddc00000, 0x00000000eab00000)
  object space 487424K, 99% used [0x00000000c0000000,0x00000000ddbe4f80,0x00000000ddc00000)
 Metaspace       used 66487K, committed 68032K, reserved 1114112K
  class space    used 6755K, committed 7424K, reserved 1048576K
}
Event: 31.359 GC heap after
{Heap after GC invocations=776 (full 3):
 PSYoungGen      total 9728K, used 1008K [0x00000000eab00000, 0x00000000eb780000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 1024K, 98% used [0x00000000eb380000,0x00000000eb47c010,0x00000000eb480000)
  to   space 2048K, 0% used [0x00000000eb580000,0x00000000eb580000,0x00000000eb780000)
 ParOldGen       total 487936K, used 487760K [0x00000000c0000000, 0x00000000ddc80000, 0x00000000eab00000)
  object space 487936K, 99% used [0x00000000c0000000,0x00000000ddc54200,0x00000000ddc80000)
 Metaspace       used 66487K, committed 68032K, reserved 1114112K
  class space    used 6755K, committed 7424K, reserved 1048576K
}
Event: 31.405 GC heap before
{Heap before GC invocations=777 (full 3):
 PSYoungGen      total 9728K, used 9712K [0x00000000eab00000, 0x00000000eb780000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 1024K, 98% used [0x00000000eb380000,0x00000000eb47c010,0x00000000eb480000)
  to   space 2048K, 0% used [0x00000000eb580000,0x00000000eb580000,0x00000000eb780000)
 ParOldGen       total 487936K, used 487760K [0x00000000c0000000, 0x00000000ddc80000, 0x00000000eab00000)
  object space 487936K, 99% used [0x00000000c0000000,0x00000000ddc54200,0x00000000ddc80000)
 Metaspace       used 66807K, committed 68352K, reserved 1114112K
  class space    used 6780K, committed 7424K, reserved 1048576K
}
Event: 31.408 GC heap after
{Heap after GC invocations=777 (full 3):
 PSYoungGen      total 10752K, used 1592K [0x00000000eab00000, 0x00000000eb780000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 2048K, 77% used [0x00000000eb580000,0x00000000eb70e2b0,0x00000000eb780000)
  to   space 2048K, 0% used [0x00000000eb380000,0x00000000eb380000,0x00000000eb580000)
 ParOldGen       total 488960K, used 488727K [0x00000000c0000000, 0x00000000ddd80000, 0x00000000eab00000)
  object space 488960K, 99% used [0x00000000c0000000,0x00000000ddd45f40,0x00000000ddd80000)
 Metaspace       used 66807K, committed 68352K, reserved 1114112K
  class space    used 6780K, committed 7424K, reserved 1048576K
}
Event: 31.470 GC heap before
{Heap before GC invocations=778 (full 3):
 PSYoungGen      total 10752K, used 10296K [0x00000000eab00000, 0x00000000eb780000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 2048K, 77% used [0x00000000eb580000,0x00000000eb70e2b0,0x00000000eb780000)
  to   space 2048K, 0% used [0x00000000eb380000,0x00000000eb380000,0x00000000eb580000)
 ParOldGen       total 488960K, used 488727K [0x00000000c0000000, 0x00000000ddd80000, 0x00000000eab00000)
  object space 488960K, 99% used [0x00000000c0000000,0x00000000ddd45f40,0x00000000ddd80000)
 Metaspace       used 66832K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.474 GC heap after
{Heap after GC invocations=778 (full 3):
 PSYoungGen      total 10240K, used 1048K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 8704K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb380000)
  from space 1536K, 68% used [0x00000000eb380000,0x00000000eb4863b8,0x00000000eb500000)
  to   space 1536K, 0% used [0x00000000eb500000,0x00000000eb500000,0x00000000eb680000)
 ParOldGen       total 490496K, used 490115K [0x00000000c0000000, 0x00000000ddf00000, 0x00000000eab00000)
  object space 490496K, 99% used [0x00000000c0000000,0x00000000ddea0f40,0x00000000ddf00000)
 Metaspace       used 66832K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.512 GC heap before
{Heap before GC invocations=779 (full 3):
 PSYoungGen      total 10240K, used 9752K [0x00000000eab00000, 0x00000000eb680000, 0x0000000100000000)
  eden space 8704K, 100% used [0x00000000eab00000,0x00000000eb380000,0x00000000eb380000)
  from space 1536K, 68% used [0x00000000eb380000,0x00000000eb4863b8,0x00000000eb500000)
  to   space 1536K, 0% used [0x00000000eb500000,0x00000000eb500000,0x00000000eb680000)
 ParOldGen       total 490496K, used 490115K [0x00000000c0000000, 0x00000000ddf00000, 0x00000000eab00000)
  object space 490496K, 99% used [0x00000000c0000000,0x00000000ddea0f40,0x00000000ddf00000)
 Metaspace       used 66835K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.518 GC heap after
{Heap after GC invocations=779 (full 3):
 PSYoungGen      total 9728K, used 1504K [0x00000000eab00000, 0x00000000eb780000, 0x0000000100000000)
  eden space 8192K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb300000)
  from space 1536K, 97% used [0x00000000eb500000,0x00000000eb678000,0x00000000eb680000)
  to   space 2048K, 0% used [0x00000000eb300000,0x00000000eb300000,0x00000000eb500000)
 ParOldGen       total 491008K, used 490985K [0x00000000c0000000, 0x00000000ddf80000, 0x00000000eab00000)
  object space 491008K, 99% used [0x00000000c0000000,0x00000000ddf7a748,0x00000000ddf80000)
 Metaspace       used 66835K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.560 GC heap before
{Heap before GC invocations=780 (full 3):
 PSYoungGen      total 9728K, used 9696K [0x00000000eab00000, 0x00000000eb780000, 0x0000000100000000)
  eden space 8192K, 100% used [0x00000000eab00000,0x00000000eb300000,0x00000000eb300000)
  from space 1536K, 97% used [0x00000000eb500000,0x00000000eb678000,0x00000000eb680000)
  to   space 2048K, 0% used [0x00000000eb300000,0x00000000eb300000,0x00000000eb500000)
 ParOldGen       total 491008K, used 490985K [0x00000000c0000000, 0x00000000ddf80000, 0x00000000eab00000)
  object space 491008K, 99% used [0x00000000c0000000,0x00000000ddf7a748,0x00000000ddf80000)
 Metaspace       used 66835K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.563 GC heap after
{Heap after GC invocations=780 (full 3):
 PSYoungGen      total 9216K, used 988K [0x00000000eab00000, 0x00000000eb600000, 0x0000000100000000)
  eden space 8192K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb300000)
  from space 1024K, 96% used [0x00000000eb300000,0x00000000eb3f7018,0x00000000eb400000)
  to   space 1536K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb600000)
 ParOldGen       total 492544K, used 492273K [0x00000000c0000000, 0x00000000de100000, 0x00000000eab00000)
  object space 492544K, 99% used [0x00000000c0000000,0x00000000de0bc448,0x00000000de100000)
 Metaspace       used 66835K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.605 GC heap before
{Heap before GC invocations=781 (full 3):
 PSYoungGen      total 9216K, used 9180K [0x00000000eab00000, 0x00000000eb600000, 0x0000000100000000)
  eden space 8192K, 100% used [0x00000000eab00000,0x00000000eb300000,0x00000000eb300000)
  from space 1024K, 96% used [0x00000000eb300000,0x00000000eb3f7018,0x00000000eb400000)
  to   space 1536K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb600000)
 ParOldGen       total 492544K, used 492273K [0x00000000c0000000, 0x00000000de100000, 0x00000000eab00000)
  object space 492544K, 99% used [0x00000000c0000000,0x00000000de0bc448,0x00000000de100000)
 Metaspace       used 66835K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.609 GC heap after
{Heap after GC invocations=781 (full 3):
 PSYoungGen      total 9216K, used 992K [0x00000000eab00000, 0x00000000eb580000, 0x0000000100000000)
  eden space 8192K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb300000)
  from space 1024K, 96% used [0x00000000eb480000,0x00000000eb578000,0x00000000eb580000)
  to   space 1024K, 0% used [0x00000000eb300000,0x00000000eb300000,0x00000000eb400000)
 ParOldGen       total 493568K, used 493133K [0x00000000c0000000, 0x00000000de200000, 0x00000000eab00000)
  object space 493568K, 99% used [0x00000000c0000000,0x00000000de193470,0x00000000de200000)
 Metaspace       used 66835K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.642 GC heap before
{Heap before GC invocations=782 (full 3):
 PSYoungGen      total 9216K, used 9184K [0x00000000eab00000, 0x00000000eb580000, 0x0000000100000000)
  eden space 8192K, 100% used [0x00000000eab00000,0x00000000eb300000,0x00000000eb300000)
  from space 1024K, 96% used [0x00000000eb480000,0x00000000eb578000,0x00000000eb580000)
  to   space 1024K, 0% used [0x00000000eb300000,0x00000000eb300000,0x00000000eb400000)
 ParOldGen       total 493568K, used 493133K [0x00000000c0000000, 0x00000000de200000, 0x00000000eab00000)
  object space 493568K, 99% used [0x00000000c0000000,0x00000000de193470,0x00000000de200000)
 Metaspace       used 66835K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.645 GC heap after
{Heap after GC invocations=782 (full 3):
 PSYoungGen      total 9216K, used 1024K [0x00000000eab00000, 0x00000000eb600000, 0x0000000100000000)
  eden space 8192K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb300000)
  from space 1024K, 100% used [0x00000000eb300000,0x00000000eb400000,0x00000000eb400000)
  to   space 1536K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb600000)
 ParOldGen       total 494080K, used 494018K [0x00000000c0000000, 0x00000000de280000, 0x00000000eab00000)
  object space 494080K, 99% used [0x00000000c0000000,0x00000000de270a80,0x00000000de280000)
 Metaspace       used 66835K, committed 68416K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.677 GC heap before
{Heap before GC invocations=783 (full 3):
 PSYoungGen      total 9216K, used 9214K [0x00000000eab00000, 0x00000000eb600000, 0x0000000100000000)
  eden space 8192K, 99% used [0x00000000eab00000,0x00000000eb2ff830,0x00000000eb300000)
  from space 1024K, 100% used [0x00000000eb300000,0x00000000eb400000,0x00000000eb400000)
  to   space 1536K, 0% used [0x00000000eb480000,0x00000000eb480000,0x00000000eb600000)
 ParOldGen       total 494080K, used 494018K [0x00000000c0000000, 0x00000000de280000, 0x00000000eab00000)
  object space 494080K, 99% used [0x00000000c0000000,0x00000000de270a80,0x00000000de280000)
 Metaspace       used 66837K, committed 68480K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.680 GC heap after
{Heap after GC invocations=783 (full 3):
 PSYoungGen      total 9728K, used 1304K [0x00000000eab00000, 0x00000000eb600000, 0x0000000100000000)
  eden space 8192K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb300000)
  from space 1536K, 84% used [0x00000000eb480000,0x00000000eb5c6008,0x00000000eb600000)
  to   space 1536K, 0% used [0x00000000eb300000,0x00000000eb300000,0x00000000eb480000)
 ParOldGen       total 495104K, used 494890K [0x00000000c0000000, 0x00000000de380000, 0x00000000eab00000)
  object space 495104K, 99% used [0x00000000c0000000,0x00000000de34aa80,0x00000000de380000)
 Metaspace       used 66837K, committed 68480K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.716 GC heap before
{Heap before GC invocations=784 (full 3):
 PSYoungGen      total 9728K, used 9496K [0x00000000eab00000, 0x00000000eb600000, 0x0000000100000000)
  eden space 8192K, 100% used [0x00000000eab00000,0x00000000eb300000,0x00000000eb300000)
  from space 1536K, 84% used [0x00000000eb480000,0x00000000eb5c6008,0x00000000eb600000)
  to   space 1536K, 0% used [0x00000000eb300000,0x00000000eb300000,0x00000000eb480000)
 ParOldGen       total 495104K, used 494890K [0x00000000c0000000, 0x00000000de380000, 0x00000000eab00000)
  object space 495104K, 99% used [0x00000000c0000000,0x00000000de34aa80,0x00000000de380000)
 Metaspace       used 66840K, committed 68480K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.720 GC heap after
{Heap after GC invocations=784 (full 3):
 PSYoungGen      total 9216K, used 800K [0x00000000eab00000, 0x00000000eb500000, 0x0000000100000000)
  eden space 8192K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb300000)
  from space 1024K, 78% used [0x00000000eb300000,0x00000000eb3c8000,0x00000000eb400000)
  to   space 1024K, 0% used [0x00000000eb400000,0x00000000eb400000,0x00000000eb500000)
 ParOldGen       total 496128K, used 496026K [0x00000000c0000000, 0x00000000de480000, 0x00000000eab00000)
  object space 496128K, 99% used [0x00000000c0000000,0x00000000de466a88,0x00000000de480000)
 Metaspace       used 66840K, committed 68480K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.744 GC heap before
{Heap before GC invocations=785 (full 3):
 PSYoungGen      total 9216K, used 8992K [0x00000000eab00000, 0x00000000eb500000, 0x0000000100000000)
  eden space 8192K, 100% used [0x00000000eab00000,0x00000000eb300000,0x00000000eb300000)
  from space 1024K, 78% used [0x00000000eb300000,0x00000000eb3c8000,0x00000000eb400000)
  to   space 1024K, 0% used [0x00000000eb400000,0x00000000eb400000,0x00000000eb500000)
 ParOldGen       total 496128K, used 496026K [0x00000000c0000000, 0x00000000de480000, 0x00000000eab00000)
  object space 496128K, 99% used [0x00000000c0000000,0x00000000de466a88,0x00000000de480000)
 Metaspace       used 66841K, committed 68480K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}
Event: 31.748 GC heap after
{Heap after GC invocations=785 (full 3):
 PSYoungGen      total 7680K, used 1024K [0x00000000eab00000, 0x00000000eb800000, 0x0000000100000000)
  eden space 6656K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eb180000)
  from space 1024K, 100% used [0x00000000eb400000,0x00000000eb500000,0x00000000eb500000)
  to   space 2560K, 0% used [0x00000000eb180000,0x00000000eb180000,0x00000000eb400000)
 ParOldGen       total 497152K, used 496882K [0x00000000c0000000, 0x00000000de580000, 0x00000000eab00000)
  object space 497152K, 99% used [0x00000000c0000000,0x00000000de53ca88,0x00000000de580000)
 Metaspace       used 66841K, committed 68480K, reserved 1114112K
  class space    used 6782K, committed 7488K, reserved 1048576K
}

Dll operation events (12 events):
Event: 0.013 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.194 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.222 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.237 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.247 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.257 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.285 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.360 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.487 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.060 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna1517277019010960529.dll
Event: 5.860 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 5.864 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 31.337 Thread 0x00000251190bf1a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000250c922f918 relative=0x0000000000000438
Event: 31.337 Thread 0x00000251190bf1a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000250c922f918 method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 62 c2
Event: 31.337 Thread 0x00000251190bf1a0 DEOPT PACKING pc=0x00000250c922f918 sp=0x0000009fe69fefb0
Event: 31.337 Thread 0x00000251190bf1a0 DEOPT UNPACKING pc=0x00000250c8663aa2 sp=0x0000009fe69feeb0 mode 2
Event: 31.375 Thread 0x00000251190bf1a0 DEOPT PACKING pc=0x00000250c2ee7c7c sp=0x0000009fe69feed0
Event: 31.375 Thread 0x00000251190bf1a0 DEOPT UNPACKING pc=0x00000250c8664242 sp=0x0000009fe69fe358 mode 0
Event: 31.383 Thread 0x00000251190bf1a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000250c93844ac relative=0x000000000000080c
Event: 31.383 Thread 0x00000251190bf1a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000250c93844ac method=java.util.AbstractMap.equals(Ljava/lang/Object;)Z @ 11 c2
Event: 31.383 Thread 0x00000251190bf1a0 DEOPT PACKING pc=0x00000250c93844ac sp=0x0000009fe69feec0
Event: 31.383 Thread 0x00000251190bf1a0 DEOPT UNPACKING pc=0x00000250c8663aa2 sp=0x0000009fe69fee78 mode 2
Event: 31.388 Thread 0x00000251190bf1a0 DEOPT PACKING pc=0x00000250c2ee7c7c sp=0x0000009fe69feed0
Event: 31.388 Thread 0x00000251190bf1a0 DEOPT UNPACKING pc=0x00000250c8664242 sp=0x0000009fe69fe358 mode 0
Event: 31.400 Thread 0x00000251190bf1a0 DEOPT PACKING pc=0x00000250c2ee7c7c sp=0x0000009fe69fed90
Event: 31.400 Thread 0x00000251190bf1a0 DEOPT UNPACKING pc=0x00000250c8664242 sp=0x0000009fe69fe218 mode 0
Event: 31.752 Thread 0x000002511a232800 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000250c8fbdd9c relative=0x000000000000113c
Event: 31.752 Thread 0x000002511a232800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000250c8fbdd9c method=org.eclipse.jdt.internal.compiler.classfmt.FieldInfo.createField([B[IIJ)Lorg/eclipse/jdt/internal/compiler/classfmt/FieldInfo; @ 254 c2
Event: 31.752 Thread 0x000002511a232800 DEOPT PACKING pc=0x00000250c8fbdd9c sp=0x0000009fe75fee10
Event: 31.752 Thread 0x000002511a232800 DEOPT UNPACKING pc=0x00000250c8663aa2 sp=0x0000009fe75fed78 mode 2
Event: 31.753 Thread 0x000002511a232800 DEOPT PACKING pc=0x00000250c2d7186b sp=0x0000009fe75feda0
Event: 31.753 Thread 0x000002511a232800 DEOPT UNPACKING pc=0x00000250c8664242 sp=0x0000009fe75fe240 mode 0

Classes loaded (20 events):
Event: 26.810 Loading class java/util/TreeMap$NavigableSubMap$SubMapEntryIterator
Event: 26.811 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator
Event: 26.812 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator done
Event: 26.812 Loading class java/util/TreeMap$NavigableSubMap$SubMapEntryIterator done
Event: 26.832 Loading class java/util/concurrent/CompletableFuture$UniApply
Event: 26.835 Loading class java/util/concurrent/CompletableFuture$UniApply done
Event: 26.837 Loading class java/util/concurrent/CompletableFuture$UniExceptionally
Event: 26.838 Loading class java/util/concurrent/CompletableFuture$UniExceptionally done
Event: 27.402 Loading class java/lang/UnsupportedClassVersionError
Event: 27.402 Loading class java/lang/UnsupportedClassVersionError done
Event: 28.340 Loading class org/xml/sax/SAXNotRecognizedException
Event: 28.340 Loading class org/xml/sax/SAXNotRecognizedException done
Event: 28.340 Loading class org/xml/sax/SAXNotSupportedException
Event: 28.340 Loading class org/xml/sax/SAXNotSupportedException done
Event: 28.347 Loading class com/sun/org/apache/xerces/internal/util/URI
Event: 28.348 Loading class com/sun/org/apache/xerces/internal/util/URI done
Event: 28.350 Loading class com/sun/org/apache/xerces/internal/xni/XNIException
Event: 28.350 Loading class com/sun/org/apache/xerces/internal/xni/XNIException done
Event: 28.353 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLParseException
Event: 28.354 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLParseException done

Classes unloaded (8 events):
Event: 4.977 Thread 0x00000250d22a2c50 Unloading class 0x00000250d41a4400 'java/lang/invoke/LambdaForm$MH+0x00000250d41a4400'
Event: 4.977 Thread 0x00000250d22a2c50 Unloading class 0x00000250d41a4000 'java/lang/invoke/LambdaForm$MH+0x00000250d41a4000'
Event: 4.977 Thread 0x00000250d22a2c50 Unloading class 0x00000250d41a3c00 'java/lang/invoke/LambdaForm$MH+0x00000250d41a3c00'
Event: 4.977 Thread 0x00000250d22a2c50 Unloading class 0x00000250d41a3800 'java/lang/invoke/LambdaForm$MH+0x00000250d41a3800'
Event: 4.977 Thread 0x00000250d22a2c50 Unloading class 0x00000250d41a3400 'java/lang/invoke/LambdaForm$BMH+0x00000250d41a3400'
Event: 4.977 Thread 0x00000250d22a2c50 Unloading class 0x00000250d41a3000 'java/lang/invoke/LambdaForm$DMH+0x00000250d41a3000'
Event: 4.977 Thread 0x00000250d22a2c50 Unloading class 0x00000250d41a1c00 'java/lang/invoke/LambdaForm$DMH+0x00000250d41a1c00'
Event: 17.359 Thread 0x00000250d22a2c50 Unloading class 0x00000250d4650c00 'java/lang/invoke/LambdaForm$DMH+0x00000250d4650c00'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 28.005 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab96308}> (0x00000000eab96308) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 28.238 Thread 0x000002511a233bb0 Implicit null exception at 0x00000250c8e8c009 to 0x00000250c8e8c115
Event: 28.268 Thread 0x000002511a233520 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eab770e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eab770e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 28.336 Thread 0x000002511a233bb0 Implicit null exception at 0x00000250c95730cb to 0x00000250c9573b00
Event: 28.578 Thread 0x00000251190bd0d0 Implicit null exception at 0x00000250c8e6aca4 to 0x00000250c8e6afd0
Event: 29.557 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb24a5c0}> (0x00000000eb24a5c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 29.761 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eadb55f0}> (0x00000000eadb55f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 29.944 Thread 0x00000251190bd0d0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eaf49068}: Found class java.lang.Object, but interface was expected> (0x00000000eaf49068) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 30.170 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eb0b2bb8}> (0x00000000eb0b2bb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 30.542 Thread 0x000002511a233bb0 Implicit null exception at 0x00000250c9590e67 to 0x00000250c9591504
Event: 30.542 Thread 0x00000251190bd0d0 Implicit null exception at 0x00000250c9590e67 to 0x00000250c9591504
Event: 30.559 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabc9110}> (0x00000000eabc9110) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 30.707 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eabbb330}> (0x00000000eabbb330) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 30.723 Thread 0x000002511a233bb0 Implicit null exception at 0x00000250c8e29de0 to 0x00000250c8e29e1a
Event: 30.723 Thread 0x00000251190bd0d0 Implicit null exception at 0x00000250c8e29de0 to 0x00000250c8e29e1a
Event: 30.738 Thread 0x00000251190bd0d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb0174e0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x00000000eb0174e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 30.739 Thread 0x000002511a233bb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb004b98}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x00000000eb004b98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 31.159 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaceb518}> (0x00000000eaceb518) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 31.274 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab8aea0}> (0x00000000eab8aea0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 31.364 Thread 0x000002511a232800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab5e928}> (0x00000000eab5e928) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 31.359 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.405 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.408 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.470 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.474 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.512 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.518 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.560 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.563 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.605 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.609 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.642 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.645 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.677 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.680 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.716 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.720 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 31.744 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 31.748 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 33.748 Executing VM operation: Cleanup

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a48110
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a60910
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a62190
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a62a10
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a63010
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a63710
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a65710
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a69110
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a73190
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1a82d90
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1aa1210
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1acbc10
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1accd10
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1ad5c90
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1af3710
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1b02110
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1b03c10
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1b05510
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1b1c710
Event: 17.406 Thread 0x00000250d22a2c50 flushing  nmethod 0x00000250c1b30410

Events (20 events):
Event: 31.520 Thread 0x000002511c76d630 Thread added: 0x000002511bc55d80
Event: 31.520 Thread 0x000002511c76d630 Thread added: 0x000002511bc53cb0
Event: 31.520 Thread 0x000002511c76d630 Thread added: 0x000002511bc52f90
Event: 31.520 Thread 0x000002511c76d630 Thread added: 0x000002511bc54340
Event: 31.520 Thread 0x000002511c76d630 Thread added: 0x000002511bc56aa0
Event: 31.534 Thread 0x000002511bc54340 Thread exited: 0x000002511bc54340
Event: 31.534 Thread 0x000002511bc53cb0 Thread exited: 0x000002511bc53cb0
Event: 31.534 Thread 0x000002511bc52f90 Thread exited: 0x000002511bc52f90
Event: 31.535 Thread 0x000002511bc55d80 Thread exited: 0x000002511bc55d80
Event: 31.535 Thread 0x000002511bc56aa0 Thread exited: 0x000002511bc56aa0
Event: 31.728 Thread 0x000002511c76d630 Thread added: 0x000002511bc55d80
Event: 31.728 Thread 0x000002511c76d630 Thread added: 0x000002511bc52f90
Event: 31.728 Thread 0x000002511c76d630 Thread added: 0x000002511bc53620
Event: 31.729 Thread 0x000002511c76d630 Thread added: 0x000002511bc56aa0
Event: 31.729 Thread 0x000002511c76d630 Thread added: 0x000002511bc53cb0
Event: 31.739 Thread 0x000002511bc53620 Thread exited: 0x000002511bc53620
Event: 31.739 Thread 0x000002511bc55d80 Thread exited: 0x000002511bc55d80
Event: 31.739 Thread 0x000002511bc52f90 Thread exited: 0x000002511bc52f90
Event: 31.739 Thread 0x000002511bc53cb0 Thread exited: 0x000002511bc53cb0
Event: 31.739 Thread 0x000002511bc56aa0 Thread exited: 0x000002511bc56aa0


Dynamic libraries:
0x00007ff70c070000 - 0x00007ff70c07e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe0b1b0000 - 0x00007ffe0b405000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe0a820000 - 0x00007ffe0a8e7000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe088b0000 - 0x00007ffe08c50000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe08760000 - 0x00007ffe088aa000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe01b30000 - 0x00007ffe01b48000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe02010000 - 0x00007ffe0202e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe0a2e0000 - 0x00007ffe0a49f000 	C:\Windows\System32\USER32.dll
0x00007ffdebb00000 - 0x00007ffdebd90000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076\COMCTL32.dll
0x00007ffe08730000 - 0x00007ffe08757000 	C:\Windows\System32\win32u.dll
0x00007ffe09840000 - 0x00007ffe0986a000 	C:\Windows\System32\GDI32.dll
0x00007ffe09b90000 - 0x00007ffe09c39000 	C:\Windows\System32\msvcrt.dll
0x00007ffe08600000 - 0x00007ffe08721000 	C:\Windows\System32\gdi32full.dll
0x00007ffe08c60000 - 0x00007ffe08d03000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe0a570000 - 0x00007ffe0a59f000 	C:\Windows\System32\IMM32.DLL
0x00007ffe020d0000 - 0x00007ffe020dc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffdeacb0000 - 0x00007ffdead3d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffd97fb0000 - 0x00007ffd98d40000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe0a4b0000 - 0x00007ffe0a562000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe09fd0000 - 0x00007ffe0a076000 	C:\Windows\System32\sechost.dll
0x00007ffe098f0000 - 0x00007ffe09a09000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe09870000 - 0x00007ffe098e4000 	C:\Windows\System32\WS2_32.dll
0x00007ffe07e80000 - 0x00007ffe07ece000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffded780000 - 0x00007ffded7b6000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe01030000 - 0x00007ffe0103b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffe07e60000 - 0x00007ffe07e74000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe074a0000 - 0x00007ffe074ba000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe02000000 - 0x00007ffe0200a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffdec8b0000 - 0x00007ffdecaf1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe0a8f0000 - 0x00007ffe0ac65000 	C:\Windows\System32\combase.dll
0x00007ffe0a6a0000 - 0x00007ffe0a776000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdef220000 - 0x00007ffdef259000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe08fd0000 - 0x00007ffe09069000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe01b90000 - 0x00007ffe01b9f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffdf6f10000 - 0x00007ffdf6f2f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe09150000 - 0x00007ffe09836000 	C:\Windows\System32\SHELL32.dll
0x00007ffe06430000 - 0x00007ffe06c5b000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffe09a20000 - 0x00007ffe09af3000 	C:\Windows\System32\SHCORE.dll
0x00007ffe09070000 - 0x00007ffe090cd000 	C:\Windows\System32\shlwapi.dll
0x00007ffe08520000 - 0x00007ffe08544000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdf6ef0000 - 0x00007ffdf6f08000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffdfa0f0000 - 0x00007ffdfa100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe05e30000 - 0x00007ffe05f4d000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffe07940000 - 0x00007ffe079a8000 	C:\Windows\system32\mswsock.dll
0x00007ffdeae20000 - 0x00007ffdeae36000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffdf7ba0000 - 0x00007ffdf7bb0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffddde70000 - 0x00007ffdddeb5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffe0a140000 - 0x00007ffe0a2d5000 	C:\Windows\System32\ole32.dll
0x00007ffe07ce0000 - 0x00007ffe07cfc000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffe07400000 - 0x00007ffe07438000 	C:\Windows\system32\rsaenh.dll
0x00007ffe079e0000 - 0x00007ffe07a0b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffe07f70000 - 0x00007ffe07f96000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffe07b60000 - 0x00007ffe07b6c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe06ee0000 - 0x00007ffe06f10000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffe0a130000 - 0x00007ffe0a13a000 	C:\Windows\System32\NSI.dll
0x00007ffdd3950000 - 0x00007ffdd3999000 	C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna1517277019010960529.dll
0x00007ffe0a810000 - 0x00007ffe0a818000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdec4e0000 - 0x00007ffdec4ea000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ffdeab40000 - 0x00007ffdeab4b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ffe003e0000 - 0x00007ffe003fc000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffe05af0000 - 0x00007ffe05b12000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-63116079

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-090397e9d4cbcd20a9f47dc3a18c3716-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.1)
OS uptime: 0 days 3:31 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 1992, Current Mhz: 1792, Mhz Limit: 1792

Memory: 4k page, system-wide physical 8066M (423M free)
TotalPageFile size 14210M (AvailPageFile size 23M)
current process WorkingSet (physical memory assigned to process): 799M, peak: 802M
current process commit charge ("private bytes"): 784M, peak: 789M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
