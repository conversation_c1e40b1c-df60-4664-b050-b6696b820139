package com.ultimate.uff.teller;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Type extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private final FieldValue<String> type = new FieldValue<>(null);
    private final FieldValue<String> applicationName = new FieldValue<>(null);
    private final FieldValue<String> function = new FieldValue<>(null);
    private final List<Field> fieldList = new ArrayList<>();

    public Type(Map<String, Object> fieldDetail) {
        type.setValue((String) fieldDetail.get("type"));
        applicationName.setValue((String) fieldDetail.get("applicationName"));
        function.setValue((String) fieldDetail.get("function"));
        Object fieldObj = fieldDetail.get("field");
        if (fieldObj instanceof List<?>) {
            for (Object item : (List<?>) fieldObj) {
                if (item instanceof Map) {
                    fieldList.add(new Field((Map<String, Object>) item));
                }
            }
        }
        for (Map.Entry<String, Object> entry : fieldDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type.getValue());
        map.put("applicationName", applicationName.getValue());
        map.put("function", function.getValue());
        List<Map<String, Object>> fieldMapList = new ArrayList<>();
        for (Field item : fieldList) {
            fieldMapList.add(item.toMap());
        }
        map.put("field", fieldMapList);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
            case "type", "applicationName", "function", "field" -> true;
            default -> false;
        };
    }

    public FieldValue<String> getType() { return type; }
    public FieldValue<String> getApplicationName() { return applicationName; }
    public FieldValue<String> getFunction() { return function; }
    public List<Field> getFieldList() { return fieldList; }
}