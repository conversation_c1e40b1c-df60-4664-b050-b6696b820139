package com.ultimate.uff.multi.thread.jobs;

import com.ultimate.uff.multi.thread.enums.ServiceStatus;
import com.ultimate.uff.multi.thread.generatedClass.Jobs;
import com.ultimate.uff.multi.thread.services.LoggerClass;
import com.ultimate.uff.validation.ReadOnlyDatabaseService;

import org.springframework.context.ApplicationContext;
//import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
//import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;


/*
    private final ReadOnlyDatabaseService readOnlyDatabaseService;
    private final RecordInsertionService recordInsertionService;

    public JobService(ReadOnlyDatabaseService readOnlyDatabaseService, RecordInsertionService recordInsertionService) {
        this.readOnlyDatabaseService = readOnlyDatabaseService;
        this.recordInsertionService = recordInsertionService;
    }

    public JobExecutable loadJob(String jobId) throws Exception {
        // تحميل بيانات الجوب من جدول jobs
        Map<String, Object> recordData = readOnlyDatabaseService.getRecordById("jobs", jobId);
        Jobs job = new Jobs(recordData);

        String className = job.getClassName();

        // تحميل الكلاس ديناميكيًا
        Class<?> clazz = Class.forName(className);
        if (!JobExecutable.class.isAssignableFrom(clazz)) {
            throw new IllegalArgumentException("Class does not implement JobExecutable: " + className);
        }

        JobExecutable jobExecutable = (JobExecutable) clazz.getDeclaredConstructor().newInstance();
       // jobExecutable.setJobId(job);

        return jobExecutable;
    }

    public JobExecutable fetchNextJob(String serviceId) {
        // محاكاة لجلب job - لاحقاً ستتغير لتأخذ من قاعدة البيانات
        return new JobExecutable() {
            @Override
            public JobResult execute() {
                // تنفيذ وهمي
                return new JobResult("job-123", true, "تم تنفيذ المهمة بنجاح");
            }

            @Override
            public String getJobId() {
                return "job-123";
            }
        };
    }

    *******public void updateJobStatus(String jobId, JobResult result) {
        try {
            Map<String, Object> recordData = readOnlyDatabaseService.getRecordById("jobs", jobId);
            Jobs job = new Jobs(recordData);

           // job.setStatus(result.getStatus());
            job.setFinishedAt(result.getFinishedAt());
            job.setErrorMessage(result.getErrorMessage());

           // recordInsertionService.updateRecord("jobs", jobId, job.toMap());
        } catch (Exception e) {
            throw new RuntimeException("Failed to update job status: " + e.getMessage(), e);
        }
    }

    public void markJobFailed(String jobId, String errorMessage) {
        JobResult result = new JobResult();
        result.setStatus("FAILED");
       // result.setFinishedAtNow();
        result.setErrorMessage(errorMessage);
        updateJobStatus(jobId, result);
    }
    public JobResult executeJob(String jobId, String payload) {
        // منطق تنفيذ الـ Job بناءً على jobId و payload
        // يمكن إضافة أكثر من منطق لتحديد نوع الـ Job وعملياته
        return new JobResult(jobId, "Executed successfully");
    }*************/


    /*private final ReadOnlyDatabaseService readOnlyDatabaseService;
    private final LoggerClass loggerClass;
    private final JobLogService jobLogService;

    public JobService(ReadOnlyDatabaseService readOnlyDatabaseService,JobLogService jobLogService,LoggerClass loggerClass) {
        this.readOnlyDatabaseService = readOnlyDatabaseService;
        this.jobLogService=jobLogService;
        this.loggerClass=loggerClass;
    }*/

    @Service
public class JobService {
    private final ReadOnlyDatabaseService readOnlyDatabaseService;
    private final LoggerClass loggerClass;
    private final JobLogService jobLogService;
    private final ApplicationContext context;

    private ServiceStatus serviceStatus2;

    public JobService(ReadOnlyDatabaseService readOnlyDatabaseService,
                    JobLogService jobLogService,
                    LoggerClass loggerClass,
                    ApplicationContext context) {
        this.readOnlyDatabaseService = readOnlyDatabaseService;
        this.jobLogService = jobLogService;
        this.loggerClass = loggerClass;
        this.context = context;
    }
    public JobExecutable loadJob(String jobId) throws Exception {
        Map<String, Object> jobData = readOnlyDatabaseService.getRecordById("jobs", jobId);
        if (jobData == null) {
            throw new IllegalArgumentException("Job not found for ID: " + jobId);
        }
        Jobs job = new Jobs(jobData);
        String className = job.getClassName();
        Class<?> clazz = Class.forName(className);
        if (!JobExecutable.class.isAssignableFrom(clazz)) {
            throw new IllegalArgumentException("Class does not implement JobExecutable: " + className);
        }
        JobExecutable jobExec = (JobExecutable) context.getBean(clazz);
        if (jobExec instanceof ConfigurableJob cj) {
            cj.setJobData(jobData);
        }
        return jobExec;
    }


    public Map<String, List<JobExecutable>> getExecutableJobsGroupedByTask(String serviceId) {
        String serviceStatus = getServiceStatus(serviceId);
        if ("START".equalsIgnoreCase(serviceStatus) || "AUTO".equalsIgnoreCase(serviceStatus)) {

        Map<String, List<JobExecutable>> taskJobExecutables = new HashMap<>();
        List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks", Map.of("serviceId", serviceId));

        for (String taskId : taskIds) {
            Map<String, Object> taskData = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
            if (taskData == null) continue;

            List<JobExecutable> taskExecutables = processTask(taskData);
            if (!taskExecutables.isEmpty()) {
                taskJobExecutables.put(taskId, taskExecutables);
            }
        }
        return taskJobExecutables;
    }
    loggerClass.getLogger().debug("Skipping job execution for service '{}' with status: {}", serviceId, serviceStatus);
    return Collections.emptyMap();
    }

    /*public Map<String, List<JobExecutable>> getExecutableJobsGroupedByTask(String serviceId) {
        String serviceStatus = getServiceStatus(serviceId);
        if (!"START".equalsIgnoreCase(serviceStatus)){ //&& !"AUTO".equalsIgnoreCase(serviceStatus)) {
            loggerClass.getLogger().debug("Skipping job execution for service '{}' with status: {}", serviceId, serviceStatus);
            return Collections.emptyMap();
        }
        Map<String, List<JobExecutable>> taskJobExecutables = new HashMap<>();
        List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks", Map.of("serviceId", serviceId));

        for (String taskId : taskIds) {
            Map<String, Object> taskData = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
            if (taskData == null) continue;

            List<JobExecutable> taskExecutables = processTask(taskData);
            if (!taskExecutables.isEmpty()) {
                taskJobExecutables.put(taskId, taskExecutables);
            }
        }
        return taskJobExecutables;
    }
*/
    private List<JobExecutable> processTask(Map<String, Object> taskData) {
        List<JobExecutable> executables = new ArrayList<>();
        List<Map<String, Object>> jobDetails = (List<Map<String, Object>>) taskData.get("JobDetails");
        if (jobDetails == null) return executables;

        Map<String, String> dependencyMap = new HashMap<>();
        for (Map<String, Object> jobEntry : jobDetails) {
            String jobId = (String) jobEntry.get("jobId");
            String dependencyJobId = (String) jobEntry.get("dependencyJobId");
            if (jobId != null) {
                dependencyMap.put(jobId, dependencyJobId);
            }
        }

        List<String> sortedJobIds = topologicalSort(dependencyMap);
        if (sortedJobIds == null) {
            loggerClass.getLogger().error("Cycle detected in job dependencies for task: {}", taskData.get("ID"));
            return executables;
        }

        for (String jobId : sortedJobIds) {
            String depJobId = dependencyMap.get(jobId);
            if (depJobId != null && !isDependencySuccess(depJobId)) {
                loggerClass.getLogger().debug("Job {} skipped: dependency job {} not successful", jobId, depJobId);
                continue;
            }
            try {
                JobExecutable jobExecutable = loadJob(jobId);
                executables.add(jobExecutable);
            } catch (Exception e) {
                loggerClass.getLogger().error("Failed to load job {}: {}", jobId, e.getMessage());
            }
        }
        return executables;
    }
    private boolean isDependencySuccess(String jobId) {
        try {
            Optional<Map<String, Object>> logOpt = jobLogService.getLatestLog("JOB", jobId);
            if (logOpt.isEmpty()) {
                loggerClass.getLogger().debug("No log found for dependency job {}. Assuming not executed yet.", jobId);
                return false;
            }
            String action = (String) logOpt.get().get("action");
            return "SUCCESS".equalsIgnoreCase(action);
        } catch (Exception e) {
            loggerClass.getLogger().error("Error checking dependency status for job {}: {}", jobId, e.getMessage());
            return false;
        }
    }
    
    private List<String> topologicalSort(Map<String, String> dependencyMap) {
        Map<String, List<String>> adjList = new HashMap<>();
        Map<String, Integer> inDegree = new HashMap<>();

        for (Map.Entry<String, String> entry : dependencyMap.entrySet()) {
            String job = entry.getKey();
            String dep = entry.getValue();

            if (dep != null) {
                adjList.computeIfAbsent(dep, k -> new ArrayList<>()).add(job);
                inDegree.put(job, inDegree.getOrDefault(job, 0) + 1);
                inDegree.putIfAbsent(dep, 0);
            } else {
                inDegree.putIfAbsent(job, 0);
            }
        }

        Queue<String> queue = new LinkedList<>();
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) queue.add(entry.getKey());
        }

        List<String> sorted = new ArrayList<>();
        while (!queue.isEmpty()) {
            String node = queue.poll();
            sorted.add(node);
            for (String neighbor : adjList.getOrDefault(node, Collections.emptyList())) {
                inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                if (inDegree.get(neighbor) == 0) queue.add(neighbor);
            }
        }
        return sorted.size() == inDegree.size() ? sorted : null;
    }

    /*public String getServiceStatus(String serviceId) {
        Map<String, Object> record = readOnlyDatabaseService.getRecordById("service", serviceId);
        if (record == null) return null;
        com.ultimate.uff.multi.thread.generatedClass.Service service=new com.ultimate.uff.multi.thread.generatedClass.Service(record);
        //return (String) record.get(service.getStatus());
        return service.getStatus();
    }*/
    public String getServiceStatus(String serviceId) {
        Map<String, Object> record = readOnlyDatabaseService.getRecordById("service", serviceId);
        if (record == null) return null;
        return (String) record.get("status");
    }
}


        /*public Map<String, List<JobExecutable>> getExecutableJobsGroupedByTask(String serviceId) {
        Map<String, List<JobExecutable>> taskJobExecutables = new HashMap<>();
        List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks", Map.of("serviceId", serviceId));

        for (String taskId : taskIds) {
            Map<String, Object> taskData = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
            if (taskData == null) continue;

            List<JobExecutable> taskExecutables = processTask(taskData);
            if (!taskExecutables.isEmpty()) {
                taskJobExecutables.put(taskId, taskExecutables);
            }
        }
        return taskJobExecutables;
    }*/

    /*public String getServiceStatus(String serviceId) {
        try {
            Map<String, Object> record = readOnlyDatabaseService.getRecordById("service", serviceId);
            if (record == null) {
                loggerClass.getLogger().error("Service '{}' is not found )" + serviceId);
                return ("Service "+serviceId + " Not Found");
            }
                com.ultimate.uff.multi.thread.generatedClass.Service service=new com.ultimate.uff.multi.thread.generatedClass.Service(record);
                return (String) record.get(service.getStatus());
            
        } catch (Exception e) {
            loggerClass.getLogger().error("Error when get Service" + serviceId,e.getMessage(),e);
            return ("Error when get Service"+serviceId + e.getMessage()+e);
        }
    }*/
    /*public Map<String, List<JobExecutable>> getExecutableJobsGroupedByTask(String serviceId) {
        Map<String, Object> service = readOnlyDatabaseService.getRecordById("service", serviceId);
        if (service == null || (!"START".equals(service.get("status")) && !"AUTO".equals(service.get("status")))) {
            loggerClass.getLogger().warn("Service '{}' is not active (status={}). Skipping job execution.", serviceId, service != null ? service.get("status") : "N/A");
            return Collections.emptyMap();
        }
    
        // ثم نتابع كما في السابق...
    }*/
    
    /*public JobExecutable loadJob(String jobId) throws Exception {
        Map<String, Object> jobData = readOnlyDatabaseService.getRecordById("jobs", jobId);
        if (jobData == null) {
            throw new IllegalArgumentException("Job not found for ID: " + jobId);
        }
        Jobs job = new Jobs(jobData);
        String className = job.getClassName();
        Class<?> clazz = Class.forName(className);

       // jobData.put("jobId", jobId);
        //String className = (String) jobData.get("className");
       // Class<?> clazz = Class.forName(className);

        if (!JobExecutable.class.isAssignableFrom(clazz)) {
            throw new IllegalArgumentException("Class does not implement JobExecutable: " + className);
        }

        Constructor<?> constructor = clazz.getConstructor(Map.class);
        return (JobExecutable) constructor.newInstance(jobData);
    }*/

        /*  private boolean isDependencySuccess(String jobId) {
        try {
            Optional<Map<String, Object>> logOpt = jobLogService.getLatestLog("JOB", jobId);
            return logOpt
                .map(log -> "SUCCESS".equalsIgnoreCase((String) log.get("action")))
                .orElse(false);
        } catch (Exception e) {
            loggerClass.getLogger().error("Error checking dependency status for job {}: {}", jobId, e.getMessage());
            return false;
        }
    }*/
     //Constructor<?> constructor2 = clazz.getConstructor(Map.class, ReadOnlyDatabaseService.class, DatabaseService.class, JobLogService.class);
    //return (JobExecutable) constructor.newInstance(jobData, readOnlyDatabaseService, databaseService, jobLogService);

    
    /*public List<JobExecutable> getPendingJobsForService(String serviceId) {
        List<JobExecutable> allExecutables = new ArrayList<>();

        List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks", Map.of("serviceId", serviceId));

        for (String taskId : taskIds) {
            Map<String, Object> taskData = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
            if (taskData == null) continue;

            List<JobExecutable> taskExecutables = processTask(taskData);
            allExecutables.addAll(taskExecutables);
        }

        return allExecutables;
    }

    private List<JobExecutable> processTask(Map<String, Object> taskData) {
        List<JobExecutable> executables = new ArrayList<>();

        List<Map<String, Object>> jobDetails = (List<Map<String, Object>>) taskData.get("JobDetailes");
        if (jobDetails == null) return executables;

        Map<String, String> dependencyMap = new HashMap<>();
        Set<String> jobIds = new HashSet<>();

        for (Map<String, Object> jobEntry : jobDetails) {
            String jobId = (String) jobEntry.get("jobId");
            String dependencyJobId = (String) jobEntry.get("dependencyJobId");
            if (jobId != null) {
                dependencyMap.put(jobId, dependencyJobId);
                jobIds.add(jobId);
                if (dependencyJobId != null) jobIds.add(dependencyJobId);
            }
        }

        List<String> sortedJobIds = topologicalSort(dependencyMap);
        if (sortedJobIds == null) {
            loggerClass.getLogger().error("Cycle detected in job dependencies for task: {}", taskData.get("ID"));
            return executables;
        }

        for (String jobId : sortedJobIds) {
            Map<String, Object> jobData = readOnlyDatabaseService.getRecordById("jobs", jobId);
            if (jobData == null) continue;

            String status = (String) jobData.get("status");
            if (!"PENDING".equals(status)) continue;

            String depJobId = dependencyMap.get(jobId);
            if (depJobId != null) {
                Map<String, Object> depData = readOnlyDatabaseService.getRecordById("jobs", depJobId);
                if (depData == null || !"SUCCESS".equals(depData.get("status"))) {
                    loggerClass.getLogger().warn("Job {} skipped: dependency {} not ready or not successful", jobId, depJobId);
                    continue;
                }
            }

            try {
                JobExecutable jobExecutable = loadJob(jobId);
                executables.add(jobExecutable);
            } catch (Exception e) {
                loggerClass.getLogger().error("Failed to load job {}: {}", jobId, e.getMessage());
            }
        }

        return executables;
    }

    private List<String> topologicalSort(Map<String, String> dependencyMap) {
        Map<String, List<String>> adjList = new HashMap<>();
        Map<String, Integer> inDegree = new HashMap<>();

        for (Map.Entry<String, String> entry : dependencyMap.entrySet()) {
            String job = entry.getKey();
            String dep = entry.getValue();

            if (dep != null) {
                adjList.computeIfAbsent(dep, k -> new ArrayList<>()).add(job);
                inDegree.put(job, inDegree.getOrDefault(job, 0) + 1);
                inDegree.putIfAbsent(dep, 0);
            } else {
                inDegree.putIfAbsent(job, 0);
            }
        }

        Queue<String> queue = new LinkedList<>();
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.add(entry.getKey());
            }
        }

        List<String> sorted = new ArrayList<>();
        while (!queue.isEmpty()) {
            String node = queue.poll();
            sorted.add(node);

            for (String neighbor : adjList.getOrDefault(node, Collections.emptyList())) {
                inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                if (inDegree.get(neighbor) == 0) {
                    queue.add(neighbor);
                }
            }
        }

        return sorted.size() == inDegree.size() ? sorted : null;
    }*/


    /*

    public List<JobExecutable> getPendingJobsForService(String serviceId) {
        List<JobExecutable> executables = new ArrayList<>();

        List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks", Map.of("serviceId", serviceId));
          // Map of jobId -> dependencyJobId (or null)
    Map<String, String> dependencyMap = new HashMap<>();
    Set<String> allJobIds = new HashSet<>();

    for (String taskId : taskIds) {
        Map<String, Object> taskData = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
        if (taskData == null) continue;

        List<Map<String, Object>> jobDetails = (List<Map<String, Object>>) taskData.get("JobDetailes");
        if (jobDetails == null) continue;

        for (Map<String, Object> jobEntry : jobDetails) {
            String jobId = (String) jobEntry.get("jobId");
            String dependencyJobId = (String) jobEntry.get("dependencyJobId");
            if (jobId != null) {
                dependencyMap.put(jobId, dependencyJobId);
                allJobIds.add(jobId);
                if (dependencyJobId != null) allJobIds.add(dependencyJobId);
            }
        }
    }

    // Topological Sort
    List<String> sortedJobIds = topologicalSort(dependencyMap);
    if (sortedJobIds == null) {
        loggerClass.getLogger().debug("Dependency job {} is not ready");
        System.err.println("Cycle detected in job dependencies.");
        return executables;
    }

    for (String jobId : sortedJobIds) {
        Map<String, Object> jobData = readOnlyDatabaseService.getRecordById("jobs", jobId);
        if (jobData == null) continue;

        String status = (String) jobData.get("status");
        if (!"PENDING".equals(status)) continue;

        String depJobId = dependencyMap.get(jobId);
        if (depJobId != null) {
            Map<String, Object> depData = readOnlyDatabaseService.getRecordById("jobs", depJobId);
            if (depData == null || !"SUCCESS".equals(depData.get("status"))) {
                System.out.printf("Job %s skipped: dependency job %s not found.%n", jobId, depJobId);
                loggerClass.getLogger().warn("Dependency job {} is not ready",jobId, depJobId);

                continue; // dependency not met
            }
            String depStatus = (String) depData.get("status");
            if (!"SUCCESS".equals(depStatus)) {
                loggerClass.getLogger().debug("Job %s skipped: dependency job %s has not succeeded yet (status = %s).%n", jobId, depJobId, depStatus);
                System.out.printf("Job %s skipped: dependency job %s has not succeeded yet (status = %s).%n", jobId, depJobId, depStatus);
                continue;
            }
        }
        try {
            JobExecutable jobExecutable = loadJob(jobId);
            executables.add(jobExecutable);
        } catch (Exception e) {
            loggerClass.getLogger().debug("Failed to load job: " + jobId + " - " + e.getMessage());
            System.err.println("Failed to load job: " + jobId + " - " + e.getMessage());
            e.printStackTrace();
        }
    }

    return executables;
}
private List<String> topologicalSort(Map<String, String> dependencyMap) {
    Map<String, List<String>> adjList = new HashMap<>();
    Map<String, Integer> inDegree = new HashMap<>();

    for (Map.Entry<String, String> entry : dependencyMap.entrySet()) {
        String job = entry.getKey();
        String dep = entry.getValue();

        adjList.computeIfAbsent(dep, k -> new ArrayList<>()).add(job);
        inDegree.put(job, inDegree.getOrDefault(job, 0) + 1);
        inDegree.putIfAbsent(dep, 0);
    }

    Queue<String> queue = new LinkedList<>();
    for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
        if (entry.getValue() == 0) {
            queue.add(entry.getKey());
        }
    }

    List<String> sorted = new ArrayList<>();
    while (!queue.isEmpty()) {
        String node = queue.poll();
        if (node == null) continue;

        sorted.add(node);

        for (String neighbor : adjList.getOrDefault(node, Collections.emptyList())) {
            inDegree.put(neighbor, inDegree.get(neighbor) - 1);
            if (inDegree.get(neighbor) == 0) {
                queue.add(neighbor);
            }
        }
    }
    if (sorted.size() != inDegree.size()) {
        return null; // cycle detected
    }
    return sorted;
}*/



    /*   public List<JobExecutable> getPendingJobsForService(String serviceId) {
        List<JobExecutable> executables = new ArrayList<>();

        // احصل على جميع مهام الخدمة المرتبطة بالخدمة المحددة
        List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks", Map.of("serviceId", serviceId));

        for (String taskId : taskIds) {
            // البحث في جدول jobs عن جميع الوظائف المرتبطة بهذا taskId
            List<String> jobIds = readOnlyDatabaseService.getRecordIdsByCriteria("jobs", Map.of("taskId", taskId));

            for (String jobId : jobIds) {
                Map<String, Object> jobData = readOnlyDatabaseService.getRecordById("jobs", jobId);
                if (jobData == null) continue;
                String status = (String) jobData.get("status");
                if ("PENDING".equals(status)) {
                    try {
                        JobExecutable jobExecutable = loadJob(jobId);
                        executables.add(jobExecutable);
                    } catch (Exception e) {
                        System.err.println("Failed to load job: " + jobId + " - " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            }
        }
        return executables;
    }*/

        /*for (String taskId : taskIds) {
            Map<String, Object> taskData = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
            if (taskData == null) continue;
    
            List<Map<String, Object>> jobDetails = (List<Map<String, Object>>) taskData.get("JobDetailes");
            if (jobDetails == null) continue;
            
            for (Map<String, Object> jobEntry : jobDetails) {
                String jobId = (String) jobEntry.get("jobId");
                String dependencyJobId = (String) jobEntry.get("dependencyJobId");
    
                Map<String, Object> jobData = readOnlyDatabaseService.getRecordById("jobs", jobId);
                if (jobData == null) continue;
    
                String status = (String) jobData.get("status");
                if (!"PENDING".equals(status)) continue;
    
                // تحقق من الاعتماديات إن وجدت
                if (dependencyJobId != null) {
                    Map<String, Object> depJobData = readOnlyDatabaseService.getRecordById("jobs", dependencyJobId);
                    if (depJobData == null) continue;
    
                    String depStatus = (String) depJobData.get("status");
                    if (!"SUCCESS".equals(depStatus)) {
                        // الاعتماد غير مكتمل بعد
                        continue;
                    }
                }
                try {
                    JobExecutable jobExecutable = loadJob(jobId);
                    executables.add(jobExecutable);
                } catch (Exception e) {
                    System.err.println("Failed to load job: " + jobId + " - " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }
        return executables;
*/

        /*for (String taskId : taskIds) {
            Map<String, Object> taskData = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
            if (taskData == null) continue;

            String jobId = (String) taskData.get("jobId");
            String dependencyJobId = (String) taskData.getOrDefault("dependencyJobId", null);

            if (jobId == null || jobId.isBlank()) continue;

            Map<String, Object> jobData = readOnlyDatabaseService.getRecordById("jobs", jobId);
            if (jobData == null) continue;

            String status = (String) jobData.get("status");
            if (!"PENDING".equalsIgnoreCase(status)) continue;

            // تحقق من تنفيذ job المعتمَد عليها إن وُجدت
            if (dependencyJobId != null && !dependencyJobId.isBlank()) {
                Map<String, Object> dependencyJobData = readOnlyDatabaseService.getRecordById("jobs", dependencyJobId);
                if (dependencyJobData == null) continue;

                String depStatus = (String) dependencyJobData.get("status");
                if (!"SUCCESS".equalsIgnoreCase(depStatus)) {
                    // تأجيل تنفيذ هذه الـ job حتى تنجح المعتمدة عليها
                    continue;
                }
            }

            try {
                JobExecutable executable = loadJob(jobId);
                executables.add(executable);
            } catch (Exception e) {
                System.err.println("❌ Failed to load job: " + jobId + " - " + e.getMessage());
                e.printStackTrace();
            }
        }

        return executables;
    }*/

    /*public JobExecutable loadJob(String jobId) throws Exception {
        Map<String, Object> recordData = readOnlyDatabaseService.getRecordById("jobs", jobId);
        Jobs job = new Jobs(recordData);

       // Map<String, Object> jobData = (Map<String, Object>) recordData.get("data");
        //jobData.put("jobId", jobId); // تأكيد وجود jobId في البيانات

         // إنشاء نسخة من كل البيانات داخل JSON_ROW_DETAIL
        Map<String, Object> jobData = new HashMap<>((Map<String, Object>) recordData.get("data"));
        //jobData.put("jobId", jobId); // إضافي - وليس ضروريًا إذا كان موجود في JSON
        jobData.put("_jobRowId", jobId); 

    
        String className = job.getClassName();
        if (className == null) {
            throw new IllegalArgumentException("Missing 'className' in job data: " + jobId);
        }
        Class<?> clazz = Class.forName(className);
    
        if (!JobExecutable.class.isAssignableFrom(clazz)) {
            throw new IllegalArgumentException("Class does not implement JobExecutable: " + className);
        }
    
       // Constructor<?> constructor = clazz.getConstructor(Map.class);
        //return (JobExecutable) constructor.newInstance(jobData);
        Constructor<?> constructor = clazz.getConstructor(String.class, Map.class);
        return (JobExecutable) constructor.newInstance(jobId, jobData);
    }*/

    /*public List<JobExecutable> getPendingJobsForService(String serviceId) {
        List<JobExecutable> executables = new ArrayList<>();
        // الحصول على كل serviceTasks المرتبطة بالخدمة
        List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks", Map.of("serviceId", serviceId));

        for (String taskId : taskIds) {
            //Map<String, Object> taskRecord = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
            //List<String> jobIds = (List<String>) ((Map<String, Object>) taskRecord.get("data")).get("jobs");
            List<String> jobIds = readOnlyDatabaseService.getRecordIdsByCriteria("jobs", Map.of("taskId", taskId));


            for (String jobId : jobIds) {
                Map<String, Object> jobRecord = readOnlyDatabaseService.getRecordById("jobs", jobId);
                System.out.println("jobRecord: " + jobRecord);
                Map<String, Object> jobData = (Map<String, Object>) jobRecord.get("data");


                if ("PENDING".equals(jobData.get("status"))) {
                    try {
                        JobExecutable jobExecutable = loadJob(jobId);
                        executables.add(jobExecutable);
                    } catch (Exception e) {
                        // سجل الخطأ أو تجاهل هذا الـ job
                        System.err.println("Failed to load job: " + jobId + " - " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            }
        }
        return executables;
    }*/

    /*public List<JobExecutable> getPendingJobsForService(String serviceId) {
        List<JobExecutable> executables = new ArrayList<>();
        // احصل على جميع مهام الخدمة المرتبطة بالخدمة المحددة
        List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks", Map.of("serviceId", serviceId));
        for (String taskId : taskIds) {
            Map<String, Object> taskRecord = readOnlyDatabaseService.getRecordById("serviceTasks", taskId);
            System.out.println("🔍 taskRecord: " + taskRecord);
            List<String> jobIds = (List<String>) ((Map<String, Object>) taskRecord.get("data")).get("jobId");
            Map<String, Object> data = (Map<String, Object>) taskRecord.get("data");
            System.out.println("📦 Extracted data: " + data);
            //List<String> jobIds = readOnlyDatabaseService.getRecordIdsByCriteria("jobs", Map.of());

            /*  for (String jobId : jobIds) {
                Map<String, Object> jobRecord = readOnlyDatabaseService.getRecordById("jobs", jobId);
                System.out.println("jobRecord: " + jobRecord);
                Map<String, Object> jobData = (Map<String, Object>) jobRecord.get("data");*/

        /*for (String taskId : taskIds) {
            // البحث في جدول jobs عن جميع الوظائف المرتبطة بهذا taskId
            List<String> jobIds = readOnlyDatabaseService.getRecordIdsByCriteria("jobs", Map.of("taskId", taskId));
**********************
            for (String jobId : jobIds) {
                Map<String, Object> jobData = readOnlyDatabaseService.getRecordById("jobs", jobId);
                if (jobData == null) continue;
                String status = (String) jobData.get("status");
                if ("PENDING".equals(status)) {
                    try {
                        JobExecutable jobExecutable = loadJob(jobId);
                        executables.add(jobExecutable);
                    } catch (Exception e) {
                        System.err.println("Failed to load job: " + jobId + " - " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            }
        }
        return executables;
    }*/

    /*public JobExecutable loadJob(String jobId) throws Exception {
        Map<String, Object> recordData = readOnlyDatabaseService.getRecordById("jobs", jobId);
        Jobs job = new Jobs(recordData);

        Map<String, Object> jobData = (Map<String, Object>) recordData.get("data");
        jobData.put("jobId", jobId); // تأكيد وجود jobId في البيانات

        String className = job.getClassName();
        Class<?> clazz = Class.forName(className);

        if (!JobExecutable.class.isAssignableFrom(clazz)) {
            throw new IllegalArgumentException("Class does not implement JobExecutable: " + className);
        }

        JobExecutable jobExecutable = (JobExecutable) clazz.getDeclaredConstructor().newInstance();
        return jobExecutable;

        // إذا كنت تحتاج تمرير بيانات الـ job نفسها إلى الكائن، يمكن تعديل الواجهة أو استخدام constructor مخصص

        return jobExecutable;
    }*/
    

