package com.ultimate.uff.teller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.CustomIdValidator;

import jakarta.servlet.http.HttpSession;

@Component
public class tellerIdValidator implements CustomIdValidator {

    @Autowired
    private DatabaseService databaseService;

    @Autowired
    private HttpSession session;

    @Override
    public String validate(String id) {
        String user2 = null;

        // Get "user" object from session
        Object user = session.getAttribute("user");

        if (user instanceof JsonNode jsonUser) {
            JsonNode rowIdNode = jsonUser.get("user");

            if (rowIdNode != null && !rowIdNode.isNull()) {
                user2 = rowIdNode.asText();

                // Create criteria to fetch userTill records
                Map<String, Object> userTillCriteria = Map.of("user", Map.of("EQ", user2));
                ArrayNode resultArray = databaseService.getRecordsByCriteria("userTill", userTillCriteria);

                if (resultArray != null && resultArray.size() > 0) {
                    JsonNode firstRow = resultArray.get(0);
                    String jsonDetail = firstRow.get("JSON_ROW_DETAIL").asText();

                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        JsonNode detailNode = objectMapper.readTree(jsonDetail);
                        String status = detailNode.get("status").asText();

                        System.out.println("Status: " + status);

                        if ("open".equalsIgnoreCase(status)) {
                            return "Validation successful";
                        } else {
                            return "Validation failed: status is not open";
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                        return "Validation failed: Error parsing userTill JSON";
                    }

                } else {
                    return "Validation failed: No matching userTill record";
                }
            }
        }

        return "Validation failed: Invalid session or missing user ID";
    }
}
