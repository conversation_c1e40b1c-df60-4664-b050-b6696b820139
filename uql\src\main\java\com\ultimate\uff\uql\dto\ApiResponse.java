package com.ultimate.uff.uql.dto;

public class ApiResponse {
    private String status;
    private String message;
    private Object data;

    // Default constructor
    public ApiResponse() {
    }

    // Constructor for status, message, and data
    public ApiResponse(String status, String message, Object data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    // Constructor for status and message
    public ApiResponse(String status, String message) {
        this.status = status;
        this.message = message;
    }

    // Constructor for status and data
    public ApiResponse(String status, Object data) {
        this.status = status;
        this.data = data;
    }

    // Getters and setters
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
