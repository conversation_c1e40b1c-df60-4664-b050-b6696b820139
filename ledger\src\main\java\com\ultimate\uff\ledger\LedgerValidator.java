package com.ultimate.uff.ledger;

import com.ultimate.uff.account.LedgerEntry.AccountLedgerEntry;
import com.ultimate.uff.validation.ValidationResponse;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Service
public class LedgerValidator {

    private static final String BASE_CURRENCY = "YER";
    private static final Set<String> VALID_CURRENCY_CODES = Set.of("USD", "EUR", "YER", "GBP", "SAR");

    public ValidationResponse prcValidateLedgerEntries(List<AccountLedgerEntry> ledgerEntries) {
        ValidationResponse response = new ValidationResponse();
        double totalAmountLcy = 0.0;

        for (int i = 0; i < ledgerEntries.size(); i++) {
            AccountLedgerEntry entry = ledgerEntries.get(i);
            String prefix = "Entry " + (i + 1);

            // 1. Required Fields
            if (isNullOrEmpty(entry.getTransactionReference())) {
                response.addFieldError(prefix, "MISSING_TRANSACTION_REFERENCE");
            }
            if (isNullOrEmpty(entry.getAccountNumber())) {
                response.addFieldError(prefix, "MISSING_ACCOUNT_NUMBER");
            }
            if (isNullOrEmpty(entry.getBookingDate())) {
                response.addFieldError(prefix, "MISSING_BOOKING_DATE");
            }
            if (isNullOrEmpty(entry.getValueDate())) {
                response.addFieldError(prefix, "MISSING_VALUE_DATE");
            }
            if (isNullOrEmpty(entry.getCurrencyCode())) {
                response.addFieldError(prefix, "MISSING_CURRENCY_CODE");
            }
            if (entry.getAmountLcy() == null) {
                response.addFieldError(prefix, "MISSING_AMOUNT_LCY");
            }
            if (isNullOrEmpty(entry.getEntryDrCr())) {
                response.addFieldError(prefix, "MISSING_ENTRY_MARKER");
            }

            String currencyCode = entry.getCurrencyCode();

            // 2. FCY check
            if (currencyCode != null && !BASE_CURRENCY.equals(currencyCode)) {
                if (entry.getAmountFcy() == null) {
                    response.addFieldError(prefix, "MISSING_AMOUNT_FCY_FOR_FOREIGN_CURRENCY_" + currencyCode);
                }
            }

            // 3. Currency validity
            if (currencyCode != null && !VALID_CURRENCY_CODES.contains(currencyCode)) {
                response.addFieldError(prefix, "INVALID_CURRENCY_CODE_" + currencyCode);
            }

            // 4. Account/Customer Validity
            if (!isValidAccount(entry.getAccountNumber())) {
                response.addFieldError(prefix, "INVALID_ACCOUNT_NUMBER_" + entry.getAccountNumber());
            }
            if (entry.getCustomerNumber() != null && !isValidCustomer(entry.getCustomerNumber())) {
                response.addFieldError(prefix, "INVALID_CUSTOMER_NUMBER_" + entry.getCustomerNumber());
            }

            // 5. Marker and Amount Sign Check
            String marker = entry.getEntryDrCr() != null ? entry.getEntryDrCr().toUpperCase() : null;
            Double amountLcy = entry.getAmountLcy() != null ? entry.getAmountLcy() : 0.0;

            if ("DR".equals(marker)) {
                if (amountLcy >= 0) {
                    response.addFieldError(prefix, "DEBIT_ENTRY_AMOUNT_SHOULD_BE_NEGATIVE");
                }
            } else if ("CR".equals(marker)) {
                if (amountLcy <= 0) {
                    response.addFieldError(prefix, "CREDIT_ENTRY_AMOUNT_SHOULD_BE_POSITIVE");
                }
            } else {
                response.addFieldError(prefix, "INVALID_ENTRY_MARKER_" + marker);
            }

            // 6. Logical date check
            if (!isNullOrEmpty(entry.getValueDate()) && !isNullOrEmpty(entry.getBookingDate())) {
                if (isValueDateBeforeBookingDate(entry.getValueDate(), entry.getBookingDate())) {
                    response.addFieldError(prefix, "VALUEDATE_BEFORE_BOOKINGDATE");
                }
            }

            // 7. Accumulate amountLcy
            totalAmountLcy += amountLcy;
        }

        // 8. LCY total balancing
        if (Math.abs(totalAmountLcy) > 0.01) {
            response.addFieldError("LedgerTotal", "DEBIT_CREDIT_LCY_NOT_BALANCED");
        }

        return response;
    }

    private boolean isValueDateBeforeBookingDate(String valueDateStr, String bookingDateStr) {
        try {
            Instant valueDate = Instant.parse(valueDateStr);
            Instant bookingDate = Instant.parse(bookingDateStr);
            return valueDate.isBefore(bookingDate);
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isNullOrEmpty(Object value) {
        return value == null || (value instanceof String && ((String) value).trim().isEmpty());
    }

    private boolean isValidAccount(String accountNumber) {
        return accountNumber != null;
    }

    private boolean isValidCustomer(String customerNumber) {
        return customerNumber != null;
    }
}
