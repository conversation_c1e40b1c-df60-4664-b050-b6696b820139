package com.ultimate.uff.uql.util.mssql;

import java.util.List;

public class OpenJsonChainBuilder {

    public record OpenJsonChainResult(String sql, String lastAlias, String lastField, boolean isLastArray) {}

    public static OpenJsonChainResult build(String jsonField, List<String> pathParts, boolean lastFieldIsArray) {
        StringBuilder sql = new StringBuilder();
        String aliasPrefix = "lvl";
    
        for (int i = 0; i < pathParts.size(); i++) {
            String part = pathParts.get(i);
            String cleanPart = part.replace("[]", "");  // ✅ Add this!
    
            if (i == 0) {
                sql.append("OPENJSON(JSON_QUERY([").append(jsonField)
                   .append("], '$.").append(cleanPart).append("')) AS ").append(aliasPrefix).append(i).append("\n");
    
            } else if (i < pathParts.size() - 1) {
                sql.append("CROSS APPLY OPENJSON(JSON_QUERY(").append(aliasPrefix).append(i - 1)
                   .append(".value), '$.").append(cleanPart).append("') AS ").append(aliasPrefix).append(i).append("\n");
    
            } else {
                if (lastFieldIsArray) {
                    sql.append("CROSS APPLY OPENJSON(")
                       .append(aliasPrefix).append(i - 1).append(".value, '$.").append(cleanPart)
                       .append("') AS ").append(aliasPrefix).append(i).append("\n");
                } else {
                    sql.append("CROSS APPLY OPENJSON(JSON_QUERY(")
                       .append(aliasPrefix).append(i - 1).append(".value))\nWITH (")
                       .append(cleanPart).append(" NVARCHAR(MAX) '$.").append(cleanPart).append("') AS ")
                       .append(aliasPrefix).append(i).append("\n");
                }
            }
        }
    
        String lastAlias = aliasPrefix + (pathParts.size() - 1);
        String lastField = pathParts.get(pathParts.size() - 1);
    
        return new OpenJsonChainResult(sql.toString().trim(), lastAlias, lastField, lastFieldIsArray);
    }
    
}
