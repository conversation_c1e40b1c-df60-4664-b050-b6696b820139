package com.ultimate.uff.uql.service;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.fasterxml.jackson.databind.node.ArrayNode;

@SpringBootTest(classes = com.ultimate.uff.main.UffMainApplication.class)
public class EdgeCaseCriteriaTests extends BaseCriteriaTest {

    @Test
    public void test_CT_NonExistentField() {
        runJsonNegativeTest("{ \"ghostField\": { \"CT\": \"value1\" } }", "ghostField", "value1");
    }

    @Test
    public void test_DNEW_Single() {
        runJsonNegativeTest("{ \"singleField\": { \"DNEW\": \"atchValue\" } }", "singleField", "noMatchValue");
    }

    @Test
    public void test_EmptyCriteria_ReturnsAll() {
        Map<String, Object> empty = Map.of();
        ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, empty);
        assertEquals(9, result.size()); // All inserted rows
    }

    @Test
    public void test_MixedCase_Operator_EQ() {
        runJsonTest("{ \"singleField\": { \"eQ\": \"singleValue\" } }", "singleValue");
    }

    @Test
    public void test_EQ_EmptyArrayField_ShouldNotMatch() {
        runJsonNegativeTest("{ \"emptyArray[]\": { \"EQ\": \"anything\" } }", "emptyArray", "anything");
    }

    @Test
    public void test_EQ_ArrayWithNulls() {
        runJsonTest("{ \"arrayWithNulls[]\": { \"EQ\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_SortOnNestedField_ShouldNotCrash() {
        Map<String, Object> criteria = Map.of("_sort", List.of("nestedArray[].nestedKey ASC"));
        ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
        assertEquals(9, result.size()); // All records, unsorted
    }

    @Test
    public void test_HugeOffset_ShouldReturnEmpty() {
        Map<String, Object> criteria = Map.of("_offset", 9999);
        ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
        assertEquals(0, result.size(), "Expected empty result due to large offset");
    }

    @Test
    public void test_RG_CrossYearDates() {
        runJsonTest("{ \"createdDate:DATE\": { \"RG\": [\"2023-01-01\", \"2025-12-31\"] } }", "ROW_ID", "T1");
    }

}