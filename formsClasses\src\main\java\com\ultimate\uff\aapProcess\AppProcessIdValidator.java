package com.ultimate.uff.aapProcess;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Component;

import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.CustomIdValidator;

@Component
public class AppProcessIdValidator implements CustomIdValidator {


    @Autowired
    private DatabaseService DatabaseService;
    @Override
    public String validate(String id) {
   
    	Boolean existans = DatabaseService.checkRecordExistence("formDefinition", id);
    	
    	if(existans.equals(true)) {
        return "Validation successful";
        
    	}else {
    		return "no table exist";
    	}
    	
    }
}
