package com.ultimate.uff.formClasses;

import java.util.HashMap;
import java.util.Map;

public class FieldName {

    private String fieldName;
    private String type;
    private Boolean isMulti;
    private Boolean mandatory;
    private Boolean noChange;
    private String foreignKey;
    private String group;

    // Map to store dynamic/extra fields
    private Map<String, Object> extraFields = new HashMap<>();

    // Constructor to initialize from a map
    public FieldName(Map<String, Object> fieldDetail) {
        this.fieldName = (String) fieldDetail.get("fieldName");
        this.type = (String) fieldDetail.get("type");
        this.isMulti = (Boolean) fieldDetail.get("isMulti");
        this.mandatory = (Boolean) fieldDetail.get("mandatory");
        this.noChange = (Boolean) fieldDetail.get("noChange");
        this.foreignKey = (String) fieldDetail.get("foreignKey");
        this.group = (String) fieldDetail.get("Group");

        // Store any extra fields in the extraFields map
        for (Map.Entry<String, Object> entry : fieldDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    // Method to check if a field is explicitly handled
    private boolean isFieldHandled(String fieldName) {
        return fieldName.equals("fieldName") || fieldName.equals("type") || fieldName.equals("isMulti") ||
               fieldName.equals("mandatory") || fieldName.equals("noChange") || fieldName.equals("foreignKey") ||
               fieldName.equals("Group");
    }

    // Convert FieldName object to Map to put back into recordDetail
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("fieldName", this.fieldName);
        map.put("type", this.type);
        map.put("isMulti", this.isMulti);
        map.put("mandatory", this.mandatory);
        map.put("noChange", this.noChange);
        map.put("foreignKey", this.foreignKey);
        map.put("Group", this.group);
        // Add all extra fields to the map
        map.putAll(extraFields);
        return map;
    }

    // Getters and setters
    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getIsMulti() {
        return isMulti;
    }

    public void setIsMulti(Boolean isMulti) {
        this.isMulti = isMulti;
    }

    public Boolean getMandatory() {
        return mandatory;
    }

    public void setMandatory(Boolean mandatory) {
        this.mandatory = mandatory;
    }

    public Boolean getNoChange() {
        return noChange;
    }

    public void setNoChange(Boolean noChange) {
        this.noChange = noChange;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public Map<String, Object> getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(Map<String, Object> extraFields) {
        this.extraFields = extraFields;
    }
}
