<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.2</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.ultimate.uff</groupId>
    <artifactId>uff</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>uff</name>
    <description>uff project for Spring Boot</description>

    <modules>
        <module>uql</module>
        <module>validation</module>
        <module>tenant</module>
        <module>service</module>
        <module>security</module>        
        <module>web</module>
        <module>main</module>
        <module>formsClasses</module>
        <module>queue</module>
        <module>multiThreadServices</module>
        <module>recordHandler</module>
        <module>ledger</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <spring.boot.version>3.3.2</spring.boot.version>
        <mysql.connector.version>8.0.33</mysql.connector.version>
        <jjwt.version>0.9.1</jjwt.version>
       <!-- <mssql.jdbc.version>9.4.1.jre8</mssql.jdbc.version>--> 
        <com.fasterxml.version>2.15.2</com.fasterxml.version>
        <postgresql.version>42.7.3</postgresql.version>
        <ojdbc8.version>********</ojdbc8.version>
        <ojdbc11.version>*********.07</ojdbc11.version>
        <mssql.version>12.8.0.jre11</mssql.version>
        <validation.api.version>2.0.1.Final</validation.api.version>
        <hibernate.validator.version>8.0.1.Final</hibernate.validator.version>
        <junit.jupiter.version>5.9.3</junit.jupiter.version>
        <fasterxml.jackson.version>2.17.2</fasterxml.jackson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot Starters -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-jpa</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>${spring.boot.version}</version>
            </dependency>

            <!-- Database Connectors -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.connector.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql.version}</version>
            </dependency>

            <!-- JSON Web Token -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- Testing Frameworks -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.boot.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- Jackson Libraries -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${fasterxml.jackson.version}</version>
            </dependency>
<dependency>
    <groupId>jakarta.servlet</groupId>
    <artifactId>jakarta.servlet-api</artifactId>
    <version>6.0.0</version>
    <scope>provided</scope>
</dependency>
    
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
