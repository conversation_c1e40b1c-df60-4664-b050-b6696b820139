package com.ultimate.uff.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ultimate.uff.service.SignonProfileService;

import java.util.Map;

@RestController
@RequestMapping("/api/signon-profile")
public class SignonProfileController {

    @Autowired
    private SignonProfileService signonProfileService;

    @GetMapping("/{signOnName}")
    public Map<String, Object> getProfileAndMenu(@PathVariable String signOnName) {
        String profile = signonProfileService.getProfileBySignOnName(signOnName);
        if (profile != null && !profile.equals("Profile not found")) {
            return signonProfileService.getMenuByProfile(profile);
        } else {
            return null; // or return an appropriate error response
        }
    }
}
