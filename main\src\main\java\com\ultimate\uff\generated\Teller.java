package com.ultimate.uff.generated;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Teller extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String transactionCode;
    private final FieldValue<String> transactionCodeVal = new FieldValue<>(null);
    private String debitTellerId;
    private final FieldValue<String> debitTellerIdVal = new FieldValue<>(null);
    private String debitCurrency;
    private final FieldValue<String> debitCurrencyVal = new FieldValue<>(null);
    private String debitAccount;
    private final FieldValue<String> debitAccountVal = new FieldValue<>(null);
    private String debitCustomer;
    private final FieldValue<String> debitCustomerVal = new FieldValue<>(null);
    private Double debitAmountLocal;
    private final FieldValue<Double> debitAmountLocalVal = new FieldValue<>(null);
    private Double debitAmountForeign;
    private final FieldValue<Double> debitAmountForeignVal = new FieldValue<>(null);
    private Double debitRate;
    private final FieldValue<Double> debitRateVal = new FieldValue<>(null);
    private String debitValueDate;
    private final FieldValue<String> debitValueDateVal = new FieldValue<>(null);
    private String debitExposureDate;
    private final FieldValue<String> debitExposureDateVal = new FieldValue<>(null);
    private String debitNarrative;
    private final FieldValue<String> debitNarrativeVal = new FieldValue<>(null);
    private Double dealRate;
    private final FieldValue<Double> dealRateVal = new FieldValue<>(null);
    private String creditTellerId;
    private final FieldValue<String> creditTellerIdVal = new FieldValue<>(null);
    private String creditCurrency;
    private final FieldValue<String> creditCurrencyVal = new FieldValue<>(null);
    private String creditAccount;
    private final FieldValue<String> creditAccountVal = new FieldValue<>(null);
    private String creditCustomer;
    private final FieldValue<String> creditCustomerVal = new FieldValue<>(null);
    private Double creditAmountLocal;
    private final FieldValue<Double> creditAmountLocalVal = new FieldValue<>(null);
    private Double creditAmountForeign;
    private final FieldValue<Double> creditAmountForeignVal = new FieldValue<>(null);
    private String creditValueDate;
    private final FieldValue<String> creditValueDateVal = new FieldValue<>(null);
    private String creditExpsureDate;
    private final FieldValue<String> creditExpsureDateVal = new FieldValue<>(null);
    private Double creditRate;
    private final FieldValue<Double> creditRateVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private List<String> recordAuthorizer = new ArrayList<>();
    private final FieldValue<List<String>> recordAuthorizerVal = new FieldValue<>(new ArrayList<>());
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);
    private final List<CreditUnit> creditUnitList = new ArrayList<>();
    private final List<DebitUnit> debitUnitList = new ArrayList<>();

    public Teller() {}

    public Teller(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object transactionCodeObj = recordDetail.get("transactionCode");
        if (transactionCodeObj != null) {
            this.setTransactionCode(transactionCodeObj.toString());
        }
        Object debitTellerIdObj = recordDetail.get("debitTellerId");
        if (debitTellerIdObj != null) {
            this.setDebitTellerId(debitTellerIdObj.toString());
        }
        Object debitCurrencyObj = recordDetail.get("debitCurrency");
        if (debitCurrencyObj != null) {
            this.setDebitCurrency(debitCurrencyObj.toString());
        }
        Object debitAccountObj = recordDetail.get("debitAccount");
        if (debitAccountObj != null) {
            this.setDebitAccount(debitAccountObj.toString());
        }
        Object debitCustomerObj = recordDetail.get("debitCustomer");
        if (debitCustomerObj != null) {
            this.setDebitCustomer(debitCustomerObj.toString());
        }
        Object debitAmountLocalObj = recordDetail.get("debitAmountLocal");
        if (debitAmountLocalObj instanceof Number) {
            this.setDebitAmountLocal(((Number) debitAmountLocalObj).doubleValue());
        }
        Object debitAmountForeignObj = recordDetail.get("debitAmountForeign");
        if (debitAmountForeignObj instanceof Number) {
            this.setDebitAmountForeign(((Number) debitAmountForeignObj).doubleValue());
        }
        Object debitRateObj = recordDetail.get("debitRate");
        if (debitRateObj instanceof Number) {
            this.setDebitRate(((Number) debitRateObj).doubleValue());
        }
        Object debitValueDateObj = recordDetail.get("debitValueDate");
        if (debitValueDateObj != null) {
            this.setDebitValueDate(debitValueDateObj.toString());
        }
        Object debitExposureDateObj = recordDetail.get("debitExposureDate");
        if (debitExposureDateObj != null) {
            this.setDebitExposureDate(debitExposureDateObj.toString());
        }
        Object debitNarrativeObj = recordDetail.get("debitNarrative");
        if (debitNarrativeObj != null) {
            this.setDebitNarrative(debitNarrativeObj.toString());
        }
        Object dealRateObj = recordDetail.get("dealRate");
        if (dealRateObj instanceof Number) {
            this.setDealRate(((Number) dealRateObj).doubleValue());
        }
        Object creditTellerIdObj = recordDetail.get("creditTellerId");
        if (creditTellerIdObj != null) {
            this.setCreditTellerId(creditTellerIdObj.toString());
        }
        Object creditCurrencyObj = recordDetail.get("creditCurrency");
        if (creditCurrencyObj != null) {
            this.setCreditCurrency(creditCurrencyObj.toString());
        }
        Object creditAccountObj = recordDetail.get("creditAccount");
        if (creditAccountObj != null) {
            this.setCreditAccount(creditAccountObj.toString());
        }
        Object creditCustomerObj = recordDetail.get("creditCustomer");
        if (creditCustomerObj != null) {
            this.setCreditCustomer(creditCustomerObj.toString());
        }
        Object creditAmountLocalObj = recordDetail.get("creditAmountLocal");
        if (creditAmountLocalObj instanceof Number) {
            this.setCreditAmountLocal(((Number) creditAmountLocalObj).doubleValue());
        }
        Object creditAmountForeignObj = recordDetail.get("creditAmountForeign");
        if (creditAmountForeignObj instanceof Number) {
            this.setCreditAmountForeign(((Number) creditAmountForeignObj).doubleValue());
        }
        Object creditValueDateObj = recordDetail.get("creditValueDate");
        if (creditValueDateObj != null) {
            this.setCreditValueDate(creditValueDateObj.toString());
        }
        Object creditExpsureDateObj = recordDetail.get("creditExpsureDate");
        if (creditExpsureDateObj != null) {
            this.setCreditExpsureDate(creditExpsureDateObj.toString());
        }
        Object creditRateObj = recordDetail.get("creditRate");
        if (creditRateObj instanceof Number) {
            this.setCreditRate(((Number) creditRateObj).doubleValue());
        }
        Object IDObj = recordDetail.get("ID");
        if (IDObj != null) {
            this.setID(IDObj.toString());
        }
        Object recordStatusObj = recordDetail.get("recordStatus");
        if (recordStatusObj != null) {
            this.setRecordStatus(recordStatusObj.toString());
        }
        Object recordInputterObj = recordDetail.get("recordInputter");
        if (recordInputterObj != null) {
            this.setRecordInputter(recordInputterObj.toString());
        }
        Object listObj = recordDetail.get("recordAuthorizer");
        if (listObj instanceof List<?>) {
            this.setRecordAuthorizer((List<String>) listObj);
        }
        Object recordCountObj = recordDetail.get("recordCount");
        if (recordCountObj != null) {
            this.setRecordCount(recordCountObj.toString());
        }
        Object dateTimeObj = recordDetail.get("dateTime");
        if (dateTimeObj != null) {
            this.setDateTime(dateTimeObj.toString());
        }
        Object creditUnitObj = recordDetail.get("creditUnit");
        if (creditUnitObj instanceof List<?>) {
            for (Object item : (List<?>) creditUnitObj) {
                if (item instanceof Map) {
                    creditUnitList.add(new CreditUnit((Map<String, Object>) item));
                }
            }
        }
        Object debitUnitObj = recordDetail.get("debitUnit");
        if (debitUnitObj instanceof List<?>) {
            for (Object item : (List<?>) debitUnitObj) {
                if (item instanceof Map) {
                    debitUnitList.add(new DebitUnit((Map<String, Object>) item));
                }
            }
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (transactionCode != null) map.put("transactionCode", transactionCode);
        if (debitTellerId != null) map.put("debitTellerId", debitTellerId);
        if (debitCurrency != null) map.put("debitCurrency", debitCurrency);
        if (debitAccount != null) map.put("debitAccount", debitAccount);
        if (debitCustomer != null) map.put("debitCustomer", debitCustomer);
        if (debitAmountLocal != null) map.put("debitAmountLocal", debitAmountLocal);
        if (debitAmountForeign != null) map.put("debitAmountForeign", debitAmountForeign);
        if (debitRate != null) map.put("debitRate", debitRate);
        if (debitValueDate != null) map.put("debitValueDate", debitValueDate);
        if (debitExposureDate != null) map.put("debitExposureDate", debitExposureDate);
        if (debitNarrative != null) map.put("debitNarrative", debitNarrative);
        if (dealRate != null) map.put("dealRate", dealRate);
        if (creditTellerId != null) map.put("creditTellerId", creditTellerId);
        if (creditCurrency != null) map.put("creditCurrency", creditCurrency);
        if (creditAccount != null) map.put("creditAccount", creditAccount);
        if (creditCustomer != null) map.put("creditCustomer", creditCustomer);
        if (creditAmountLocal != null) map.put("creditAmountLocal", creditAmountLocal);
        if (creditAmountForeign != null) map.put("creditAmountForeign", creditAmountForeign);
        if (creditValueDate != null) map.put("creditValueDate", creditValueDate);
        if (creditExpsureDate != null) map.put("creditExpsureDate", creditExpsureDate);
        if (creditRate != null) map.put("creditRate", creditRate);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        if (!creditUnitList.isEmpty()) {
            List<Map<String, Object>> tmp = new ArrayList<>();
            for (CreditUnit u : creditUnitList) tmp.add(u.toMap());
            map.put("creditUnit", tmp);
        }
        if (!debitUnitList.isEmpty()) {
            List<Map<String, Object>> tmp = new ArrayList<>();
            for (DebitUnit u : debitUnitList) tmp.add(u.toMap());
            map.put("debitUnit", tmp);
        }
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "transactionCode", "debitTellerId", "debitCurrency", "debitAccount", "debitCustomer", "debitAmountLocal", "debitAmountForeign", "debitRate", "debitValueDate", "debitExposureDate", "debitNarrative", "dealRate", "creditTellerId", "creditCurrency", "creditAccount", "creditCustomer", "creditAmountLocal", "creditAmountForeign", "creditValueDate", "creditExpsureDate", "creditRate", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime", "creditUnit", "debitUnit" -> true;
            default -> false;
        };
    }

    public void setTransactionCode(String transactionCode) {
        this.transactionCode = transactionCode;
        this.transactionCodeVal.setValue(transactionCode);
    }
    public String getTransactionCode() { return this.transactionCode; }
    public FieldValue<String> transactionCodeVal() { return transactionCodeVal; }
    public void setDebitTellerId(String debitTellerId) {
        this.debitTellerId = debitTellerId;
        this.debitTellerIdVal.setValue(debitTellerId);
    }
    public String getDebitTellerId() { return this.debitTellerId; }
    public FieldValue<String> debitTellerIdVal() { return debitTellerIdVal; }
    public void setDebitCurrency(String debitCurrency) {
        this.debitCurrency = debitCurrency;
        this.debitCurrencyVal.setValue(debitCurrency);
    }
    public String getDebitCurrency() { return this.debitCurrency; }
    public FieldValue<String> debitCurrencyVal() { return debitCurrencyVal; }
    public void setDebitAccount(String debitAccount) {
        this.debitAccount = debitAccount;
        this.debitAccountVal.setValue(debitAccount);
    }
    public String getDebitAccount() { return this.debitAccount; }
    public FieldValue<String> debitAccountVal() { return debitAccountVal; }
    public void setDebitCustomer(String debitCustomer) {
        this.debitCustomer = debitCustomer;
        this.debitCustomerVal.setValue(debitCustomer);
    }
    public String getDebitCustomer() { return this.debitCustomer; }
    public FieldValue<String> debitCustomerVal() { return debitCustomerVal; }
    public void setDebitAmountLocal(Double debitAmountLocal) {
        this.debitAmountLocal = debitAmountLocal;
        this.debitAmountLocalVal.setValue(debitAmountLocal);
    }
    public Double getDebitAmountLocal() { return this.debitAmountLocal; }
    public FieldValue<Double> debitAmountLocalVal() { return debitAmountLocalVal; }
    public void setDebitAmountForeign(Double debitAmountForeign) {
        this.debitAmountForeign = debitAmountForeign;
        this.debitAmountForeignVal.setValue(debitAmountForeign);
    }
    public Double getDebitAmountForeign() { return this.debitAmountForeign; }
    public FieldValue<Double> debitAmountForeignVal() { return debitAmountForeignVal; }
    public void setDebitRate(Double debitRate) {
        this.debitRate = debitRate;
        this.debitRateVal.setValue(debitRate);
    }
    public Double getDebitRate() { return this.debitRate; }
    public FieldValue<Double> debitRateVal() { return debitRateVal; }
    public void setDebitValueDate(String debitValueDate) {
        this.debitValueDate = debitValueDate;
        this.debitValueDateVal.setValue(debitValueDate);
    }
    public String getDebitValueDate() { return this.debitValueDate; }
    public FieldValue<String> debitValueDateVal() { return debitValueDateVal; }
    public void setDebitExposureDate(String debitExposureDate) {
        this.debitExposureDate = debitExposureDate;
        this.debitExposureDateVal.setValue(debitExposureDate);
    }
    public String getDebitExposureDate() { return this.debitExposureDate; }
    public FieldValue<String> debitExposureDateVal() { return debitExposureDateVal; }
    public void setDebitNarrative(String debitNarrative) {
        this.debitNarrative = debitNarrative;
        this.debitNarrativeVal.setValue(debitNarrative);
    }
    public String getDebitNarrative() { return this.debitNarrative; }
    public FieldValue<String> debitNarrativeVal() { return debitNarrativeVal; }
    public void setDealRate(Double dealRate) {
        this.dealRate = dealRate;
        this.dealRateVal.setValue(dealRate);
    }
    public Double getDealRate() { return this.dealRate; }
    public FieldValue<Double> dealRateVal() { return dealRateVal; }
    public void setCreditTellerId(String creditTellerId) {
        this.creditTellerId = creditTellerId;
        this.creditTellerIdVal.setValue(creditTellerId);
    }
    public String getCreditTellerId() { return this.creditTellerId; }
    public FieldValue<String> creditTellerIdVal() { return creditTellerIdVal; }
    public void setCreditCurrency(String creditCurrency) {
        this.creditCurrency = creditCurrency;
        this.creditCurrencyVal.setValue(creditCurrency);
    }
    public String getCreditCurrency() { return this.creditCurrency; }
    public FieldValue<String> creditCurrencyVal() { return creditCurrencyVal; }
    public void setCreditAccount(String creditAccount) {
        this.creditAccount = creditAccount;
        this.creditAccountVal.setValue(creditAccount);
    }
    public String getCreditAccount() { return this.creditAccount; }
    public FieldValue<String> creditAccountVal() { return creditAccountVal; }
    public void setCreditCustomer(String creditCustomer) {
        this.creditCustomer = creditCustomer;
        this.creditCustomerVal.setValue(creditCustomer);
    }
    public String getCreditCustomer() { return this.creditCustomer; }
    public FieldValue<String> creditCustomerVal() { return creditCustomerVal; }
    public void setCreditAmountLocal(Double creditAmountLocal) {
        this.creditAmountLocal = creditAmountLocal;
        this.creditAmountLocalVal.setValue(creditAmountLocal);
    }
    public Double getCreditAmountLocal() { return this.creditAmountLocal; }
    public FieldValue<Double> creditAmountLocalVal() { return creditAmountLocalVal; }
    public void setCreditAmountForeign(Double creditAmountForeign) {
        this.creditAmountForeign = creditAmountForeign;
        this.creditAmountForeignVal.setValue(creditAmountForeign);
    }
    public Double getCreditAmountForeign() { return this.creditAmountForeign; }
    public FieldValue<Double> creditAmountForeignVal() { return creditAmountForeignVal; }
    public void setCreditValueDate(String creditValueDate) {
        this.creditValueDate = creditValueDate;
        this.creditValueDateVal.setValue(creditValueDate);
    }
    public String getCreditValueDate() { return this.creditValueDate; }
    public FieldValue<String> creditValueDateVal() { return creditValueDateVal; }
    public void setCreditExpsureDate(String creditExpsureDate) {
        this.creditExpsureDate = creditExpsureDate;
        this.creditExpsureDateVal.setValue(creditExpsureDate);
    }
    public String getCreditExpsureDate() { return this.creditExpsureDate; }
    public FieldValue<String> creditExpsureDateVal() { return creditExpsureDateVal; }
    public void setCreditRate(Double creditRate) {
        this.creditRate = creditRate;
        this.creditRateVal.setValue(creditRate);
    }
    public Double getCreditRate() { return this.creditRate; }
    public FieldValue<Double> creditRateVal() { return creditRateVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(List<String> recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public List<String> getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<List<String>> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
    public List<CreditUnit> getCreditUnitList() { return creditUnitList; }
    public List<DebitUnit> getDebitUnitList() { return debitUnitList; }
}
