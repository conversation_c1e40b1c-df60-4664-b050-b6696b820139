package com.ultimate.uff.web.controller;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.queue.RecordProcessingService;
import com.ultimate.uff.validation.GlobalResponse;
import com.ultimate.uff.validation.JavaClassGeneratorService;
import com.ultimate.uff.validation.RecordInsertionService;

import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/api/class-generator")
public class JavaClassGeneratorController {

    @Autowired
    private JavaClassGeneratorService javaClassGeneratorService;

    @Autowired
    private RecordInsertionService recordInsertionService; // assuming you named it like this

    @Autowired
    private ObjectMapper objectMapper;

    @PostMapping("/generate")
    public String generateClassById(@RequestParam String id) {
        try {
            GlobalResponse response = recordInsertionService.getTableMetadata(id);

            if (!response.isSuccess() || response.getData() == null) {
                return "❌ No metadata found for ID: " + id;
            }

            String json = objectMapper.writeValueAsString(response.getData());
            javaClassGeneratorService.generateJavaClass(json);

            return "✅ Class generated successfully for ID: " + id;

        } catch (Exception e) {
            return "❌ Error generating class for ID: " + id + " — " + e.getMessage();
        }
    }
}
