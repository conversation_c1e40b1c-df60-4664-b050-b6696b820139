package com.ultimate.uff.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ultimate.uff.uql.service.DatabaseService;

import jakarta.servlet.http.HttpSession;

@Service
public class UserService {

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;
    @Autowired
    private HttpSession session;
    @Autowired
    private DatabaseService databaseService;

    public JsonNode authenticate(String username, String password) throws JsonProcessingException {
        try {
            Map<String, Object> userProfile = Map.of("Sign_on_name", Map.of("EQ", username));

            ArrayNode results = databaseService.getRecordsByCriteria("user", userProfile);
            if (results.isEmpty()) {
                throw new UsernameNotFoundException("Invalid username");
            }

            String recordDetailJson = results.get(0).toString();
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode outerNode = objectMapper.readTree(recordDetailJson);

            // Access the inner JSON string
            String jsonRowDetailStr = outerNode.path("JSON_ROW_DETAIL").asText();
            JsonNode recordDetail = objectMapper.readTree(jsonRowDetailStr);

            // Now safely extract password
            String storedPassword = recordDetail.path("password").asText();
            if (storedPassword == null || storedPassword.isEmpty() || storedPassword.equals("null")) {
                throw new UsernameNotFoundException("No password set for this user");
            }

            if (passwordEncoder.matches(password, storedPassword)) {

                List<Map<String, Object>> simplifiedPrivileges = new ArrayList<>();

                if (recordDetail.has("role") && recordDetail.get("role").isArray()) {
                    ArrayNode rolesNode = (ArrayNode) recordDetail.get("role");

                    for (JsonNode roleNode : rolesNode) {
                        // Get tenant id
                        String tenantId = roleNode.path("tenant").asText();
                        if (tenantId == null || tenantId.isEmpty()) {
                            // Skip if tenant is missing
                            continue;
                        }

                        // Fetch tenant info
                        Map<String, Object> tenantInfo = databaseService.getRecordById("tenant", tenantId);

                        if (tenantInfo != null) {
                            boolean isPrimary = false;
                            Object isPrimaryObj = tenantInfo.get("isPrimary");
                            if (isPrimaryObj instanceof Boolean) {
                                isPrimary = (Boolean) isPrimaryObj;
                            } else if (isPrimaryObj instanceof String) {
                                isPrimary = Boolean.parseBoolean((String) isPrimaryObj);
                            }

                            String parentTenantId;

                            if (isPrimary) {
                                parentTenantId = (String) tenantInfo.get("ID");
                            } else {
                                parentTenantId = (String) tenantInfo.get("parentTenant");
                            }

                            Map<String, Object> simpleEntry = new HashMap<>();
                            simpleEntry.put("currentTenant", tenantId);
                            simpleEntry.put("parentTenant", parentTenantId);

                            simplifiedPrivileges.add(simpleEntry);
                        }
                    }
                }

                if (simplifiedPrivileges.isEmpty()) {
                    session.setAttribute("privileges", null);
                } else {
                    session.setAttribute("privileges", simplifiedPrivileges);
                }

                return recordDetail;
            }

        } catch (EmptyResultDataAccessException | IndexOutOfBoundsException e) {
            throw new UsernameNotFoundException("Invalid username");
        }

        return null;
    }

    // setPassword method unchanged
    public Map<String, Object> setPassword(String username, String password, String confirmPassword) {
        Map<String, Object> response = new HashMap<>();

        if (!password.equals(confirmPassword)) {
            response.put("success", false);
            response.put("message", "Passwords do not match");
            return response;
        }

        try {
            ArrayNode results = databaseService.getRecordsByCriteria("user", Map.of("Sign_on_name", Map.of("EQ", username)));
            if (results.isEmpty()) {
                response.put("success", false);
                response.put("message", "User not found");
                return response;
            }

            JsonNode userRecord = results.get(0);
            String userId = userRecord.path("ROW_ID").asText();

            String encryptedPassword = passwordEncoder.encode(password);
            String recordDetail = userRecord.path("JSON_ROW_DETAIL").asText();

            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode updatedRecord = (ObjectNode) objectMapper.readTree(recordDetail);
            updatedRecord.put("password", encryptedPassword);

            databaseService.insertRecord("user", userId, updatedRecord.toString());

            response.put("success", true);
            response.put("message", "Password set successfully");
        } catch (Exception e) {
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "Error setting password");
        }

        return response;
    }
}
