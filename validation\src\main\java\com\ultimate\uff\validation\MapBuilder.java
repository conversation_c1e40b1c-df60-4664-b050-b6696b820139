package com.ultimate.uff.validation;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

public class MapBuilder {

    public static Map<String, Object> buildFrom(ValidatableRecord record) {
        return buildFrom(record, new HashMap<>());
    }

    public static Map<String, Object> buildFrom(ValidatableRecord record, Map<String, Object> originalMap) {
        if (originalMap == null) {
            originalMap = new HashMap<>();
        }

        Class<?> clazz = record.getClass();

        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);

            try {
                Object value = field.get(record);

                if (value instanceof FieldValue<?> fv) {
                    originalMap.put(field.getName(), fv.getValue());

                } else if (value instanceof List<?> list) {
                    List<Object> serialized = new ArrayList<>();
                    for (Object item : list) {

                        if (item instanceof ValidatableRecord vr) {
                            serialized.add(buildFrom(vr));

                        } else if (hasToMap(item)) {
                            serialized.add(invokeToMap(item));

                        } else if (item instanceof Map<?, ?> m) {
                            // Safe cast for known structures
                            serialized.add(new HashMap<>((Map<?, ?>) m));

                        } else {
                            serialized.add(item);
                        }
                    }
                    originalMap.put(field.getName(), serialized);

                } else if (value instanceof Map<?, ?> map) {
                    try {
                        originalMap.putAll((Map<String, Object>) map);
                    } catch (ClassCastException e) {
                        // Optional: log or skip
                    }

                } else {
                    originalMap.put(field.getName(), value);
                }

            } catch (Exception e) {
                throw new RuntimeException("Failed to map field: " + field.getName(), e);
            }
        }

        return originalMap;
    }

    private static boolean hasToMap(Object obj) {
        if (obj == null) return false;
        try {
            Method method = obj.getClass().getMethod("toMap");
            return method.getReturnType().equals(Map.class);
        } catch (NoSuchMethodException e) {
            return false;
        }
    }

    private static Map<String, Object> invokeToMap(Object obj) {
        try {
            Method method = obj.getClass().getMethod("toMap");
            Object result = method.invoke(obj);
            return (Map<String, Object>) result;
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke toMap() on: " + obj.getClass().getSimpleName(), e);
        }
    }
}
