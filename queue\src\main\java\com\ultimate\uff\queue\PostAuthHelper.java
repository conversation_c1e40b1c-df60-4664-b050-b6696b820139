package com.ultimate.uff.queue;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Utility class for manually triggering post-authorization events.
 */
@Component
public class PostAuthHelper {

    private static final Logger logger = LoggerFactory.getLogger(PostAuthHelper.class);

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;

    public PostAuthHelper(KafkaTemplate<String, String> kafkaTemplate, ObjectMapper objectMapper) {
        this.kafkaTemplate = kafkaTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Triggers post-authorization logic by sending a record to the Kafka topic manually.
     *
     * @param tableName the name of the table
     * @param record    the fully authorized record
     */
    public void triggerPostAuthorizationManually(String tableName, Map<String, Object> record) {
        try {
            Map<String, Object> postAuthPayload = Map.of(
                "tableName", tableName,
                "id", record.get("ID"),
                "record", record
            );

            String json = objectMapper.writeValueAsString(postAuthPayload);
            kafkaTemplate.send("record-postauth-topic", json);
            logger.info("📤 Manual post-auth triggered for [{}]", record.get("ID"));
        } catch (Exception e) {
            logger.error("❌ Failed to trigger post-auth for [{}]: {}", record.get("ID"), e.getMessage(), e);
        }
    }
}
