package com.ultimate.uff.config;

import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;

import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.requestreply.ReplyingKafkaTemplate;
import org.springframework.kafka.support.serializer.JsonSerializer;


import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Kafka configuration for producer, consumer, and request-reply templates.
 * This config enables sending and receiving messages using Kafka with synchronous replies.
 */
@Configuration
@EnableKafka
public class KafkaConfig {
	@Autowired
	private KafkaReplyProperties kafkaReplyProperties;
    /**
     * Kafka topic for record insert operations.
     */
    @Bean
    public NewTopic insertTopic() {
        return new NewTopic("record-topic", 1, (short) 1);
    }
    @Bean
    public NewTopic postAuthTopic() {
        return new NewTopic("record-postauth-topic", 1, (short) 1);
    }

    /**
     * Kafka topic for record authorization operations.
     */
    @Bean
    public NewTopic authorizeTopic() {
        return new NewTopic("record-authorize-topic", 1, (short) 1);
    }

    /**
     * Kafka topic for sending replies back to the producer.
     */
    @Bean
    public NewTopic replyTopic() {
        return new NewTopic("record-reply-topic", 1, (short) 1);
    }

    /**
     * Kafka producer factory using String key and value serialization.
     */
    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> config = new HashMap<>();
        config.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        config.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        config.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return new DefaultKafkaProducerFactory<>(config);
    }
    /**
     * Kafka consumer factory using String key and value deserialization.
     */
    @Bean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> config = new HashMap<>();
        config.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        config.put(ConsumerConfig.GROUP_ID_CONFIG, "uff-group");
        config.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        config.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        return new DefaultKafkaConsumerFactory<>(config);
    }

    /**
     * Kafka template used for sending messages without reply.
     */
    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }


    /**
     * Listener container used by ReplyingKafkaTemplate to receive replies from the reply-topic.
     */
    @Bean
    public ConcurrentMessageListenerContainer<String, String> repliesContainer() {
        ContainerProperties containerProperties = new ContainerProperties("record-reply-topic");
        return new ConcurrentMessageListenerContainer<>(consumerFactory(), containerProperties);
    }

    /**
     * Replying Kafka Template used to send requests and wait synchronously for replies.
     * Sets a default reply timeout to prevent hanging calls.
     */
    @Bean
    public ReplyingKafkaTemplate<String, String, String> replyingKafkaTemplate() {
        ReplyingKafkaTemplate<String, String, String> template =
                new ReplyingKafkaTemplate<>(producerFactory(), repliesContainer());

        // Set default reply timeout (e.g., 10 seconds)
        long d = kafkaReplyProperties.getTimeoutSeconds();
        template.setDefaultReplyTimeout(Duration.ofSeconds(kafkaReplyProperties.getTimeoutSeconds()));

        return template;
    }
}
