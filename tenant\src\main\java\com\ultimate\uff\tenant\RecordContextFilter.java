package com.ultimate.uff.tenant;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class RecordContextFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;

        String currentCompany = httpRequest.getHeader("Current-Company");
        String primaryCompany = httpRequest.getHeader("Primary-Company-Code");
        String isTenantBasedStr = httpRequest.getHeader("Is-Tenant-Based");

        boolean isTenantBased = isTenantBasedStr != null && isTenantBasedStr.equalsIgnoreCase("true");

        if (primaryCompany != null && !primaryCompany.isEmpty()) {
            RecordContext.setCompanies(currentCompany != null ? currentCompany : primaryCompany, primaryCompany);
        } else if (currentCompany != null && !currentCompany.isEmpty()) {
            RecordContext.setCompanies(currentCompany, currentCompany);
        } else {
            System.out.println("Warning: No company code provided.");
        }

        RecordContext.setTenantBased(isTenantBased);

        try {
            chain.doFilter(request, response);
        } finally {
            RecordContext.clear();
        }
    }

    @Override
    public void init(FilterConfig filterConfig) {
        // no-op
    }

    @Override
    public void destroy() {
        // no-op
    }
}
