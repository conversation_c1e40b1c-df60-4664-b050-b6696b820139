package com.ultimate.uff.ledger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ultimate.uff.account.LedgerEntry.AccountLedgerEntry;
import com.ultimate.uff.recordHandler.PostActionData;
import com.ultimate.uff.recordHandler.PostRecordService;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Service
public class LedgerPostingService {

    @Autowired
    private PostRecordService postRecordService;

    public void prcPostLedgerEntries(List<AccountLedgerEntry> ledgerEntries) {
        if (ledgerEntries == null || ledgerEntries.isEmpty()) {
            throw new IllegalArgumentException("Ledger entries list is empty");
        }

        // 1. Generate transaction timestamp
        String transactionTimestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());

        // 2. Set fields and post each ledger entry individually
        int sequence = 1;
        int total = ledgerEntries.size();

        for (AccountLedgerEntry entry : ledgerEntries) {
            // Set missing mandatory fields
            entry.setEntryTimestamp(transactionTimestamp);
            entry.setEntrySequenceNo(String.format("%04d", sequence));
            entry.setTotalEntries(total);
            String transactionReference = entry.getTransactionReference();
            if (transactionReference == null || transactionReference.isEmpty()) {
                throw new IllegalArgumentException("Missing transactionReference for ledger entry");
            }

            // Build the ID: <timestamp>-<transactionReference>-<sequenceNo>
            String generatedId = transactionTimestamp + "-" + transactionReference + "-" + String.format("%04d", sequence);
            entry.setID(generatedId);

            // Prepare PostActionData
            PostActionData action = new PostActionData();
            action.setTable("accountLedgerEntry");
            action.setFunction("insert");
            action.setId(generatedId);                        // Set generated ID
            action.setBody(entry.toMap());                    // Single record map
            action.setAuthNum(0);                              // No authorization immediately

            // Post the record
            postRecordService.handle(action);

            sequence++;
        }
    }
}
