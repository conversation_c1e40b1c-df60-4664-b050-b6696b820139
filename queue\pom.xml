<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ultimate.uff</groupId>
    <artifactId>uff</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  <artifactId>queue</artifactId>
      <dependencies>

  <!-- Spring Boot Starter Web (for REST APIs) -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- Spring Boot Starter for Kafka -->
<dependency>
    <groupId>org.springframework.kafka</groupId>
    <artifactId>spring-kafka</artifactId>
</dependency>

<!-- <PERSON> (for JSON serialization/deserialization) -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
</dependency>

<!-- Spring Boot Starter Validation (optional but useful) -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-validation</artifactId>
</dependency>

<!-- (Optional) Lombok - if you're using it -->
<dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
    <optional>true</optional>
</dependency>
      	<dependency>
      		<groupId>com.ultimate.uff</groupId>
      		<artifactId>validation</artifactId>
      		<version>0.0.1-SNAPSHOT</version>
      	</dependency>
      	
      	<dependency>
    <groupId>org.springframework.kafka</groupId>
    <artifactId>spring-kafka</artifactId>
</dependency>

      	<dependency>
      		<groupId>com.ultimate.uff</groupId>
      		<artifactId>tenant</artifactId>
      		<version>0.0.1-SNAPSHOT</version>
      	</dependency>
      </dependencies>

</project>