package com.ultimate.uff.formClasses;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.CustomValidator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.queryBuilderClasses.QueryBuilder;
import com.ultimate.uff.queryBuilderClasses.SelectionFields;
import com.ultimate.uff.recordHandler.PostActionData;
import com.ultimate.uff.recordHandler.PostRecordService;
import com.ultimate.uff.tenant.RecordContext;

@Component
public class tableCreationDB implements CustomValidator {

    @Autowired
    private DatabaseService databaseService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private PostRecordService postRecordService;

    @Override
    public void authorize(String id, Map<String, Object> recordDetail) {
        // Initialize TableDefinition object from recordDetail
        TableDefinition tableDefinition = new TableDefinition(recordDetail);
        int recordCount = tableDefinition.getRecordCount();

        String currCom = RecordContext.getCurrentCompany();
        String primCom = RecordContext.getPrimaryCompany();
        boolean isTenantBased = RecordContext.isTenantBased();
        // Check if the record count is 1, then create tables
        if (recordCount <= 1) {
            databaseService.createTable(id);
            databaseService.createTable(id + "$UNAU");
            databaseService.createTable(id + "$HIST");
        }

        // Create an empty map for QueryBuilder
        Map<String, Object> emptyRecordDetail = new HashMap<>();

        // Initialize QueryBuilder object with the empty map
        QueryBuilder queryBuilder = new QueryBuilder(emptyRecordDetail);

        // Get the list of FieldName objects from TableDefinition
        List<FieldName> fieldNameList = tableDefinition.getFieldNameList();

        // Initialize the fieldsToAppear list if it is null or empty
        if (queryBuilder.getFieldsToAppear() == null) {
            queryBuilder.setFieldsToAppear(new ArrayList<>());
        }

        // Set the fields to appear in the QueryBuilder
        for (int i = 0; i < fieldNameList.size(); i++) {
            if (i < queryBuilder.getFieldsToAppear().size()) {
                queryBuilder.getFieldsToAppear().set(i, fieldNameList.get(i).getFieldName());
            } else {
                queryBuilder.getFieldsToAppear().add(fieldNameList.get(i).getFieldName());
            }
        }

        // Initialize the selectionFieldsList if it is null or empty
        if (queryBuilder.getSelectionFieldsList() == null) {
            queryBuilder.setSelectionFieldsList(new ArrayList<>());
        }

        // Populate the selectionFieldsList in QueryBuilder
        for (FieldName fieldName : fieldNameList) {
            // Create a new SelectionFields object
            SelectionFields selectionField = new SelectionFields(new HashMap<>());
            selectionField.setSelectionField(fieldName.getFieldName());
            selectionField.setOperator("EQ");
            queryBuilder.getSelectionFieldsList().add(selectionField);
        }

        queryBuilder.setId(id);
        queryBuilder.setFileName(id);

        // Update the empty map using QueryBuilder and discard the result if not needed
        queryBuilder.updateRecordDetail(emptyRecordDetail);

        PostActionData action = new PostActionData();
		action.setTable("query_builder");
		action.setFunction("insert");
		action.setId(id);
		action.setBody(emptyRecordDetail);
		action.setAuthNum(0);
		postRecordService.handle(action);
    }
}
