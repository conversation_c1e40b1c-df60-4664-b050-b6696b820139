package com.ultimate.uff.teller;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;

public class UserTill extends ValidatableRecord {

    private static final Logger logger = LoggerFactory.getLogger(UserTill.class);
    private final Map<String, Object> extraFields = new HashMap<>();
    private final FieldValue<String> user = new FieldValue<>(null);
    private final FieldValue<String> status = new FieldValue<>(null);
    private final FieldValue<String> ID = new FieldValue<>(null);
    private final FieldValue<String> recordStatus = new FieldValue<>(null);
    private final FieldValue<String> recordInputter = new FieldValue<>(null);
    private final FieldValue<String> recordAuthorizer = new FieldValue<>(null);
    private final FieldValue<String> recordCount = new FieldValue<>(null);
    private final FieldValue<String> dateTime = new FieldValue<>(null);

    public UserTill() {
        logger.debug("UserTill created with empty constructor.");
    }

    public UserTill(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        logger.debug("Initializing UserTill from provided recordDetail.");
        user.setValue((String) recordDetail.get("user"));
        status.setValue((String) recordDetail.get("status"));
        ID.setValue((String) recordDetail.get("ID"));
        recordStatus.setValue((String) recordDetail.get("recordStatus"));
        recordInputter.setValue((String) recordDetail.get("recordInputter"));
        recordAuthorizer.setValue((String) recordDetail.get("recordAuthorizer"));
        recordCount.setValue((String) recordDetail.get("recordCount"));
        dateTime.setValue((String) recordDetail.get("dateTime"));
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> recordDetail = new HashMap<>();
        recordDetail.put("user", user.getValue());
        recordDetail.put("status", status.getValue());
        recordDetail.put("ID", ID.getValue());
        recordDetail.put("recordStatus", recordStatus.getValue());
        recordDetail.put("recordInputter", recordInputter.getValue());
        recordDetail.put("recordAuthorizer", recordAuthorizer.getValue());
        recordDetail.put("recordCount", recordCount.getValue());
        recordDetail.put("dateTime", dateTime.getValue());
        recordDetail.putAll(extraFields);
        logger.debug("UserTill serialized to map with {} fields.", recordDetail.size());
        return recordDetail;
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
            case "user", "status", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public FieldValue<String> getUser() { return user; }
    public FieldValue<String> getStatus() { return status; }
    public FieldValue<String> getID() { return ID; }
    public FieldValue<String> getRecordStatus() { return recordStatus; }
    public FieldValue<String> getRecordInputter() { return recordInputter; }
    public FieldValue<String> getRecordAuthorizer() { return recordAuthorizer; }
    public FieldValue<String> getRecordCount() { return recordCount; }
    public FieldValue<String> getDateTime() { return dateTime; }
}