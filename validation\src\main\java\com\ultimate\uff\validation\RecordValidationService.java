package com.ultimate.uff.validation;

import com.ultimate.uff.uql.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Responsible for validating a record against:
 * 1. Field-level rules defined in formDefinition
 * 2. Record-level custom validations defined in application_process
 */
@Service
public class RecordValidationService {

    private static final Logger logger = LoggerFactory.getLogger(RecordValidationService.class);

    @Autowired
    private DatabaseService databaseService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private FieldValidationService fieldValidationService;

    /**
     * Performs complete validation for a given record.
     * Includes:
     * - Basic field-level checks (mandatory, type, FK)
     * - Custom business rule validators (per table)
     *
     * @param tableName    name of the form/table to validate
     * @param recordDetail the user-submitted record data
     * @return a {@link ValidationResponse} with errors if any
     */
    public ValidationResponse validateRecord(String tableName, Map<String, Object> recordDetail) {
        ValidationResponse result = new ValidationResponse();

        try {
            logger.info("Validating record for table: {}", tableName);

            // Step 1: Field-level validation
            Map<String, Object> metadataJson = databaseService.getRecordById("formDefinition", tableName);
            if (metadataJson == null) {
                logger.warn("Metadata not found for table: {}", tableName);
                result.addFieldError("table", "Table metadata not found");
                return result;
            }

            String fieldValidationResult = fieldValidationService.validateFields(metadataJson, recordDetail);
            if (!"Validation successful".equals(fieldValidationResult)) {
                logger.debug("Field validation failed: {}", fieldValidationResult);
                result.addFieldError("fields", fieldValidationResult);
                return result;
            }

            // Step 2: Record-level custom validators from application_process
            Map<String, Object> recordValidationMap;
            try {
                recordValidationMap = databaseService.getRecordById("application_process", tableName);
                if (recordValidationMap == null || recordValidationMap.isEmpty()) {
                    return result; // No record-level validation configured
                }
            } catch (EmptyResultDataAccessException e) {
                logger.warn("No application_process config found for {}", tableName);
                return result;
            }

            List<String> recordValidationClasses = (List<String>) recordValidationMap.get("recordValidation");
            if (recordValidationClasses == null || recordValidationClasses.isEmpty()) {
                return result;
            }

            for (String className : recordValidationClasses) {
                if (className == null || className.trim().isEmpty()) continue;

                try {
                    logger.debug("Invoking record validator: {}", className);
                    Class<?> clazz = Class.forName(className);
                    CustomValidator validator = (CustomValidator) applicationContext.getBean(clazz);

                    ValidationResponse validatorResponse = validator.validateWithResponse("", recordDetail);
                    validatorResponse.getFieldErrors().forEach(fe ->
                        result.addFieldError(fe.getField(), fe.getError())
                    );

                } catch (ClassNotFoundException e) {
                    logger.error("Validation class not found: {}", className);
                    result.addFieldError("class", "Validation class not found: " + className);
                } catch (Exception e) {
                    logger.error("Error in validation class [{}]: {}", className, e.getMessage(), e);
                    result.addFieldError("class", "Error during validation in class " + className + ": " + e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("Unexpected error during validation for table [{}]: {}", tableName, e.getMessage(), e);
            result.addFieldError("exception", "Unexpected error during validation: " + e.getMessage());
        }

        return result;
    }
}
