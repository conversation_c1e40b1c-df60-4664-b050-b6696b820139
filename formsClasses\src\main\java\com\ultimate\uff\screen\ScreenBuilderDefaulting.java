package com.ultimate.uff.screen;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.ultimate.uff.validation.CustomValidator;

/**
 * Defaulting logic for ScreenBuilder.
 * Applies custom defaulting logic when new data is constructed or processed.
 */
@Component
public class ScreenBuilderDefaulting implements CustomValidator {

    private static final Logger logger = LoggerFactory.getLogger(ScreenBuilderDefaulting.class);

    /**
     * Applies default values to the given screen metadata.
     *
     * @param recordDetail input map representing raw screen data
     * @return a fully processed and updated map structure with applied defaults
     */
    @Override
    public Map<String, Object> newData(Map<String, Object> recordDetail) {
        // Initialize ScreenBuilder from input map
        ScreenBuilder sc = new ScreenBuilder(recordDetail);
        logger.debug("Initialized ScreenBuilder with input map.");

        // Example modification: Update the first default field if present
        if (!sc.getDefaultFieldsList().isEmpty()) {
            sc.getDefaultFieldsList().get(0).getDefaultField().setValue("sdd");
            logger.debug("Modified first defaultField value to 'sdd'.");
        }

        // Set table name
        sc.getTable().setValue("dd");
        logger.debug("Set table name to 'dd'.");

        // Access the updated value if needed (optional)
        String firstDefault = sc.getDefaultFieldsList().get(0).getDefaultField().getValue();
        logger.info("First defaultField value: {}", firstDefault);

        // Return the updated map using toMap()
        return sc.toMap();
    }
}
