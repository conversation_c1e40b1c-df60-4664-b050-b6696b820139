package com.ultimate.uff.teller;

import com.ultimate.uff.account.LedgerEntry.AccountLedgerEntry;
import com.ultimate.uff.ledger.LedgerPostingService;
import com.ultimate.uff.validation.CustomValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class postTeller implements CustomValidator {

    private static final String PL_ACCOUNT = "PL50063"; // Profit/Loss account
    private static final String BASE_CURRENCY = "YER";

    @Autowired
    private LedgerPostingService ledgerPostingService;

    @Override
    public void authorize(String id, Map<String, Object> recordDetail) {
        Teller teller = new Teller(recordDetail);
        List<AccountLedgerEntry> ledgerEntries = new ArrayList<>();

        Double debitAmountLocal = teller.getDebitAmountLocal();
        Double creditAmountLocal = teller.getCreditAmountLocal();

        // ➤ Debit Entry
        if (teller.getDebitAccount() != null) {
            AccountLedgerEntry debitEntry = new AccountLedgerEntry();
            debitEntry.setLedgerId(1001);
            debitEntry.setAccountNumber(teller.getDebitAccount());
            debitEntry.setCustomerNumber(teller.getDebitCustomer());
            debitEntry.setCurrencyCode(teller.getDebitCurrency());
            debitEntry.setAmountLcy(-1 * debitAmountLocal);
            debitEntry.setEntryDrCr("DR");
            debitEntry.setTransactionReference(id);
            debitEntry.setCurrencyMarket("10");
            debitEntry.setBookingDate(teller.getDebitValueDate());
            debitEntry.setValueDate(teller.getDebitValueDate());
            debitEntry.setProcessingDate(teller.getDebitValueDate());

            // Handle Foreign Amount and Rate for Debit
            if (!BASE_CURRENCY.equals(teller.getDebitCurrency())) {
                debitEntry.setAmountFcy(teller.getDebitAmountForeign());
                debitEntry.setCurrencyRate(teller.getDebitRate());
            }

            ledgerEntries.add(debitEntry);
        }

        // ➤ Credit Entry
        if (teller.getCreditAccount() != null) {
            AccountLedgerEntry creditEntry = new AccountLedgerEntry();
            creditEntry.setLedgerId(1001);
            creditEntry.setAccountNumber(teller.getCreditAccount());
            creditEntry.setCustomerNumber(teller.getCreditCustomer());
            creditEntry.setCurrencyCode(teller.getCreditCurrency());
            creditEntry.setAmountLcy(creditAmountLocal);
            creditEntry.setEntryDrCr("CR");
            creditEntry.setTransactionReference(id);
            creditEntry.setCurrencyMarket("10");
            creditEntry.setBookingDate(teller.getCreditValueDate());
            creditEntry.setValueDate(teller.getCreditValueDate());
            creditEntry.setProcessingDate(teller.getCreditValueDate());

            // Handle Foreign Amount and Rate for Credit
            if (!BASE_CURRENCY.equals(teller.getCreditCurrency())) {
                creditEntry.setAmountFcy(teller.getCreditAmountForeign());
                creditEntry.setCurrencyRate(teller.getCreditRate());
            }

            ledgerEntries.add(creditEntry);
        }

        // ➤ Profit/Loss Entry
        if (!teller.getDebitCurrency().equals(teller.getCreditCurrency())) {
            double plAmount = debitAmountLocal - creditAmountLocal;

            if (plAmount != 0.0) {
                AccountLedgerEntry plEntry = new AccountLedgerEntry();
                plEntry.setLedgerId(50063);
                plEntry.setAccountNumber(PL_ACCOUNT);
                plEntry.setCustomerNumber("PL");
                plEntry.setCurrencyCode(BASE_CURRENCY);
                plEntry.setAmountLcy(plAmount);
                plEntry.setEntryDrCr(plAmount > 0 ? "CR" : "DR");
                plEntry.setCurrencyMarket("10");
                plEntry.setTransactionReference(id);
                plEntry.setBookingDate(teller.getCreditValueDate());
                plEntry.setValueDate(teller.getCreditValueDate());
                plEntry.setProcessingDate(teller.getCreditValueDate());



                ledgerEntries.add(plEntry);
            }
        }

        // ➤ Post to Ledger
        ledgerPostingService.prcPostLedgerEntries(ledgerEntries);
    }
}
