package com.ultimate.uff.web.controller;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ultimate.uff.multi.thread.services.MainService;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/agents")
public class AgentController {

    @Autowired
    private MainService mainService;


    @PostMapping("/start")
    public String start() {
        mainService.startAgents();
        return "🟢 Agents and watcher started.";
    }
    @PostMapping("/stop")
    public String stop() {
        mainService.stopAgents();
        return "🛑 All agents and watcher stopped.";
    }

    @PostMapping("/clear-manual-stop")
    public String clearManualStop() {
        mainService.clearManuallyStoppedAgents();
        return "✅ Cleared manually stopped list. Ready for /agents/start.";
    }

    @GetMapping("/status")
    public Map<String, Object> status() {
        Map<String, Object> status = new LinkedHashMap<>();
        status.put("watcherRunning", mainService.isWatcherRunning());
        status.put("runningAgents", mainService.getRunningAgentNames());
        status.put("manuallyStopped", mainService.getManuallyStoppedAgents());
        status.put("agentNumbers", mainService.getAgentNumberMap());
        return status;
    }
}
