package com.ultimate.uff.uql.util.mssql.condition;

import com.ultimate.uff.uql.model.ParsedFilter;
import com.ultimate.uff.uql.util.mssql.MSSQLExpressionBuilder;
import com.ultimate.uff.uql.util.mssql.MSSQLUtils;

import java.util.List;

public class SingleFieldConditionBuilder {
    public static String build(ParsedFilter filter, String operator, Object value) {
        String castType = MSSQLUtils.getSqlCastType(filter.getType());
        boolean isDate = "DATE".equalsIgnoreCase(filter.getType());
        boolean isLike = MSSQLExpressionBuilder.isLikeOperator(operator);

        String columnExpr = buildColumnExpr(filter.getField(), castType, isDate, isLike);

        return switch (operator.toUpperCase()) {
            case "EQ" -> buildEqCondition(columnExpr, value, isDate);
            case "NE" -> buildNeCondition(columnExpr, value, isDate);
            case "CT"-> buildLikeCondition(columnExpr, "%" + value + "%");
            case "LK" -> buildLikeCondition(columnExpr, value.toString());
            case "LI" -> buildILikeCondition(columnExpr, value.toString());
            case "BW" -> buildLikeCondition(columnExpr, value + "%");
            case "EW" -> buildLikeCondition(columnExpr, "%" + value);
            case "DNBW" -> "NOT " + buildLikeCondition(columnExpr, value + "%");
            case "DNEW" -> "NOT " + buildLikeCondition(columnExpr, "%" + value);
            case "GT" -> buildSimpleComparison(columnExpr, ">", value);
            case "LT" -> buildSimpleComparison(columnExpr, "<", value);
            case "GE" -> buildSimpleComparison(columnExpr, ">=", value);
            case "LE" -> buildSimpleComparison(columnExpr, "<=", value);
            case "RG", "NR" -> buildRangeComparison(columnExpr, value, operator.equalsIgnoreCase("NR"));
            default -> throw new IllegalArgumentException("Unsupported operator for single field: " + operator);
        };
    }

    // --- Internal Builders --- //

    private static String buildEqCondition(String columnExpr, Object value, boolean isDate) {
        if (value == null) {
            return columnExpr + " IS NULL";
        }
        return columnExpr + " = " + formatValue(value, isDate);
    }

    private static String buildNeCondition(String columnExpr, Object value, boolean isDate) {
        if (value == null) {
            return columnExpr + " IS NOT NULL";
        }
        return columnExpr + " <> " + formatValue(value, isDate);
    }

    private static String buildLikeCondition(String columnExpr, String pattern) {
        String castedExpr = "CAST(" + columnExpr + " AS NVARCHAR(MAX))";
        return castedExpr + " LIKE " + MSSQLUtils.likeQuote(pattern);
    }

    private static String buildSimpleComparison(String columnExpr, String op, Object value) {
        return columnExpr + " " + op + " " + MSSQLUtils.formatSqlValue(value, "NVARCHAR(MAX)");
    }

    private static String buildRangeComparison(String columnExpr, Object value, boolean negate) {
        if (!(value instanceof List<?> list) || list.size() != 2) {
            throw new IllegalArgumentException("Range operator requires exactly 2 values");
        }
        Object startRaw = list.get(0);
        Object endRaw = list.get(1);

        boolean simpleDate = MSSQLUtils.isSimpleDateFormat(startRaw.toString()) && MSSQLUtils.isSimpleDateFormat(endRaw.toString());

        String start = MSSQLUtils.formatSqlValue(startRaw, "NVARCHAR(MAX)");
        String end = MSSQLUtils.formatSqlValue(endRaw, "NVARCHAR(MAX)");

        String condition = simpleDate
                ? "CAST(" + columnExpr + " AS DATE) BETWEEN CAST(" + start + " AS DATE) AND CAST(" + end + " AS DATE)"
                : columnExpr + " BETWEEN " + start + " AND " + end;

        return negate ? "NOT (" + condition + ")" : condition;
    }

    private static String buildColumnExpr(String field, String castType, boolean isDate, boolean isLike) {
        String baseExpr = "JSON_VALUE(JSON_ROW_DETAIL, '$." + field + "')";

        if (isLike) {
            return baseExpr; // LIKE works over NVARCHAR, cast later
        }
        return isDate ? "CAST(" + baseExpr + " AS DATE)" : "TRY_CAST(" + baseExpr + " AS " + castType + ")";
    }

    private static String formatValue(Object value, boolean isDate) {
        if (isDate && value instanceof String s) {
            MSSQLUtils.validateDateFormat(s);
            if (MSSQLUtils.isSimpleDateFormat(s)) {
                return "CAST(" + MSSQLUtils.formatSqlValue(s, "STRING") + " AS DATE)";
            }
            return MSSQLUtils.formatSqlValue(s, "STRING");
        }
        return MSSQLUtils.formatSqlValue(value, "NVARCHAR(MAX)");
    }

    private static String buildILikeCondition(String columnExpr, String pattern) {
        String castedExpr = "LOWER(CAST(" + columnExpr + " AS NVARCHAR(MAX)))";
        return castedExpr + " LIKE " + MSSQLUtils.likeQuote(pattern.toLowerCase());
    }
    
}
