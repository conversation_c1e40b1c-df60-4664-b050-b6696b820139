package com.ultimate.uff.multi.thread.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.multi.thread.agents.AgentManager;
import com.ultimate.uff.multi.thread.generatedClass.Service;
import com.ultimate.uff.multi.thread.jobs.JobLogService;
import com.ultimate.uff.multi.thread.jobs.JobService;
import com.ultimate.uff.validation.ReadOnlyDatabaseService;

import oracle.net.aso.l;

import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class ServiceManager {
     //private static final Logger logger = LoggerFactory.getLogger(ServiceManager.class);

    // Dependencies for database access, job management, Kafka, logging, and JSON processing
    private final ReadOnlyDatabaseService readOnlyDatabaseService;
    private final JobService jobService;
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final JobLogService jobLogService;
    private final ObjectMapper objectMapper;
    private final LoggerClass loggerClass;

    // Map to keep track of running services and their AgentManagers
    private final Map<String, AgentManager> runningServices = new ConcurrentHashMap<>();

    // Scheduled executor to monitor CMS service status periodically
    private final ScheduledExecutorService monitorScheduler = Executors.newSingleThreadScheduledExecutor();

    // Cache for service IDs to reduce database calls
    private volatile List<String> cachedServiceIds = Collections.emptyList();
    private final Object cacheLock = new Object();

    // Constructor to inject dependencies
    public ServiceManager(ReadOnlyDatabaseService readOnlyDatabaseService,
                        JobService jobService,
                        KafkaTemplate<String, String> kafkaTemplate,
                        JobLogService jobLogService,
                        ObjectMapper objectMapper,
                        LoggerClass loggerClass
                        //PostRecordService postRecordService
    ) {
        this.readOnlyDatabaseService = readOnlyDatabaseService;
        this.jobService = jobService;
        this.kafkaTemplate = kafkaTemplate;
        this.jobLogService = jobLogService;
        this.objectMapper = objectMapper;
        this.loggerClass=loggerClass;
        //this.postRecordService=postRecordService;
    }
    /**
     * Starts a service by its ID if not already running.
     * Only services with status "START" or "AUTO" (except "CMS") are allowed to start.
     * If the service is "CMS", it triggers starting eligible services and monitoring thread.
     *
     * @param serviceId the ID of the service to start
     * @return status message about the start operation
     */
   /*  public String startService(String serviceId) {
        return runningServices.compute(serviceId, (key, existingManager) -> {
            if (existingManager != null) {
                loggerClass.getLogger().warn("SERVICE", serviceId, "ALREADY_RUNNING", "Service already running");
                return existingManager;
            }

            // Fetch service record from read-only database
            Map<String, Object> serviceRecord = readOnlyDatabaseService.getRecordById("service", key);
            if (serviceRecord == null) {
                loggerClass.getLogger().error("SERVICE", key, "Failed", "Service not found");
                return null;
            }

            Service service = new Service(serviceRecord);
            String status = service.getStatus();

            // Only start services with status START or AUTO, except CMS which is always allowed
            if (!"CMS".equalsIgnoreCase(key) && !"START".equalsIgnoreCase(status) && !"AUTO".equalsIgnoreCase(status)) {
                loggerClass.getLogger().warn("SERVICE", key, "SKIPPED", "Service is not marked as START or AUTO (status = " + status + ")");
                return null;
            }

            // Prevent starting services if CMS is not running (except CMS itself)
            if (!"CMS".equalsIgnoreCase(key) && !isCmsRunning()) {
                loggerClass.getLogger().error("SERVICE", key, "CMS_NOT_RUNNING", "Cannot start because CMS is not running");
                return null;
            }

            // Determine the number of agents to start for the service
            int agentCount = (int) service.getCountAgents();
            // Create and start AgentManager for this service
            AgentManager manager = new AgentManager(key, agentCount, jobService, kafkaTemplate, jobLogService, objectMapper,loggerClass);
            manager.start();
            jobLogService.logInfo("SERVICE", key, "STARTED", "Service started with " + agentCount + " agent(s)");
            loggerClass.getLogger().info("Service" + key +" started with " + agentCount + " agent(s)");

            // If the started service is CMS, start other eligible services and monitoring thread
            if ("CMS".equalsIgnoreCase(key)) {
                startEligibleServices();
                startMonitorThread();
            }
            return manager;
        }) != null ? "Service '" + serviceId + "' started successfully." : "Service '" + serviceId + "' is already running.";
    }*/
    


    public String startService(String serviceId) {
        if (runningServices.containsKey(serviceId)) {
            return "Service '" + serviceId + "' is already running.";
        }
        Map<String, Object> serviceRecord = readOnlyDatabaseService.getRecordById("service", serviceId);
        if (serviceRecord == null) {
            return "Service '" + serviceId + "' not found.";
        }
        Service service = new Service(serviceRecord);
        String status = service.getStatus();
         //mainService
        // Check CMS if we are trying to run another service
        if (!"CMS".equalsIgnoreCase(serviceId)) {
            AgentManager mainAgent = runningServices.get("CMS");
            if (mainAgent == null || !mainAgent.isAlive()) {
                return "Cannot start '" + serviceId + "' because CMS is not running.";
            }
        }

        // RUNNING Agents for this service
        int agentCount = (int) service.getCountAgents();
       // int agentCount = (int) serviceRecord.getOrDefault("countAgents", 1);
        AgentManager agentManager = new AgentManager(serviceId,agentCount, jobService, kafkaTemplate, jobLogService, objectMapper,loggerClass);
        agentManager.start();
        runningServices.put(serviceId, agentManager);
       // activeServices.put(serviceId, agentManager);

        // if this is CMS run all service with a status AUTO/START 
        if ("CMS".equalsIgnoreCase(serviceId)) {
            startEligibleServices();
            startMonitorThread();
        }
       // return "Service '" + serviceId + "' started successfully.";
        jobLogService.logInfo("Service '" , serviceId ,"' started with " + agentCount , " agents.");
        return "Service '" + serviceId + "' started with " + agentCount + " agents.";
    }




     //Checks if the CMS service is currently running.
    private boolean isCmsRunning() {
        AgentManager cms = runningServices.get("CMS");
        return cms != null && cms.isAlive();
    }

    /**
     * Starts a scheduled monitor thread that checks CMS status every 3 seconds.
     * If CMS stops, all other services are stopped and monitor shuts down.
     */
    private void startMonitorThread() {
        monitorScheduler.scheduleWithFixedDelay(() -> {
            if (!isCmsRunning()) {
                loggerClass.getLogger().info("CMS","TERMINATED", "Stopping all services due to CMS termination");
                stopAllServices();
                monitorScheduler.shutdown();
            }
        }, 3, 3, TimeUnit.SECONDS);
    }
    /**
     * Stops all running services except CMS.
     */
    private void stopAllServices() {
        runningServices.keySet().stream()
                .filter(sid -> !"CMS".equalsIgnoreCase(sid))
                .forEach(this::stopService);
    }
    /**
     * Stops a service by its ID if it is running.
     * If the service is CMS, stops all services and destroys the manager.
     *
     * @param serviceId the ID of the service to stop
     * @return status message about the stop operation
     */
    public String stopService(String serviceId) {
        AgentManager agentManager = runningServices.get(serviceId);
        if (agentManager == null || !agentManager.isAlive()) {
            loggerClass.getLogger().info( serviceId,  " not running");
            return "Service '" + serviceId + "' is not running.";
        }
        try {
            agentManager.stop();
            runningServices.remove(serviceId);
            if ("CMS".equalsIgnoreCase(serviceId)) {
                stopAllServices();
                destroy();
                return "Service '" + serviceId + "' stopped successfully.";
            }
            loggerClass.getLogger().info("SERVICE", serviceId, " stopped successfully.", "with agent(s)");
            return "Service '" + serviceId + "' stopped successfully.";
        } catch (Exception e) {
            loggerClass.getLogger().error("SERVICE", serviceId, "STOP_FAILED", e.getMessage(), e);
            return "Error stopping service '" + serviceId + "'.";
        }
    }

    /*public String stopService(String serviceId) {
        try {
            AgentManager agentManager = runningServices.remove(serviceId);
            if (agentManager == null) {
                jobLogService.logWarn("SERVICE", serviceId, "NOT_RUNNING", "Service not running");
                return "Service '" + serviceId + "' is not running.";
            }
            agentManager.stop();
            if ("CMS".equalsIgnoreCase(serviceId)) {
                stopAllServices();
                destroy();
            }
           // runningServices.remove(serviceId);
            jobLogService.logInfo("SERVICE",serviceId, " stopped successfully.", "with agent(s)");
            return "Service '" + serviceId + "' stopped successfully.";
        } catch (Exception e) {
            jobLogService.logError("SERVICE", serviceId, "STOP_FAILED", e.getMessage(), e);
            return "Error stopping service '" + serviceId + "'.";
        }
    }*/
    /**
     * Cleans up resources by shutting down monitor and stopping all services.
     */
    public void destroy() {
        System.out.println("🔻 Shutting down...");
        monitorScheduler.shutdownNow();
        stopAllServices();
    }
    //Retrieves the current state of a service including status, running agents, and last heartbeat.
    
    public Map<String, Object> getServiceState(String serviceId) {
        Map<String, Object> serviceRecord = readOnlyDatabaseService.getRecordById("service", serviceId);
        if (serviceRecord == null) {
            return Map.of(
                "serviceId", serviceId,
                "status", "NOT_FOUND",
                "isRunning", false,
                "lastTimeRunning", null,
                "agentCount", 0
            );
        }
        Service service = new Service(serviceRecord);
        String storedStatus = service.getStatus();
        Integer agentCount = service.getCountAgents();
        AgentManager agentManager = runningServices.get(serviceId);
        boolean isRunning = agentManager != null && agentManager.isAlive();
        if(agentManager != null){
        return Map.of(
            "serviceId", serviceId,
            "status", storedStatus,
            "agentCount",agentCount,
            "isRunning", isRunning,
            "lastTimeRunning", isRunning ? agentManager.getLatestHeartbeat() : null,
            "agentisRunning", isRunning ? agentManager.getAgentDetailsList().size() : 0
        );
    }
    return null;
    }
    /**
     * Retrieves all service IDs from cache or database.
     */
    private List<String> getAllServiceIds() {
        synchronized (cacheLock) {
            if (cachedServiceIds.isEmpty()) {
                cachedServiceIds = readOnlyDatabaseService.getRecordIdsByCriteria("service", Map.of());
            }
            return new ArrayList<>(cachedServiceIds);
        }
    }
    /**
     * Retrieves IDs of services with status "START" or "AUTO".
     */
    private List<String> getAutoStartServices() {
        Map<String, Object> criteria = Map.of(
            "OR", List.of(
                Map.of("status", Map.of("EQ", "START")),
                Map.of("status", Map.of("EQ", "AUTO"))
            )
        );
        return readOnlyDatabaseService.getRecordIdsByCriteria("service", criteria);
    }

    
     // Starts all services eligible for auto-start except CMS.

    private void startEligibleServices() {
        List<String> autoServices = getAutoStartServices();
        for (String sid : autoServices) {
            if (!"CMS".equalsIgnoreCase(sid)) {
                startService(sid);
            }
        }
    }

        /*private void startEligibleServices() {
            List<String> eligibleServiceIds = getAutoStartServices(); // Services is status START أو AUTO
            for (String serviceId : eligibleServiceIds) {
                if (!"CMS".equalsIgnoreCase(serviceId)) {
                    List<String> taskIds = readOnlyDatabaseService.getRecordIdsByCriteria("serviceTasks",Map.of("serviceId", Map.of("EQ", serviceId)));
                    if (!taskIds.isEmpty()) {
                        for (String taskId : taskIds) {
                            jobLogService.log("TASK", serviceId, "WILL_START", "Eligible task: " + taskId);
                            loggerClass.getLogger().info("loggerClass-Info "+"TASK", serviceId, "WILL_START", "Eligible task: " + taskId);
                        }
                        startService(serviceId);
                    } else {
                        jobLogService.log("SERVICE", serviceId, "SKIPPED", "No tasks found. Service will not start.");
                        loggerClass.getLogger().info("loggerClass-Info "+" SERVICE", serviceId, "SKIPPED", "No tasks found. Service will not start.");
                    }
                }
            }
        }*/

     // Checks if a service is currently running.
    public boolean isServiceRunning(String serviceId) {
        return runningServices.containsKey(serviceId);
    }
     // Retrieves detailed information about a service and its agents.
    public Map<String, Object> getServiceDetails(String serviceId) {
        AgentManager agentManager = runningServices.get(serviceId);
        if (agentManager == null) {
            return Map.of(
                "serviceId", serviceId,
                "status", "STOPPED",
                "agentCount", 0
            );
        }
        return Map.of(
            "serviceId", serviceId,
            "status", "RUNNING",
            "agentCount", agentManager.getAgentDetailsList().size(),
            "agentDetails", agentManager.getAgentDetailsList()
        );
    }
    /**
     * Retrieves details of all services.
     * @return list of maps containing details of all services
     */
    public List<Map<String, Object>> getAllServiceDetails() {
        return getAllServiceIds().stream()
                .map(this::getServiceDetails)
                .collect(Collectors.toList());
    }
    
     //Returns the state of all services in the system.
    public List<Map<String, Object>> getAllServiceStates() {
        return getAllServiceIds().stream()
                .map(this::getServiceState)
                .collect(Collectors.toList());
        }
    }
    
        
/*public String startService(String serviceId) {
        return runningServices.compute(serviceId, (key, existingManager) -> {
            if (existingManager != null) {
                jobLogService.logWarn("Service",serviceId," already running", key);
                return existingManager;
            }
            Map<String, Object> serviceRecord = readOnlyDatabaseService.getRecordById("service", key);
            if (serviceRecord == null) {
                jobLogService.logError("Service",serviceId, "not found", key,null);
                return null;
            }
            if (!"CMS".equalsIgnoreCase(key) && !isCmsRunning()) {
                jobLogService.logWarn("CMS not running."," Cannot start '{}'","", key);
                return null;
            }
            int agentCount = (int) serviceRecord.getOrDefault("countAgents", 1);
            AgentManager manager = new AgentManager(key, agentCount, jobService, kafkaTemplate, jobLogService, objectMapper, postRecordService);
            manager.start();
            //logger.info("Service '{}' started with {} agents", key, agentCount);
            //jobLogService.log("Service '{}'"+serviceId, "started with {} agents", key,""+ agentCount);
            jobLogService.log("SERVICE", key, "STARTED", "Service started with " + agentCount + " agent(s).");
            if ("CMS".equalsIgnoreCase(key)) {
                startEligibleServices();
                startMonitorThread();
            }
            return manager;
        }) != null ? "Service '" + serviceId + "' started successfully.": "Service '" + serviceId + "' is already running.";
        /*!= null ? "CMS Starte Successfull" : "CMS Failed";*//******* 
      //  return "Service '" + serviceId + "' is already running.";
    }*/
        /*private void startMonitorThread() {
        monitorThread = new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(3000);
                    AgentManager mainAgent = runningServices.get("CMS");
                    if (mainAgent == null || !mainAgent.isAlive()) {
                        System.out.println("[Monitor] CMS is no longer alive. Stopping all agents...");
                        stopAllServices();
                        break;
                    }
                } catch (InterruptedException e) {
                    break;
                }
            }
        });
        monitorThread.setDaemon(true);
        monitorThread.start();
    }*/

    /*private void stopMonitorThread() {
        if (monitorThread != null && monitorThread.isAlive()) {
            monitorThread.interrupt();
        }
    }*/
    
    /*public String startService(String serviceId) {
        if (runningServices.containsKey(serviceId)) {
            return "Service '" + serviceId + "' is already running.";
        }
        Map<String, Object> serviceRecord = readOnlyDatabaseService.getRecordById("service", serviceId);
        if (serviceRecord == null) {
            return "Service '" + serviceId + "' not found.";
        }
         //mainService
        // Check CMS if we are trying to run another service
        if (!"CMS".equalsIgnoreCase(serviceId)) {
            AgentManager mainAgent = runningServices.get("CMS");
            if (mainAgent == null || !mainAgent.isAlive()) {
                return "Cannot start '" + serviceId + "' because CMS is not running.";
            }
        }

        // RUNNING Agents for this service
        int agentCount = (int) serviceRecord.getOrDefault("countAgents", 1);
        AgentManager agentManager = new AgentManager(serviceId,agentCount, jobService, kafkaTemplate, jobLogService, objectMapper,postRecordService);
        agentManager.start();
        runningServices.put(serviceId, agentManager);
        activeServices.put(serviceId, agentManager);

        // if this is CMS run all service with a status AUTO/START 
        if ("CMS".equalsIgnoreCase(serviceId)) {
            startEligibleServices();
            startMonitorThread();
        }
       // return "Service '" + serviceId + "' started successfully.";
        return "Service '" + serviceId + "' started with " + agentCount + " agents.";
        jobLogService.log("Service '" , serviceId ,"' started with " + agentCount , " agents.");
    }*/

    /*public String stopService(String serviceId) {
        AgentManager agentManager = runningServices.remove(serviceId);
        activeServices.remove(serviceId);
        if (agentManager == null) {
            return "Service '" + serviceId + "' is not running.";
        }
        agentManager.stop();
        if ("CMS".equalsIgnoreCase(serviceId)) {
            stopAllServices();
            stopMonitorThread();
        }
        return "Service '" + serviceId + "' stopped successfully.";
    }*/

            /*public Map<String, Object> getServiceState(String serviceId) {
            AgentManager agent = runningServices.get(serviceId);
            if (agent == null) {
                return Map.of("serviceId", serviceId, "status", "STOPPED");
            }
            return Map.of("serviceId", serviceId, "status", "RUNNING", "lastTimeRunning", agent.getLatestHeartbeat());
        }*/
        
        /*public List<Map<String, Object>> getAllServiceStates() {
            List<Map<String, Object>> states = new ArrayList<>();
            for (String serviceId : getAllServiceIds()) {
                states.add(getServiceState(serviceId));
            }
            return states;
        }*/

        /*private List<String> getAllServiceIds() {
            return readOnlyDatabaseService.getRecordIdsByCriteria("service", Map.of());
        }*/
                /*private void stopAllServices() {
            List<String> servicesToStop = new ArrayList<>(runningServices.keySet());
            for (String sid : servicesToStop) {
                if (!"CMS".equalsIgnoreCase(sid)) {
                    stopService(sid);
                }
            }
        }*/

        /*public List<Map<String, Object>> getAllServiceDetails() {
            List<String> allIds = getAllServiceIds();
            return allIds.stream()
            .map(this::getServiceDetails).collect(Collectors.toList());
        }*/

        /*private Service getServiceData(String serviceId){
            Map<String,Object> servcieData=readOnlyDatabaseService.getRecordById("service", serviceId);
            return new Service(servcieData);
        }
        "serviceId", serviceId,
                    "status", service.getStatus(),
                    "agentCount", service.getCountAgents()********/



        /*private final Map<String, AgentManager> agents = new ConcurrentHashMap<>();
        
        public boolean startService(String serviceId) {
                agents.computeIfAbsent(serviceId, id -> {
                    AgentManager agent = new AgentManager(id); // إنشاؤه يدويًا
                    agent.start();
                    return agent;
                });
                return false;
            }
        */
        

                /* public boolean startService(String serviceId) {
            if (activeServices.containsKey(serviceId)) {
                return false;
            }
            AgentManager agent = new AgentManager(serviceId);
            agent.start();
            activeServices.put(serviceId, agent);
            return true;
        }*/
        /*public boolean stopService(String serviceId) {
            AgentManager agent = activeServices.remove(serviceId);
            if (agent != null) {
                agent.stop();
                return true;
            }
            return false;
        }*/

                /* public List<Map<String, Object>> getAllServiceStates() {
            List<Map<String, Object>> states = new ArrayList<>();
            for (String serviceId : activeServices.keySet()) {
                states.add(getServiceState(serviceId));
            }
            return states;
        }*/
    
        /*  private final ReadOnlyDatabaseService readOnlyDatabaseService;
        private final AgentManager agentManager;
    
        // حفظ حالة تشغيل الخدمات
        private final Map<String, Boolean> serviceStatusMap = new ConcurrentHashMap<>();
    
        public ServiceManager(ReadOnlyDatabaseService readOnlyDatabaseService,
                            AgentManager agentManager) {
            this.readOnlyDatabaseService = readOnlyDatabaseService;
            this.agentManager = agentManager;
        }
    
        public List<Service> getAllServices() {
            List<String> ids = readOnlyDatabaseService.getRecordIdsByCriteria("service", Map.of());
            List<Service> services = new ArrayList<>();
    
            for (String id : ids) {
                Map<String, Object> record = readOnlyDatabaseService.getRecordById("service", id);
                Service service = new Service(record);
                service.setRunning(serviceStatusMap.getOrDefault(service.getID(), false));
                services.add(service);
            }
    
            return services;
        }
    
        public boolean startService(String serviceId) {
            Map<String, Object> record = readOnlyDatabaseService.getRecordById("service", serviceId);
            if (record == null) return false;
    
            Service service = new Service(record);
            boolean started = agentManager.startAgentForService(service);
            if (started) {
                serviceStatusMap.put(serviceId, true);
            }
            return started;
        }
    
        public boolean stopService(String serviceId) {
            boolean stopped = agentManager.stopAgentForService(serviceId);
            if (stopped) {
                serviceStatusMap.put(serviceId, false);
            }
            return stopped;
        }
    
        public boolean isServiceRunning(String serviceId) {
            return serviceStatusMap.getOrDefault(serviceId, false);
        }*/
    
    
