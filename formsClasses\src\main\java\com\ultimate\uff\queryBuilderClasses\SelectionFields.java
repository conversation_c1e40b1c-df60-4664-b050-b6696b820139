package com.ultimate.uff.queryBuilderClasses;

import java.util.HashMap;
import java.util.Map;

public class SelectionFields {

    private String selectionField;
    private String operator;

    // Map to store dynamic/extra fields
    private Map<String, Object> extraFields = new HashMap<>();

    // Constructor to initialize from a map
    public SelectionFields(Map<String, Object> fieldDetail) {
        this.selectionField = (String) fieldDetail.get("selectionField");
        this.operator = (String) fieldDetail.get("operator");

        // Store any extra fields in the extraFields map
        for (Map.Entry<String, Object> entry : fieldDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    // Method to check if a field is explicitly handled
    private boolean isFieldHandled(String fieldName) {
        return fieldName.equals("selectionField") || fieldName.equals("operator");
    }

    // Convert SelectionFields object to Map to put back into recordDetail
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (this.selectionField != null) {
            map.put("selectionField", this.selectionField);
        }
        if (this.operator != null) {
            map.put("operator", this.operator);
        }
        // Add all extra fields to the map
        map.putAll(extraFields);
        return map;
    }

    // Getters and setters
    public String getSelectionField() {
        return selectionField;
    }

    public void setSelectionField(String selectionField) {
        this.selectionField = selectionField;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Map<String, Object> getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(Map<String, Object> extraFields) {
        this.extraFields = extraFields;
    }
}
