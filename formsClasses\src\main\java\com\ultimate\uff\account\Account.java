package com.ultimate.uff.account;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Account extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String customer;
    private final FieldValue<String> customerVal = new FieldValue<>(null);
    private List<String> accountJointHolder = new ArrayList<>();
    private final FieldValue<List<String>> accountJointHolderVal = new FieldValue<>(new ArrayList<>());
    private String currency;
    private final FieldValue<String> currencyVal = new FieldValue<>(null);
    private String category;
    private final FieldValue<String> categoryVal = new FieldValue<>(null);
    private String description;
    private final FieldValue<String> descriptionVal = new FieldValue<>(null);
    private String accountOpeningPurpose;
    private final FieldValue<String> accountOpeningPurposeVal = new FieldValue<>(null);
    private String currencyMarket;
    private final FieldValue<String> currencyMarketVal = new FieldValue<>(null);
    private String limitReference;
    private final FieldValue<String> limitReferenceVal = new FieldValue<>(null);
    private Double workingBalance;
    private final FieldValue<Double> workingBalanceVal = new FieldValue<>(null);
    private Double availableBalance;
    private final FieldValue<Double> availableBalanceVal = new FieldValue<>(null);
    private String accountOpening;
    private final FieldValue<String> accountOpeningVal = new FieldValue<>(null);
    private String dormancy;
    private final FieldValue<String> dormancyVal = new FieldValue<>(null);
    private String accountReactivation;
    private final FieldValue<String> accountReactivationVal = new FieldValue<>(null);
    private String accountConstraints;
    private final FieldValue<String> accountConstraintsVal = new FieldValue<>(null);
    private String accountClosure;
    private final FieldValue<String> accountClosureVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private String recordAuthorizer;
    private final FieldValue<String> recordAuthorizerVal = new FieldValue<>(null);
    private int recordCount;
    private final FieldValue<Integer> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public Account() {}

    public Account(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        this.setCustomer(( String ) recordDetail.get("customer"));
        Object listObj = recordDetail.get("accountJointHolder");
        if (listObj instanceof List<?>) {
            this.setAccountJointHolder((List<String>) listObj);
        }
        this.setCurrency(( String ) recordDetail.get("currency"));
        this.setCategory(( String ) recordDetail.get("category"));
        this.setDescription(( String ) recordDetail.get("description"));
        this.setAccountOpeningPurpose(( String ) recordDetail.get("accountOpeningPurpose"));
        this.setCurrencyMarket(( String ) recordDetail.get("currencyMarket"));
        this.setLimitReference(( String ) recordDetail.get("limitReference"));
        this.setWorkingBalance(( Double ) recordDetail.get("workingBalance"));
        this.setAvailableBalance(( Double ) recordDetail.get("availableBalance"));
        this.setAccountOpening(( String ) recordDetail.get("accountOpening"));
        this.setDormancy(( String ) recordDetail.get("dormancy"));
        this.setAccountReactivation(( String ) recordDetail.get("accountReactivation"));
        this.setAccountConstraints(( String ) recordDetail.get("accountConstraints"));
        this.setAccountClosure(( String ) recordDetail.get("accountClosure"));
        this.setID(( String ) recordDetail.get("ID"));
        this.setRecordStatus(( String ) recordDetail.get("recordStatus"));
        this.setRecordInputter(( String ) recordDetail.get("recordInputter"));
        this.setRecordAuthorizer(( String ) recordDetail.get("recordAuthorizer"));
        this.setRecordCount(( Integer ) recordDetail.get("recordCount"));
        this.setDateTime(( String ) recordDetail.get("dateTime"));
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (customer != null) map.put("customer", customer);
        if (accountJointHolder != null) map.put("accountJointHolder", accountJointHolder);
        if (currency != null) map.put("currency", currency);
        if (category != null) map.put("category", category);
        if (description != null) map.put("description", description);
        if (accountOpeningPurpose != null) map.put("accountOpeningPurpose", accountOpeningPurpose);
        if (currencyMarket != null) map.put("currencyMarket", currencyMarket);
        if (limitReference != null) map.put("limitReference", limitReference);
        if (workingBalance != null) map.put("workingBalance", workingBalance);
        if (availableBalance != null) map.put("availableBalance", availableBalance);
        if (accountOpening != null) map.put("accountOpening", accountOpening);
        if (dormancy != null) map.put("dormancy", dormancy);
        if (accountReactivation != null) map.put("accountReactivation", accountReactivation);
        if (accountConstraints != null) map.put("accountConstraints", accountConstraints);
        if (accountClosure != null) map.put("accountClosure", accountClosure);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != 0.0) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "customer", "accountJointHolder", "currency", "category", "description", "accountOpeningPurpose", "currencyMarket", "limitReference", "workingBalance", "availableBalance", "accountOpening", "dormancy", "accountReactivation", "accountConstraints", "accountClosure", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setCustomer(String customer) {
        this.customer = customer;
        this.customerVal.setValue(customer);
    }
    public String getCustomer() { return this.customer; }
    public FieldValue<String> customerVal() { return customerVal; }
    public void setAccountJointHolder(List<String> accountJointHolder) {
        this.accountJointHolder = accountJointHolder;
        this.accountJointHolderVal.setValue(accountJointHolder);
    }
    public List<String> getAccountJointHolder() { return this.accountJointHolder; }
    public FieldValue<List<String>> accountJointHolderVal() { return accountJointHolderVal; }
    public void setCurrency(String currency) {
        this.currency = currency;
        this.currencyVal.setValue(currency);
    }
    public String getCurrency() { return this.currency; }
    public FieldValue<String> currencyVal() { return currencyVal; }
    public void setCategory(String category) {
        this.category = category;
        this.categoryVal.setValue(category);
    }
    public String getCategory() { return this.category; }
    public FieldValue<String> categoryVal() { return categoryVal; }
    public void setDescription(String description) {
        this.description = description;
        this.descriptionVal.setValue(description);
    }
    public String getDescription() { return this.description; }
    public FieldValue<String> descriptionVal() { return descriptionVal; }
    public void setAccountOpeningPurpose(String accountOpeningPurpose) {
        this.accountOpeningPurpose = accountOpeningPurpose;
        this.accountOpeningPurposeVal.setValue(accountOpeningPurpose);
    }
    public String getAccountOpeningPurpose() { return this.accountOpeningPurpose; }
    public FieldValue<String> accountOpeningPurposeVal() { return accountOpeningPurposeVal; }
    public void setCurrencyMarket(String currencyMarket) {
        this.currencyMarket = currencyMarket;
        this.currencyMarketVal.setValue(currencyMarket);
    }
    public String getCurrencyMarket() { return this.currencyMarket; }
    public FieldValue<String> currencyMarketVal() { return currencyMarketVal; }
    public void setLimitReference(String limitReference) {
        this.limitReference = limitReference;
        this.limitReferenceVal.setValue(limitReference);
    }
    public String getLimitReference() { return this.limitReference; }
    public FieldValue<String> limitReferenceVal() { return limitReferenceVal; }
    public void setWorkingBalance(Double workingBalance) {
        this.workingBalance = workingBalance;
        this.workingBalanceVal.setValue(workingBalance);
    }
    public Double getWorkingBalance() { return this.workingBalance; }
    public FieldValue<Double> workingBalanceVal() { return workingBalanceVal; }
    public void setAvailableBalance(Double availableBalance) {
        this.availableBalance = availableBalance;
        this.availableBalanceVal.setValue(availableBalance);
    }
    public Double getAvailableBalance() { return this.availableBalance; }
    public FieldValue<Double> availableBalanceVal() { return availableBalanceVal; }
    public void setAccountOpening(String accountOpening) {
        this.accountOpening = accountOpening;
        this.accountOpeningVal.setValue(accountOpening);
    }
    public String getAccountOpening() { return this.accountOpening; }
    public FieldValue<String> accountOpeningVal() { return accountOpeningVal; }
    public void setDormancy(String dormancy) {
        this.dormancy = dormancy;
        this.dormancyVal.setValue(dormancy);
    }
    public String getDormancy() { return this.dormancy; }
    public FieldValue<String> dormancyVal() { return dormancyVal; }
    public void setAccountReactivation(String accountReactivation) {
        this.accountReactivation = accountReactivation;
        this.accountReactivationVal.setValue(accountReactivation);
    }
    public String getAccountReactivation() { return this.accountReactivation; }
    public FieldValue<String> accountReactivationVal() { return accountReactivationVal; }
    public void setAccountConstraints(String accountConstraints) {
        this.accountConstraints = accountConstraints;
        this.accountConstraintsVal.setValue(accountConstraints);
    }
    public String getAccountConstraints() { return this.accountConstraints; }
    public FieldValue<String> accountConstraintsVal() { return accountConstraintsVal; }
    public void setAccountClosure(String accountClosure) {
        this.accountClosure = accountClosure;
        this.accountClosureVal.setValue(accountClosure);
    }
    public String getAccountClosure() { return this.accountClosure; }
    public FieldValue<String> accountClosureVal() { return accountClosureVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(String recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public String getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<String> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public int getRecordCount() { return this.recordCount; }
    public FieldValue<Integer> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}
