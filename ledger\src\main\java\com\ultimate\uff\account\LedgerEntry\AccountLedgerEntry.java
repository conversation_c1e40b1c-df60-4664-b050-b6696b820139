package com.ultimate.uff.account.LedgerEntry;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class AccountLedgerEntry extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private Integer ledgerId;
    private final FieldValue<Integer> ledgerIdVal = new FieldValue<>(null);
    private String accountNumber;
    private final FieldValue<String> accountNumberVal = new FieldValue<>(null);
    private String customerNumber;
    private final FieldValue<String> customerNumberVal = new FieldValue<>(null);
    private String transactionReference;
    private final FieldValue<String> transactionReferenceVal = new FieldValue<>(null);
    private String valueDate;
    private final FieldValue<String> valueDateVal = new FieldValue<>(null);
    private String bookingDate;
    private final FieldValue<String> bookingDateVal = new FieldValue<>(null);
    private String processingDate;
    private final FieldValue<String> processingDateVal = new FieldValue<>(null);
    private String exposureDate;
    private final FieldValue<String> exposureDateVal = new FieldValue<>(null);
    private Double amountLcy;
    private final FieldValue<Double> amountLcyVal = new FieldValue<>(null);
    private Double amountFcy;
    private final FieldValue<Double> amountFcyVal = new FieldValue<>(null);
    private Double drAmountLcy;
    private final FieldValue<Double> drAmountLcyVal = new FieldValue<>(null);
    private Double crAmountLcy;
    private final FieldValue<Double> crAmountLcyVal = new FieldValue<>(null);
    private Double drAmountFcy;
    private final FieldValue<Double> drAmountFcyVal = new FieldValue<>(null);
    private Double crAmountFcy;
    private final FieldValue<Double> crAmountFcyVal = new FieldValue<>(null);
    private String currencyCode;
    private final FieldValue<String> currencyCodeVal = new FieldValue<>(null);
    private Double currencyRate;
    private final FieldValue<Double> currencyRateVal = new FieldValue<>(null);
    private String currencyMarket;
    private final FieldValue<String> currencyMarketVal = new FieldValue<>(null);
    private String entryDrCr;
    private final FieldValue<String> entryDrCrVal = new FieldValue<>(null);
    private String thierReference;
    private final FieldValue<String> thierReferenceVal = new FieldValue<>(null);
    private String ourReference;
    private final FieldValue<String> ourReferenceVal = new FieldValue<>(null);
    private String narrative;
    private final FieldValue<String> narrativeVal = new FieldValue<>(null);
    private String entryTimestamp;
    private final FieldValue<String> entryTimestampVal = new FieldValue<>(null);
    private String entrySequenceNo;
    private final FieldValue<String> entrySequenceNoVal = new FieldValue<>(null);
    private Integer totalEntries;
    private final FieldValue<Integer> totalEntriesVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private String recordAuthorizer;
    private final FieldValue<String> recordAuthorizerVal = new FieldValue<>(null);
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public AccountLedgerEntry() {}

    public AccountLedgerEntry(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        this.setLedgerId((Integer) recordDetail.get("ledgerId"));
        this.setAccountNumber((String) recordDetail.get("accountNumber"));
        this.setCustomerNumber((String) recordDetail.get("customerNumber"));
        this.setTransactionReference((String) recordDetail.get("transactionReference"));
        this.setValueDate((String) recordDetail.get("valueDate"));
        this.setBookingDate((String) recordDetail.get("bookingDate"));
        this.setProcessingDate((String) recordDetail.get("processingDate"));
        this.setExposureDate((String) recordDetail.get("exposureDate"));
        Object amountLcyObj = recordDetail.get("amountLcy");
        if (amountLcyObj instanceof Number) {
            this.setAmountLcy(((Number) amountLcyObj).doubleValue());
        }
        Object amountFcyObj = recordDetail.get("amountFcy");
        if (amountFcyObj instanceof Number) {
            this.setAmountFcy(((Number) amountFcyObj).doubleValue());
        }
        Object drAmountLcyObj = recordDetail.get("drAmountLcy");
        if (drAmountLcyObj instanceof Number) {
            this.setDrAmountLcy(((Number) drAmountLcyObj).doubleValue());
        }
        Object crAmountLcyObj = recordDetail.get("crAmountLcy");
        if (crAmountLcyObj instanceof Number) {
            this.setCrAmountLcy(((Number) crAmountLcyObj).doubleValue());
        }
        Object drAmountFcyObj = recordDetail.get("drAmountFcy");
        if (drAmountFcyObj instanceof Number) {
            this.setDrAmountFcy(((Number) drAmountFcyObj).doubleValue());
        }
        Object crAmountFcyObj = recordDetail.get("crAmountFcy");
        if (crAmountFcyObj instanceof Number) {
            this.setCrAmountFcy(((Number) crAmountFcyObj).doubleValue());
        }
        this.setCurrencyCode((String) recordDetail.get("currencyCode"));
        Object currencyRateObj = recordDetail.get("currencyRate");
        if (currencyRateObj instanceof Number) {
            this.setCurrencyRate(((Number) currencyRateObj).doubleValue());
        }
        this.setCurrencyMarket((String) recordDetail.get("currencyMarket"));
        this.setEntryDrCr((String) recordDetail.get("entryDrCr"));
        this.setThierReference((String) recordDetail.get("thierReference"));
        this.setOurReference((String) recordDetail.get("ourReference"));
        this.setNarrative((String) recordDetail.get("narrative"));
        this.setEntryTimestamp((String) recordDetail.get("entryTimestamp"));
        this.setEntrySequenceNo((String) recordDetail.get("entrySequenceNo"));
        this.setTotalEntries((Integer) recordDetail.get("totalEntries"));
        this.setID((String) recordDetail.get("ID"));
        this.setRecordStatus((String) recordDetail.get("recordStatus"));
        this.setRecordInputter((String) recordDetail.get("recordInputter"));
        this.setRecordAuthorizer((String) recordDetail.get("recordAuthorizer"));
        this.setRecordCount((String) recordDetail.get("recordCount"));
        this.setDateTime((String) recordDetail.get("dateTime"));
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (ledgerId != null) map.put("ledgerId", ledgerId);
        if (accountNumber != null) map.put("accountNumber", accountNumber);
        if (customerNumber != null) map.put("customerNumber", customerNumber);
        if (transactionReference != null) map.put("transactionReference", transactionReference);
        if (valueDate != null) map.put("valueDate", valueDate);
        if (bookingDate != null) map.put("bookingDate", bookingDate);
        if (processingDate != null) map.put("processingDate", processingDate);
        if (exposureDate != null) map.put("exposureDate", exposureDate);
        if (amountLcy != null) map.put("amountLcy", amountLcy);
        if (amountFcy != null) map.put("amountFcy", amountFcy);
        if (drAmountLcy != null) map.put("drAmountLcy", drAmountLcy);
        if (crAmountLcy != null) map.put("crAmountLcy", crAmountLcy);
        if (drAmountFcy != null) map.put("drAmountFcy", drAmountFcy);
        if (crAmountFcy != null) map.put("crAmountFcy", crAmountFcy);
        if (currencyCode != null) map.put("currencyCode", currencyCode);
        if (currencyRate != null) map.put("currencyRate", currencyRate);
        if (currencyMarket != null) map.put("currencyMarket", currencyMarket);
        if (entryDrCr != null) map.put("entryDrCr", entryDrCr);
        if (thierReference != null) map.put("thierReference", thierReference);
        if (ourReference != null) map.put("ourReference", ourReference);
        if (narrative != null) map.put("narrative", narrative);
        if (entryTimestamp != null) map.put("entryTimestamp", entryTimestamp);
        if (entrySequenceNo != null) map.put("entrySequenceNo", entrySequenceNo);
        if (totalEntries != null) map.put("totalEntries", totalEntries);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "ledgerId", "accountNumber", "customerNumber", "transactionReference", "valueDate", "bookingDate", "processingDate", "exposureDate", "amountLcy", "amountFcy", "drAmountLcy", "crAmountLcy", "drAmountFcy", "crAmountFcy", "currencyCode", "currencyRate", "currencyMarket", "entryDrCr", "thierReference", "ourReference", "narrative", "entryTimestamp", "entrySequenceNo", "totalEntries", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setLedgerId(Integer ledgerId) {
        this.ledgerId = ledgerId;
        this.ledgerIdVal.setValue(ledgerId);
    }
    public Integer getLedgerId() { return this.ledgerId; }
    public FieldValue<Integer> ledgerIdVal() { return ledgerIdVal; }
    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
        this.accountNumberVal.setValue(accountNumber);
    }
    public String getAccountNumber() { return this.accountNumber; }
    public FieldValue<String> accountNumberVal() { return accountNumberVal; }
    public void setCustomerNumber(String customerNumber) {
        this.customerNumber = customerNumber;
        this.customerNumberVal.setValue(customerNumber);
    }
    public String getCustomerNumber() { return this.customerNumber; }
    public FieldValue<String> customerNumberVal() { return customerNumberVal; }
    public void setTransactionReference(String transactionReference) {
        this.transactionReference = transactionReference;
        this.transactionReferenceVal.setValue(transactionReference);
    }
    public String getTransactionReference() { return this.transactionReference; }
    public FieldValue<String> transactionReferenceVal() { return transactionReferenceVal; }
    public void setValueDate(String valueDate) {
        this.valueDate = valueDate;
        this.valueDateVal.setValue(valueDate);
    }
    public String getValueDate() { return this.valueDate; }
    public FieldValue<String> valueDateVal() { return valueDateVal; }
    public void setBookingDate(String bookingDate) {
        this.bookingDate = bookingDate;
        this.bookingDateVal.setValue(bookingDate);
    }
    public String getBookingDate() { return this.bookingDate; }
    public FieldValue<String> bookingDateVal() { return bookingDateVal; }
    public void setProcessingDate(String processingDate) {
        this.processingDate = processingDate;
        this.processingDateVal.setValue(processingDate);
    }
    public String getProcessingDate() { return this.processingDate; }
    public FieldValue<String> processingDateVal() { return processingDateVal; }
    public void setExposureDate(String exposureDate) {
        this.exposureDate = exposureDate;
        this.exposureDateVal.setValue(exposureDate);
    }
    public String getExposureDate() { return this.exposureDate; }
    public FieldValue<String> exposureDateVal() { return exposureDateVal; }
    public void setAmountLcy(Double amountLcy) {
        this.amountLcy = amountLcy;
        this.amountLcyVal.setValue(amountLcy);
    }
    public Double getAmountLcy() { return this.amountLcy; }
    public FieldValue<Double> amountLcyVal() { return amountLcyVal; }
    public void setAmountFcy(Double amountFcy) {
        this.amountFcy = amountFcy;
        this.amountFcyVal.setValue(amountFcy);
    }
    public Double getAmountFcy() { return this.amountFcy; }
    public FieldValue<Double> amountFcyVal() { return amountFcyVal; }
    public void setDrAmountLcy(Double drAmountLcy) {
        this.drAmountLcy = drAmountLcy;
        this.drAmountLcyVal.setValue(drAmountLcy);
    }
    public Double getDrAmountLcy() { return this.drAmountLcy; }
    public FieldValue<Double> drAmountLcyVal() { return drAmountLcyVal; }
    public void setCrAmountLcy(Double crAmountLcy) {
        this.crAmountLcy = crAmountLcy;
        this.crAmountLcyVal.setValue(crAmountLcy);
    }
    public Double getCrAmountLcy() { return this.crAmountLcy; }
    public FieldValue<Double> crAmountLcyVal() { return crAmountLcyVal; }
    public void setDrAmountFcy(Double drAmountFcy) {
        this.drAmountFcy = drAmountFcy;
        this.drAmountFcyVal.setValue(drAmountFcy);
    }
    public Double getDrAmountFcy() { return this.drAmountFcy; }
    public FieldValue<Double> drAmountFcyVal() { return drAmountFcyVal; }
    public void setCrAmountFcy(Double crAmountFcy) {
        this.crAmountFcy = crAmountFcy;
        this.crAmountFcyVal.setValue(crAmountFcy);
    }
    public Double getCrAmountFcy() { return this.crAmountFcy; }
    public FieldValue<Double> crAmountFcyVal() { return crAmountFcyVal; }
    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
        this.currencyCodeVal.setValue(currencyCode);
    }
    public String getCurrencyCode() { return this.currencyCode; }
    public FieldValue<String> currencyCodeVal() { return currencyCodeVal; }
    public void setCurrencyRate(Double currencyRate) {
        this.currencyRate = currencyRate;
        this.currencyRateVal.setValue(currencyRate);
    }
    public Double getCurrencyRate() { return this.currencyRate; }
    public FieldValue<Double> currencyRateVal() { return currencyRateVal; }
    public void setCurrencyMarket(String currencyMarket) {
        this.currencyMarket = currencyMarket;
        this.currencyMarketVal.setValue(currencyMarket);
    }
    public String getCurrencyMarket() { return this.currencyMarket; }
    public FieldValue<String> currencyMarketVal() { return currencyMarketVal; }
    public void setEntryDrCr(String entryDrCr) {
        this.entryDrCr = entryDrCr;
        this.entryDrCrVal.setValue(entryDrCr);
    }
    public String getEntryDrCr() { return this.entryDrCr; }
    public FieldValue<String> entryDrCrVal() { return entryDrCrVal; }
    public void setThierReference(String thierReference) {
        this.thierReference = thierReference;
        this.thierReferenceVal.setValue(thierReference);
    }
    public String getThierReference() { return this.thierReference; }
    public FieldValue<String> thierReferenceVal() { return thierReferenceVal; }
    public void setOurReference(String ourReference) {
        this.ourReference = ourReference;
        this.ourReferenceVal.setValue(ourReference);
    }
    public String getOurReference() { return this.ourReference; }
    public FieldValue<String> ourReferenceVal() { return ourReferenceVal; }
    public void setNarrative(String narrative) {
        this.narrative = narrative;
        this.narrativeVal.setValue(narrative);
    }
    public String getNarrative() { return this.narrative; }
    public FieldValue<String> narrativeVal() { return narrativeVal; }
    public void setEntryTimestamp(String entryTimestamp) {
        this.entryTimestamp = entryTimestamp;
        this.entryTimestampVal.setValue(entryTimestamp);
    }
    public String getEntryTimestamp() { return this.entryTimestamp; }
    public FieldValue<String> entryTimestampVal() { return entryTimestampVal; }
    public void setEntrySequenceNo(String entrySequenceNo) {
        this.entrySequenceNo = entrySequenceNo;
        this.entrySequenceNoVal.setValue(entrySequenceNo);
    }
    public String getEntrySequenceNo() { return this.entrySequenceNo; }
    public FieldValue<String> entrySequenceNoVal() { return entrySequenceNoVal; }
    public void setTotalEntries(Integer totalEntries) {
        this.totalEntries = totalEntries;
        this.totalEntriesVal.setValue(totalEntries);
    }
    public Integer getTotalEntries() { return this.totalEntries; }
    public FieldValue<Integer> totalEntriesVal() { return totalEntriesVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(String recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public String getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<String> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}

