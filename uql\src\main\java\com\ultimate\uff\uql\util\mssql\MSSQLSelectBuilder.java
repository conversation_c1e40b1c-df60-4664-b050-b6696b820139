package com.ultimate.uff.uql.util.mssql;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import static com.ultimate.uff.uql.util.mssql.MSSQLExpressionBuilder.wrapCast;

public class MSSQLSelectBuilder {

    private final MSSQLConditionBuilder conditionBuilder = new MSSQLConditionBuilder();

    public String getRecordById(String tableName, String id) {

        MSSQLUtils.validateInput(id, "ID");

        return new StringBuilder()
                .append("SELECT JSON_ROW_DETAIL FROM ")
                .append(MSSQLUtils.escapeIdentifier(tableName))
                .append(" WHERE ROW_ID = '")
                .append(id.replace("'", "''")) // Escape single quotes in the ID
                .append("'")
                .toString();
    }

    public String getRecordsByCriteria(String tableName, Map<String, Object> criteriaJson) {
        // Handle _limit = 0 early to avoid SQL Server FETCH clause errors
        if (criteriaJson != null && criteriaJson.containsKey("_limit")) {
            Object limit = criteriaJson.get("_limit");
            if (limit instanceof Number && ((Number) limit).intValue() == 0) {
                return "SELECT TOP 0 ROW_ID, JSON_ROW_DETAIL FROM " + MSSQLUtils.escapeIdentifier(tableName);
            }
        }

        Map<String, Object> cloned = new HashMap<>(criteriaJson);

        // Extract metadata
        Object sortObj = cloned.remove("_sort");
        Object limitObj = cloned.remove("_limit");
        Object offsetObj = cloned.remove("_offset");
        Object selectObj = cloned.remove("_select"); 

        // TODO: Add support for _group, _count if needed later

        StringBuilder query = new StringBuilder("SELECT ");
        appendSelectClause(query, selectObj); // ✅ passing selectObj
        query.append(" FROM ");
        query.append(MSSQLUtils.escapeIdentifier(tableName));
    
        if (!cloned.isEmpty()) {
            appendWhereClause(query, cloned);
        }

        appendOrderByClause(query, sortObj);
        appendLimitOffsetClause(query, sortObj, limitObj, offsetObj);

        return query.toString();
    }

    private void appendOrderByClause(StringBuilder query, Object sortObj) {
        List<String> sortList = new ArrayList<>();

        if (sortObj instanceof List<?>) {
            for (Object obj : (List<?>) sortObj) {
                if (obj instanceof String s) {
                    // Validate: skip array-based or group-based fields
                    if (!s.contains("[]") && !s.contains("."))
                        sortList.add(s);
                }
            }
        }

        if (!sortList.isEmpty()) {
            String orderClause = sortList.stream()
                    .map(this::buildNullsLastOrderClause)
                    .collect(Collectors.joining(", "));
            query.append(" ORDER BY ").append(orderClause);
        }   
    }

    private void appendLimitOffsetClause(StringBuilder query, Object sortObj, Object limitObj, Object offsetObj) {
        boolean needsOrder = (sortObj == null);
        long offset = offsetObj instanceof Number ? ((Number) offsetObj).longValue() : 0;
        long limit = limitObj instanceof Number ? ((Number) limitObj).longValue() : Long.MAX_VALUE;

        if (limit < Long.MAX_VALUE || offset > 0) {
            if (needsOrder) {
                query.append(" ORDER BY (SELECT NULL)");
            }
            query.append(" OFFSET ").append(offset).append(" ROWS");
            if (limit < Long.MAX_VALUE) {
                query.append(" FETCH NEXT ").append(limit).append(" ROWS ONLY");
            }
        }
    }

    private void appendWhereClause(StringBuilder query, Map<String, Object> criteriaBody) {
        if (!criteriaBody.isEmpty()) {
            String where = conditionBuilder.buildWhereClauseFromJson(criteriaBody);
            if (!where.isEmpty()) {
                query.append(" WHERE ").append(where);
            }
        }
    }

    private String buildNullsLastOrderClause(String rawField) {
        String fieldExpr = MSSQLUtils.escapeOrderClauseJson(rawField);
    
        boolean desc = false;
        if (fieldExpr.toUpperCase().endsWith(" DESC")) {
            desc = true;
            fieldExpr = fieldExpr.substring(0, fieldExpr.length() - 5).trim();
        } else if (fieldExpr.toUpperCase().endsWith(" ASC")) {
            fieldExpr = fieldExpr.substring(0, fieldExpr.length() - 4).trim();
        }
    
        String direction = desc ? "DESC" : "ASC";
    
        return "CASE WHEN " + fieldExpr + " IS NULL THEN 1 ELSE 0 END, " + fieldExpr + " " + direction;
    }

    private void appendSelectClause(StringBuilder query, Object selectObj) {
        if (!(selectObj instanceof List<?> fieldsList)) {
            query.append("ROW_ID, JSON_ROW_DETAIL");
            return;
        }
    
        List<String> selectExpressions = new ArrayList<>();
        selectExpressions.add("ROW_ID"); // Always include ROW_ID
    
        for (Object fieldObj : fieldsList) {
            if (!(fieldObj instanceof String)) continue;
    
            String field = (String) fieldObj;
            String typeSuffix = null;
    
            int typeIndex = field.lastIndexOf(':');
            if (typeIndex != -1 && typeIndex < field.length() - 1) {
                typeSuffix = field.substring(typeIndex + 1);
                field = field.substring(0, typeIndex);
            }
    
            String alias = field.replace("[]", "");
    
            // Handle multi field with subpath: e.g. nestedArray[].nestedKey
            if (field.contains("[]") && field.contains(".")) {
                String jsonExpr = buildDeepMultiFieldAggregation(field, alias);
                //selectExpressions.add(jsonExpr + " AS " + alias);
                selectExpressions.add(jsonExpr);
                continue;
            }            
    
            // Handle full multi field (e.g. multiField[])
            if (field.endsWith("[]")) {
                String path = field.replace("[]", "");
                String jsonExpr = "JSON_QUERY(JSON_ROW_DETAIL, '$." + path + "')";
                selectExpressions.add(jsonExpr + " AS [" + alias + "]");
                continue;
            }
    
            // Handle scalar (with optional type)
            String jsonExpr = "JSON_VALUE(JSON_ROW_DETAIL, '$." + field + "')";
            if (typeSuffix != null) {
                jsonExpr = wrapCast(jsonExpr, typeSuffix);
            }
    
            selectExpressions.add(jsonExpr + " AS [" + alias + "]");
        }
    
        query.append(String.join(", ", selectExpressions));
    }

    private String buildDeepMultiFieldAggregation(String field, String alias) {
        // Split into hierarchy: e.g. nestedArray[].nestedKey[].subkey[].subofsub
        String[] parts = field.split("\\[]\\.");
        StringBuilder fromClause = new StringBuilder();
        StringBuilder aliasBuilder = new StringBuilder();
        String base = "JSON_ROW_DETAIL";
        String finalField = parts[parts.length - 1]; // subofsub
    
        String prev = "lvl0";
        fromClause.append("FROM OPENJSON(JSON_QUERY(").append(base).append(", '$.").append(parts[0].replace(".", "~")).append("')) AS ").append(prev);
        aliasBuilder.append(prev).append(".value");
    
        for (int i = 1; i < parts.length - 1; i++) {
            String level = "lvl" + i;
            fromClause.append("\nCROSS APPLY OPENJSON(JSON_QUERY(").append(aliasBuilder).append(", '$.").append(parts[i]).append("')) AS ").append(level);
            aliasBuilder.setLength(0);
            aliasBuilder.append(level).append(".value");
            prev = level;
        }
    
        String lastLevel = "lvl" + (parts.length - 1);
        fromClause.append("\nCROSS APPLY OPENJSON(JSON_QUERY(").append(aliasBuilder).append("))\n")
                  .append("WITH (").append(finalField).append(" NVARCHAR(MAX) '$.").append(finalField).append("') AS ").append(lastLevel);
    
        // Fix: Remove duplicate alias in SELECT part
        return "(SELECT '[' + STRING_AGG(QUOTENAME(" + lastLevel + "." + finalField + ", '\"'), ',') + ']'\n" +
               fromClause + ") AS [" + alias.replace("~", ".") + "]"; 
    }     
    
}
