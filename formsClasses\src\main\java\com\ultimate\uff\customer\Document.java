package com.ultimate.uff.customer;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class Document extends ValidatableRecord {

    private final FieldValue<String> documentType = new FieldValue<>(null);
    private final FieldValue<Date> documentIssueDate = new FieldValue<>(null);
    private final FieldValue<Date> docExpairyDate = new FieldValue<>(null);
    private final Map<String, Object> extraFields = new HashMap<>();

    public Document(Map<String, Object> fieldDetail) {
        documentType.setValue((String) fieldDetail.get("documentType"));
        documentIssueDate.setValue((Date) fieldDetail.get("documentIssueDate"));
        docExpairyDate.setValue((Date) fieldDetail.get("docExpairyDate"));

        for (Map.Entry<String, Object> entry : fieldDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    private boolean isFieldHandled(String fieldName) {
        return fieldName.equals("documentType") || fieldName.equals("documentIssueDate") || fieldName.equals("docExpairyDate");
    }

    public FieldValue<String> getDocumentType() { return documentType; }
    public FieldValue<Date> getDocumentIssueDate() { return documentIssueDate; }
    public FieldValue<Date> getDocExpairyDate() { return docExpairyDate; }
    public Map<String, Object> getExtraFields() { return extraFields; }
}
