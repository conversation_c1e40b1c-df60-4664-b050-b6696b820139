package com.ultimate.uff.multi.thread.agents;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.multi.thread.jobs.JobLogService;
import com.ultimate.uff.multi.thread.jobs.JobService;
import com.ultimate.uff.multi.thread.services.AgentConsumer;
import com.ultimate.uff.multi.thread.services.LoggerClass;

import org.springframework.kafka.core.KafkaTemplate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import ch.qos.logback.classic.Logger;

/**
 * AgentManager is responsible for managing a group of AgentConsumer threads
 * that are tied to a specific service. It handles starting, stopping, and monitoring
 * the state of these agents, including heartbeat tracking and logging.
 */
public class AgentManager {

    private final String serviceId;                // Unique identifier for the service this manager controls
    private final int agentCount;                   // Number of agents managed
    private final List<AgentConsumer> consumers = new CopyOnWriteArrayList<>();                           // Thread-safe list of agent consumers
    private final ExecutorService executor;                                                               // Thread pool for executing agent tasks
    private final List<AtomicReference<LocalDateTime>> heartbeats = new CopyOnWriteArrayList<>();                 // List of last heartbeat timestamps per agent
    private final JobLogService jobLogService;                                                                   // Service for logging to the database
     //private final Logger logger;                                                                                 // Logger for detailed file logging
    private volatile boolean alive = false;                                                                     // Flag indicating whether the agent manager is running
    private final LoggerClass loggerClass;


    /**
     * Constructor initializes the AgentManager with the given parameters,
     * creates AgentConsumer instances, and prepares heartbeat tracking.
     */
public AgentManager(String serviceId,int countAgents,JobService jobService,KafkaTemplate<String, String> kafkaTemplate, JobLogService jobLogService,
                ObjectMapper objectMapper, LoggerClass loggerClass
                //PostRecordService postRecordService
                ) {
                    this.serviceId = serviceId;
                    this.executor = Executors.newFixedThreadPool(countAgents);
                    this.agentCount=countAgents;
                    this.jobLogService = jobLogService;
                    this.loggerClass = loggerClass;
                    
                    // Initialize agent consumers and heartbeat tracking
                    for (int i = 0; i < countAgents; i++) {
                        int index = i;
                        AgentConsumer consumer = new AgentConsumer(serviceId,() -> updateHeartbeat(index),jobService,kafkaTemplate,
                        jobLogService,objectMapper, loggerClass//,postRecordService
                        );
                        consumers.add(consumer);
                        heartbeats.add(new AtomicReference<>());
                    }
                }

    //Starts all agent consumers and logs the start operation.
    public void start() {
        alive = true;
        consumers.forEach(AgentConsumer::start);

        // Log to database
        jobLogService.logInfo("AGENT_MANAGER", serviceId, "START", "AgentManager started with " + agentCount + " agents.");

        // Log to external file
        loggerClass.getLogger().info("[AgentManager] Service '{}' started with {} agents", serviceId, agentCount);
    }
    /**
     * Stops all agent consumers and shuts down the thread pool.
     * Logs the stop operation and any errors encountered.
     */
    public void stop() {
        alive = false;
        consumers.forEach(AgentConsumer::stop);
        executor.shutdown();
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
                loggerClass.getLogger().warn("[AgentManager] Force shutdown for service '{}'", serviceId);
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
            // Log interruption to database and file
            jobLogService.logError("AGENT_MANAGER", serviceId, "STOP", "Interrupted while stopping: " + e.getMessage());
            loggerClass.getLogger().error("[AgentManager] Interrupted during shutdown for service '{}'", serviceId, e);
        }
         // Final log messages
        jobLogService.logInfo("AGENT_MANAGER", serviceId, "STOP", "AgentManager stopped.");
        loggerClass.getLogger().info("[AgentManager] Service '{}' stopped", serviceId);
    }

    //Updates the heartbeat timestamp of a specific agent.
    private void updateHeartbeat(int index) {
        if (index >= 0 && index < heartbeats.size()) {
            heartbeats.get(index).set(LocalDateTime.now());
        }
    }
    /*public Optional<LocalDateTime> getLastHeartbeat() {
        return heartbeats.stream()
            .filter(Objects::nonNull)
            .max(Comparator.naturalOrder());
        }*/

    //Returns the most recent heartbeat timestamp across all agents.
    public LocalDateTime getLatestHeartbeat() {
            return heartbeats.stream()
                .map(AtomicReference::get)
                .filter(Objects::nonNull)
                .max(LocalDateTime::compareTo)
                .orElse(null);
        }
        
    // Returns the total number of agents managed.
    public int getAgentCount() {
            return agentCount;
        }

    //Returns the number of active threads in the executor pool.
    public int getActiveAgentCount() {
        if (executor instanceof ThreadPoolExecutor) {
            return ((ThreadPoolExecutor) executor).getActiveCount();
        }
        return 0;
    }

    //Returns the number of agents that are currently running.
    public int getActiveAgents() {
            return (int) consumers.stream()
                .filter(AgentConsumer::isRunning)
                .count();
    }

    //Returns true if the agent manager is currently running.
    public boolean isAlive() {
        return alive;
    }

    //Returns a list of detailed information about each agent, including last run time and status.
    public List<Map<String, Object>> getAgentDetailsList() {
        List<Map<String, Object>> details = new ArrayList<>();
        for (int i = 0; i < consumers.size(); i++) {
            AgentConsumer consumer = consumers.get(i);
            //Thread thread = consumerThreads.get(i);
            AtomicReference<LocalDateTime> heartbeat = heartbeats.get(i);
            
            Map<String, Object> info = Map.of(
                "lastTimeRun", heartbeat.get(),
                "running", consumer.isRunning(),//.isAlive(),
                "Name", serviceId + "-Agent-" + i,
                "agentNumber", i
            );
            details.add(info);
        }
        return details;
    }

    //Returns a summary of the agent manager's current state and all agent details.
    public Map<String, Object> getAgentManagerDetails() {
        return Map.of(
            "serviceId", serviceId,
            "running", isAlive(),
            "agentCount", consumers.size(),
            "agents", getAgentDetailsList()
        );
    }
}
