package com.ultimate.uff.teller;

import com.ultimate.uff.account.LedgerEntry.AccountLedgerEntry;
import com.ultimate.uff.ledger.LedgerValidator;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.CustomValidator;
import com.ultimate.uff.validation.ValidationResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class TellerValidation implements CustomValidator {

    private static final String PL_ACCOUNT = "PL50063"; // Profit/Loss account
    private static final String BASE_CURRENCY = "YER";

    @Autowired
    private DatabaseService databaseService;
    @Autowired
    private DatabaseService da;
    @Autowired
    private LedgerValidator ledgerValidator;

    @Override
    public ValidationResponse validateWithResponse(String id, Map<String, Object> recordDetail) {
        Teller teller = new Teller(recordDetail);
        List<DebitUnit> debitDenomList = teller.getDebitUnitList();
        List<CreditUnit> creditDenomList = teller.getCreditUnitList();

        String debitCurrency = teller.getDebitCurrency();
        String creditCurrency = teller.getCreditCurrency();
        String txnCode = teller.getTransactionCode();
        TellerTransaction transaction = new TellerTransaction(da.getRecordById("tellerTransaction", txnCode));

        String debitAccountType = transaction.getAccountType1().getValue();
        String creditAccountType = transaction.getAccountType2().getValue();


        // ❌ Base currency should not have foreign amounts
        if (BASE_CURRENCY.equals(debitCurrency) && teller.getDebitAmountForeign() != null) {
            teller.debitAmountForeignVal().setError("Foreign amount is not allowed for base currency (YER) on debit side.");
        }
        if (BASE_CURRENCY.equals(creditCurrency) && teller.getCreditAmountForeign() != null) {
            teller.creditAmountForeignVal().setError("Foreign amount is not allowed for base currency (YER) on credit side.");
        }

        // ✅ Required amount checks
        if (BASE_CURRENCY.equals(debitCurrency) && teller.getDebitAmountLocal() == null) {
            teller.debitAmountLocalVal().setError("Local amount must not be null for base currency (YER).");
        }
        if (!BASE_CURRENCY.equals(debitCurrency) && teller.getDebitAmountForeign() == null) {
            teller.debitAmountForeignVal().setError("Foreign amount must not be null for non-base currency.");
        }

        // ✅ Debit denomination validation (internal only)
        if ("internal".equalsIgnoreCase(debitAccountType)) {
            double total = 0.0;
            for (DebitUnit unit : debitDenomList) {
                if (unit.getDebitUnit() != null) {
                    CcyDenom denom = new CcyDenom(databaseService.getRecordById("ccyDenom", unit.getDebitUnit()));
                    total += denom.getDenomValue() * unit.getDebitUnitValue();
                }
            }
            if (BASE_CURRENCY.equals(debitCurrency)) {
                if (teller.getDebitAmountLocal() != null && total != teller.getDebitAmountLocal()) {
                    teller.debitAmountLocalVal().setError("Denomination total does not match local amount.");
                }
            } else {
                if (teller.getDebitAmountForeign() != null && total != teller.getDebitAmountForeign()) {
                    teller.debitAmountForeignVal().setError("Denomination total does not match foreign amount.");
                }
            }
        }

        // ✅ Credit denomination validation (internal only)
        if ("internal".equalsIgnoreCase(creditAccountType)) {
            double total = 0.0;
            for (CreditUnit unit : creditDenomList) {
                if (unit.getCreditUnit() != null) {
                    CcyDenom denom = new CcyDenom(databaseService.getRecordById("ccyDenom", unit.getCreditUnit()));
                    total += denom.getDenomValue() * unit.getCreditUnitValue();
                }
            }
            if (BASE_CURRENCY.equals(creditCurrency)) {
                if (teller.getCreditAmountLocal() != null && total != teller.getCreditAmountLocal()) {
                    teller.creditAmountLocalVal().setError("Denomination total does not match local amount.");
                }
            } else {
                if (teller.getCreditAmountForeign() != null && total != teller.getCreditAmountForeign()) {
                    teller.creditAmountForeignVal().setError("Denomination total does not match foreign amount.");
                }
            }
        }

        // 🧾 Ledger validation
        List<AccountLedgerEntry> ledgerEntries = buildLedgerEntriesFromTeller(teller);
        ValidationResponse ledgerValidation = ledgerValidator.prcValidateLedgerEntries(ledgerEntries);

        // ✅ Merge all errors
        ValidationResponse finalResponse = teller.getValidationResponse();
        finalResponse.getFieldErrors().addAll(ledgerValidation.getFieldErrors());

        return finalResponse;
    }

    /**
     * Builds the ledger entries from Teller, including a PL entry when currencies differ.
     */
    private List<AccountLedgerEntry> buildLedgerEntriesFromTeller(Teller teller) {
        List<AccountLedgerEntry> ledgerEntries = new ArrayList<>();

        Double debitAmountLocal = teller.getDebitAmountLocal();
        Double creditAmountLocal = teller.getCreditAmountLocal();

        // Debit entry
        if (teller.getDebitAccount() != null) {
            AccountLedgerEntry debitEntry = new AccountLedgerEntry();
            debitEntry.setAccountNumber(teller.getDebitAccount());
            debitEntry.setCustomerNumber(teller.getDebitCustomer());
            debitEntry.setCurrencyCode(teller.getDebitCurrency());
            debitEntry.setAmountLcy(-1 * debitAmountLocal);
            debitEntry.setAmountFcy(BASE_CURRENCY.equals(teller.getDebitCurrency()) ? null : teller.getDebitAmountForeign());
            debitEntry.setEntryDrCr("DR");
            debitEntry.setBookingDate(teller.getDebitValueDate());
            debitEntry.setValueDate(teller.getDebitValueDate());
            debitEntry.setTransactionReference(teller.getTransactionCode());
            ledgerEntries.add(debitEntry);
        }

        // Credit entry
        if (teller.getCreditAccount() != null) {
            AccountLedgerEntry creditEntry = new AccountLedgerEntry();
            creditEntry.setAccountNumber(teller.getCreditAccount());
            creditEntry.setCustomerNumber(teller.getCreditCustomer());
            creditEntry.setCurrencyCode(teller.getCreditCurrency());
            creditEntry.setAmountLcy(creditAmountLocal);
            creditEntry.setAmountFcy(BASE_CURRENCY.equals(teller.getCreditCurrency()) ? null : teller.getCreditAmountForeign());
            creditEntry.setEntryDrCr("CR");
            creditEntry.setBookingDate(teller.getCreditValueDate());
            creditEntry.setValueDate(teller.getCreditValueDate());
            creditEntry.setTransactionReference(teller.getTransactionCode());
            ledgerEntries.add(creditEntry);
        }

        // Profit/Loss entry if currency mismatch
        if (!teller.getDebitCurrency().equals(teller.getCreditCurrency())) {
            double plAmount = debitAmountLocal - creditAmountLocal;
            if (plAmount != 0.0) {
                AccountLedgerEntry plEntry = new AccountLedgerEntry();
                plEntry.setAccountNumber(PL_ACCOUNT);
                plEntry.setCustomerNumber("PL");
                plEntry.setCurrencyCode(BASE_CURRENCY);
                plEntry.setAmountLcy(plAmount);
                plEntry.setAmountFcy(0.0);
                plEntry.setEntryDrCr(plAmount > 0 ? "CR" : "DR");
                plEntry.setBookingDate(teller.getCreditValueDate());
                plEntry.setValueDate(teller.getCreditValueDate());
                plEntry.setTransactionReference(teller.getTransactionCode());
                ledgerEntries.add(plEntry);
            }
        }

        return ledgerEntries;
    }
}
