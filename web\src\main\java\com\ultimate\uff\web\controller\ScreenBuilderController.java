package com.ultimate.uff.web.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.ultimate.uff.service.ScreenBuilderService;


@RestController
@RequestMapping("/api/screen-builder")
public class ScreenBuilderController {

    @Autowired
    private ScreenBuilderService screenBuilderService;

    @GetMapping("/{tableName:.+}/metadata")
    public ResponseEntity<Map<String, Object>> getTableMetadata(@PathVariable String tableName) {
    	Map<String, Object> metadata = screenBuilderService.getscreenMetadata(tableName);
        if (metadata != null) {
            return ResponseEntity.ok(metadata);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

   /* @GetMapping("/list")
    public ResponseEntity<List<String>> listScreens() {
        List<String> screens = screenBuilderService.listScreens();
        return ResponseEntity.ok(screens);
    }*/
}
