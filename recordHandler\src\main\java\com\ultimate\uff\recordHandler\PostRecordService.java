package com.ultimate.uff.recordHandler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ultimate.uff.queue.RecordProcessingService;

@Service
public class PostRecordService {

    @Autowired
    private RecordProcessingService recordProcessingService;

    public void handle(PostActionData action) {
        String table = action.getTable();
        String id = action.getId();
        int authNum = action.getAuthNum();

        switch (action.getFunction().toLowerCase()) {
            case "insert":
                recordProcessingService.processRecord(table, action.getBody(), authNum);
                break;
            case "validate":
                recordProcessingService.validateRecord(table, action.getBody());
                break;
            case "authorize":
                recordProcessingService.authorizeRecord(table, id, authNum);
                break;
            case "delete":
                recordProcessingService.deleteRecord(table, id, authNum);
                break;
            case "reject":
                recordProcessingService.rejectRecord(table, id, authNum);
                break;
            default:
                throw new IllegalArgumentException("Unsupported function: " + action.getFunction());
        }
    }
}
