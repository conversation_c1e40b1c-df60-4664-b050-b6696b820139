package com.ultimate.uff.screen;

import com.ultimate.uff.validation.*;
import java.util.*;

public class DefaultFields extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private final FieldValue<String> defaultField = new FieldValue<>(null);
    private final FieldValue<String> defaultValue = new FieldValue<>(null);

    public DefaultFields() {}

    public DefaultFields(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        defaultField.setValue((String) recordDetail.get("defaultField"));
        defaultValue.setValue((String) recordDetail.get("defaultValue"));
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("defaultField", defaultField.getValue());
        map.put("defaultValue", defaultValue.getValue());
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
case "defaultField", "defaultValue" -> true;
            default -> false;
        };
    }

    public FieldValue<String> getDefaultField() { return defaultField; }
    public FieldValue<String> getDefaultValue() { return defaultValue; }
}