package com.ultimate.uff.queryBuilderClasses;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryBuilder {

    private String id;
    private String tableName;
    private String fileName;
    private List<String> fieldsToAppear;
    private String inputter;
    private String authorizer;
    private String inDate;
    private String inTime;
    private int recordCount;

    // Group "selectionFields"
    private List<SelectionFields> selectionFieldsList;

    // Map to store dynamic/extra fields
    private Map<String, Object> extraFields = new HashMap<>();

    // Constructor that initializes QueryBuilder fields from the recordDetail map
    public QueryBuilder(Map<String, Object> recordDetail) {
        this.id = (String) recordDetail.get("ID");
        this.tableName = (String) recordDetail.get("table_name");
        this.fileName = (String) recordDetail.get("fileName");
        this.inputter = (String) recordDetail.get("Inputter");
        this.authorizer = (String) recordDetail.get("Authorizer");
        this.inDate = (String) recordDetail.get("IN_DATE");
        this.inTime = (String) recordDetail.get("IN_TIME");

        // Handle record_count safely
        Object recordCountValue = recordDetail.get("record_count");
        if (recordCountValue instanceof Integer) {
            this.recordCount = (Integer) recordCountValue;
        } else if (recordCountValue instanceof String) {
            try {
                this.recordCount = Integer.parseInt((String) recordCountValue);
            } catch (NumberFormatException e) {
                this.recordCount = 0; // Default value
            }
        } else {
            this.recordCount = 0; // Default value
        }

        // Handle fieldsToAppear as a multi-value list
        Object fieldsToAppearValue = recordDetail.get("fieldsToAppear");
        if (fieldsToAppearValue instanceof List) {
            this.fieldsToAppear = (List<String>) fieldsToAppearValue;
        } else {
            this.fieldsToAppear = new ArrayList<>();
        }

        // Initialize and populate selectionFieldsList from the map
        Object selectionFieldsValue = recordDetail.get("selectionFields");
        this.selectionFieldsList = new ArrayList<>();
        if (selectionFieldsValue instanceof List) {
            List<Map<String, Object>> selectionFieldsMaps = (List<Map<String, Object>>) selectionFieldsValue;
            for (Map<String, Object> fieldMap : selectionFieldsMaps) {
                this.selectionFieldsList.add(new SelectionFields(fieldMap));
            }
        }

        // Store any extra fields in the extraFields map
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    // Method to check if a field is explicitly handled
    private boolean isFieldHandled(String fieldName) {
        return fieldName.equals("ID") || fieldName.equals("table_name") || fieldName.equals("fileName") ||
               fieldName.equals("fieldsToAppear") || fieldName.equals("Inputter") || fieldName.equals("Authorizer") ||
               fieldName.equals("IN_DATE") || fieldName.equals("IN_TIME") || fieldName.equals("record_count") ||
               fieldName.equals("selectionFields");
    }

    // Method to update the recordDetail map and return the updated map
    public Map<String, Object> updateRecordDetail(Map<String, Object> recordDetail) {
        if (this.id != null) {
            recordDetail.put("ID", this.id);
        }
        if (this.tableName != null) {
            recordDetail.put("table_name", this.tableName);
        }
        if (this.fileName != null) {
            recordDetail.put("fileName", this.fileName);
        }
        if (this.fieldsToAppear != null && !this.fieldsToAppear.isEmpty()) {
            recordDetail.put("fieldsToAppear", this.fieldsToAppear);
        }
        if (this.inputter != null) {
            recordDetail.put("Inputter", this.inputter);
        }
        if (this.authorizer != null) {
            recordDetail.put("Authorizer", this.authorizer);
        }
        if (this.inDate != null) {
            recordDetail.put("IN_DATE", this.inDate);
        }
        if (this.inTime != null) {
            recordDetail.put("IN_TIME", this.inTime);
        }
        recordDetail.put("record_count", this.recordCount);

        // Convert selectionFieldsList to List<Map> and update the map if non-null
        if (this.selectionFieldsList != null && !this.selectionFieldsList.isEmpty()) {
            List<Map<String, Object>> selectionFieldsMaps = new ArrayList<>();
            for (SelectionFields selectionField : this.selectionFieldsList) {
                selectionFieldsMaps.add(selectionField.toMap());
            }
            recordDetail.put("selectionFields", selectionFieldsMaps);
        }

        // Add all extra fields back to the recordDetail map
        recordDetail.putAll(extraFields);

        return recordDetail;
    }

    // Getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public List<String> getFieldsToAppear() {
        return fieldsToAppear;
    }

    public void setFieldsToAppear(List<String> fieldsToAppear) {
        this.fieldsToAppear = fieldsToAppear;
    }

    public String getInputter() {
        return inputter;
    }

    public void setInputter(String inputter) {
        this.inputter = inputter;
    }

    public String getAuthorizer() {
        return authorizer;
    }

    public void setAuthorizer(String authorizer) {
        this.authorizer = authorizer;
    }

    public String getInDate() {
        return inDate;
    }

    public void setInDate(String inDate) {
        this.inDate = inDate;
    }

    public String getInTime() {
        return inTime;
    }

    public void setInTime(String inTime) {
        this.inTime = inTime;
    }

    public int getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
    }

    public List<SelectionFields> getSelectionFieldsList() {
        return selectionFieldsList;
    }

    public void setSelectionFieldsList(List<SelectionFields> selectionFieldsList) {
        this.selectionFieldsList = selectionFieldsList;
    }

    public Map<String, Object> getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(Map<String, Object> extraFields) {
        this.extraFields = extraFields;
    }
}
