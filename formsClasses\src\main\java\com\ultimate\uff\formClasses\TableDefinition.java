package com.ultimate.uff.formClasses;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TableDefinition {

    private String id;
    private String tableName;
    private String inputter;
    private String authorizer;
    private String inDate;
    private String inTime;
    private int recordCount;

    // Group "fieldName"
    private List<FieldName> fieldNameList;

    // Map to store dynamic/extra fields
    private Map<String, Object> extraFields = new HashMap<>();

    // Constructor that initializes TableDefinition fields from the recordDetail map
    public TableDefinition(Map<String, Object> recordDetail) {
        this.id = (String) recordDetail.get("ID");
        this.tableName = (String) recordDetail.get("table_name");
        this.inputter = (String) recordDetail.get("Inputter");
        this.authorizer = (String) recordDetail.get("Authorizer");
        this.inDate = (String) recordDetail.get("IN_DATE");
        this.inTime = (String) recordDetail.get("IN_TIME");

        // Handle record_count safely
        Object recordCountValue = recordDetail.get("recordCount");
        if (recordCountValue instanceof Integer) {
            this.recordCount = (Integer) recordCountValue;
        } else if (recordCountValue instanceof String) {
            try {
                this.recordCount = Integer.parseInt((String) recordCountValue);
            } catch (NumberFormatException e) {
                this.recordCount = 0; // Default value
            }
        } else {
            this.recordCount = 0; // Default value
        }

        // Initialize and populate fieldNameList from the map
        Object fieldNameValue = recordDetail.get("fieldName");
        this.fieldNameList = new ArrayList<>();
        if (fieldNameValue instanceof List) {
            List<Map<String, Object>> fieldNameMaps = (List<Map<String, Object>>) fieldNameValue;
            for (Map<String, Object> fieldMap : fieldNameMaps) {
                this.fieldNameList.add(new FieldName(fieldMap));
            }
        }

        // Store any extra fields in the extraFields map
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    // Method to check if a field is explicitly handled
    private boolean isFieldHandled(String fieldName) {
        return fieldName.equals("ID") || fieldName.equals("table_name") || fieldName.equals("Inputter") ||
               fieldName.equals("Authorizer") || fieldName.equals("IN_DATE") || fieldName.equals("IN_TIME") ||
               fieldName.equals("recordCount") || fieldName.equals("fieldName");
    }

    // Method to update the recordDetail map and return the updated map
    public Map<String, Object> updateRecordDetail(Map<String, Object> recordDetail) {
        if (this.id != null) {
            recordDetail.put("ID", this.id);
        }
        if (this.tableName != null) {
            recordDetail.put("table_name", this.tableName);
        }
        if (this.inputter != null) {
            recordDetail.put("Inputter", this.inputter);
        }
        if (this.authorizer != null) {
            recordDetail.put("Authorizer", this.authorizer);
        }
        if (this.inDate != null) {
            recordDetail.put("IN_DATE", this.inDate);
        }
        if (this.inTime != null) {
            recordDetail.put("IN_TIME", this.inTime);
        }
        recordDetail.put("record_count", this.recordCount);

        // Convert fieldNameList to List<Map> and update the map if non-null
        if (this.fieldNameList != null && !this.fieldNameList.isEmpty()) {
            List<Map<String, Object>> fieldNameMaps = new ArrayList<>();
            for (FieldName fieldName : this.fieldNameList) {
                fieldNameMaps.add(fieldName.toMap());
            }
            recordDetail.put("fieldName", fieldNameMaps);
        }

        // Add all extra fields back to the recordDetail map
        recordDetail.putAll(extraFields);

        return recordDetail;
    }

    // Getters and setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getInputter() {
        return inputter;
    }

    public void setInputter(String inputter) {
        this.inputter = inputter;
    }

    public String getAuthorizer() {
        return authorizer;
    }

    public void setAuthorizer(String authorizer) {
        this.authorizer = authorizer;
    }

    public String getInDate() {
        return inDate;
    }

    public void setInDate(String inDate) {
        this.inDate = inDate;
    }

    public String getInTime() {
        return inTime;
    }

    public void setInTime(String inTime) {
        this.inTime = inTime;
    }

    public int getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
    }

    public List<FieldName> getFieldNameList() {
        return fieldNameList;
    }

    public void setFieldNameList(List<FieldName> fieldNameList) {
        this.fieldNameList = fieldNameList;
    }

    public Map<String, Object> getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(Map<String, Object> extraFields) {
        this.extraFields = extraFields;
    }
}
