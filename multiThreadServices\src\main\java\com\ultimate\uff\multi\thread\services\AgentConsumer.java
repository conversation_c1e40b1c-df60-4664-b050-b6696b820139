package com.ultimate.uff.multi.thread.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.multi.thread.jobs.JobExecutable;
import com.ultimate.uff.multi.thread.jobs.JobResult;
import com.ultimate.uff.multi.thread.jobs.JobService;
import com.ultimate.uff.recordHandler.PostActionData;
import com.ultimate.uff.recordHandler.PostRecordService;
import com.ultimate.uff.validation.ReadOnlyDatabaseService;

import ch.qos.logback.classic.Logger;

import com.ultimate.uff.multi.thread.jobs.JobLogService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.KafkaTemplate;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;


@EnableKafka
public class AgentConsumer {
    private final String serviceId; // Unique identifier for this service instance

    // Service to fetch executable jobs grouped by task
    private final JobService jobService;

    // Kafka template to send job results asynchronously
    private final KafkaTemplate<String, String> kafkaTemplate;

    // Service for logging job and agent events
    private final JobLogService jobLogService;

    // ObjectMapper for converting JobResult objects to JSON strings
    private final ObjectMapper objectMapper;

    // Single-thread executor to run the main job consumption loop
    private final ExecutorService executor = Executors.newSingleThreadExecutor();

    // Flag to indicate whether the consumer is running or stopped (thread-safe)
    private final AtomicBoolean running = new AtomicBoolean(false);

    // Callback to send heartbeat signals during job processing (optional)
    private final Runnable heartbeatCallback;
    @Autowired
    private ReadOnlyDatabaseService readOnlyDatabaseService;
    @Autowired
    private  PostRecordService postRecordService;

    // Fixed thread pool executor to process multiple tasks in parallel
    private final ExecutorService parallelExecutor = Executors.newFixedThreadPool(10);
    private final Logger logger;
    // Constructor to initialize dependencies and configurations
    public AgentConsumer(String serviceId,
                    Runnable heartbeatCallback,
                    JobService jobService,
                    KafkaTemplate<String, String> kafkaTemplate,
                    JobLogService jobLogService,
                    ObjectMapper objectMapper,
                    LoggerClass loggerClass
                    ) {
    this.serviceId = serviceId;
    this.jobService = jobService;
    this.kafkaTemplate = kafkaTemplate;
    this.jobLogService = jobLogService;
    this.objectMapper = objectMapper;
    this.heartbeatCallback=heartbeatCallback;
    this.logger=loggerClass.getLogger();
}
/**
     * Starts the AgentConsumer if not already running.
     * Submits the main job consumption loop to the executor.
     */
public synchronized void start() {
    if (running.get()) {
        jobLogService.logInfo("Agent", serviceId, "already running","AgentConsumer already running for service: {}"+ serviceId);
        logger.warn("AgentConsumer already running for service: {}", serviceId);
        return ;
    }
    try {
        jobLogService.logInfo("Agent", serviceId, "Starting","Starting AgentConsumer for service: {}"+ serviceId);
        logger.info("Starting AgentConsumer for service: {}", serviceId);
        running.set(true);
        executor.submit(this::consumeJobs);
        logger.info("AgentConsumer started successfully for service: {}", serviceId);
        jobLogService.logInfo("Agent", serviceId, "successfully","AgentConsumer started successfully for service: {}"+ serviceId);
    } catch (Exception e) {
        jobLogService.logError("Agent", serviceId,"Failed","Failed to start AgentConsumer for service: {}"+ serviceId + e.getMessage());
        logger.error("❌ Failed to start AgentConsumer for service: {}", serviceId, e);
        throw new RuntimeException("AgentConsumer start failed", e);
    }
}
    /**
     * Stops the AgentConsumer gracefully.
     * Attempts to shutdown executors and waits for tasks to complete.
     */
    public void stop() {
        jobLogService.logInfo("INFO"," : ","Stopping AgentConsumer for service: {}" , serviceId);
        logger.info("Stopping AgentConsumer for service: {}", serviceId);
        running.set(false);
        try {
            logger.debug("Shutting down executors for service: {}", serviceId);
            executor.shutdownNow();
            parallelExecutor.shutdownNow();
            // Wait for parallel tasks to finish within timeout
            if (!parallelExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                jobLogService.logError("AGENT", serviceId, "SHUTDOWN_TIMEOUT","Parallel tasks did not terminate");
                logger.error("Parallel tasks did not terminate for service: {}", serviceId);
            }
             // Wait for main executor to finish within timeout
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                jobLogService.logError("AGENT", serviceId, "SHUTDOWN_TIMEOUT","Main executor did not terminate");
                logger.error("Main executor did not terminate for service: {}", serviceId);
            }
            logger.info("AgentConsumer stopped successfully for service: {}", serviceId);
            jobLogService.logInfo("Service",serviceId,"successfully","AgentConsumer stopped successfully for service: {}" + serviceId);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("Interrupted while stopping AgentConsumer: {}", e.getMessage(), e);
            jobLogService.logError("AGENT", serviceId,"Interrupted while stopping Agent for service",e.getMessage());
        } catch (Exception e) {
            jobLogService.logError("AGENT", serviceId, "SHUTDOWN_ERROR", "Unexpected shutdown error: " + e.getMessage());
            logger.error("Unexpected shutdown error: {}", e.getMessage(), e);
        }
    }


    //Main loop that continuously fetches and processes jobs while running.
    private void consumeJobs() {
        while (running.get() && !Thread.currentThread().isInterrupted()) {
            try {
                String serviceStatus = jobService.getServiceStatus(serviceId);
                if ("START".equalsIgnoreCase(serviceStatus)|| "AUTO".equalsIgnoreCase(serviceStatus)) {
                
                logger.debug("Fetching jobs...");
                 // Fetch executable jobs grouped by task ID
                Map<String, List<JobExecutable>> jobsByTask = jobService.getExecutableJobsGroupedByTask(serviceId);
                // If no jobs, sleep for a while before retrying
                if (jobsByTask.isEmpty()) {
                    handleNoJobs();
                    if ("START".equalsIgnoreCase(serviceStatus)) {
                        updateServiceStatusToStop();
                        stop();
                    }
                    continue;
                }
                 // Submit each task's jobs for parallel processing
                jobsByTask.forEach((taskId, jobs) -> 
                    parallelExecutor.submit(() -> processTask(taskId, jobs)));
                }
                logger.debug( serviceId,"Skipping job execution. Service status: {}", serviceStatus);
                handleNoJobs();
                //continue;
                
            } catch (Exception e) {
                logger.error("AGENT", serviceId,"Unhandled error in consumeJobs: {}", e.getMessage(), e);
                jobLogService.logError("AGENT", serviceId, "AGENT Unhandled error: " , e.getMessage());
                sleepWithInterruptCheck(5000);
            }
        }
}
    
    /*private void consumeJobs() {
        while (running.get() && !Thread.currentThread().isInterrupted()) {
            try {
                String serviceStatus = jobService.getServiceStatus(serviceId);
                if (!"START".equalsIgnoreCase(serviceStatus)){//&& !"AUTO".equalsIgnoreCase(serviceStatus)) {
                    logger.debug( serviceId,"Skipping job execution. Service status: {}", serviceStatus);
                    handleNoJobs();
                    continue;
                }
                logger.debug("Fetching jobs...");
                 // Fetch executable jobs grouped by task ID
                Map<String, List<JobExecutable>> jobsByTask = jobService.getExecutableJobsGroupedByTask(serviceId);
                // If no jobs, sleep for a while before retrying
                if (jobsByTask.isEmpty()) {
                    handleNoJobs();
                    if ("START".equalsIgnoreCase(serviceStatus)) {
                        updateServiceStatusToStop();
                        stop();
                    }
                    continue;
                }
                 // Submit each task's jobs for parallel processing
                jobsByTask.forEach((taskId, jobs) -> 
                    parallelExecutor.submit(() -> processTask(taskId, jobs))
                );
                
            } catch (Exception e) {
                logger.error("AGENT", serviceId,"Unhandled error in consumeJobs: {}", e.getMessage(), e);
                jobLogService.logError("AGENT", serviceId, "AGENT Unhandled error: " , e.getMessage());
                sleepWithInterruptCheck(5000);
            }
        }
    }*/
    /**
     * Processes all jobs belonging to a single task.
     * Calls heartbeat callback after each job if provided.
     */
    private void processTask(String taskId, List<JobExecutable> jobs) {
        try {
            logger.debug("Processing task: {}", taskId);
            jobLogService.logInfo("DEBUG", serviceId, "TASK_START", "Processing task: " + taskId);
            for (JobExecutable job : jobs) {
                processJob(job);
                // Run heartbeat callback to signal liveness if available
                if (heartbeatCallback != null) {
                    heartbeatCallback.run();
                }
            }
            logger.debug("Completed task: {}", taskId);
            jobLogService.logInfo("DEBUG", serviceId, "TASK_COMPLETE", "Completed task: " + taskId);

        } catch (Exception e) {
            logger.error("Task failed: {}", e.getMessage(), e);
            jobLogService.logError("TASK", taskId, "TASK_FAILED Execution" ,e.getMessage());
        }
    }
    
    // Executes a single job and sends the result to Kafka.
    private void processJob(JobExecutable job) {
        try {
            JobResult result = job.execute();
            // Send the job result asynchronously to Kafka topic
            sendKafkaResult(result);
            logger.info("Job result: [{}] - {}", result.getRefId(), result.getMessage());
            jobLogService.logInfo(result.getType(), result.getRefId(), result.getAction(), result.getMessage());
                
        } catch (Exception e) {
            jobLogService.logError("JOB", job.getJobId(), "FAILED: "+" Execution error for job {}: {}"+ job.getJobId(), e.getMessage());
                logger.error("Execution error for job {}: {}", job.getJobId(), e.getMessage(), e);
        }
    }
    /**
     * Serializes the job result to JSON and sends it to Kafka.
     * Waits up to 5 seconds for send confirmation.
     */
    private void sendKafkaResult(JobResult result) {
        try {
            String jsonResult = objectMapper.writeValueAsString(result);
            kafkaTemplate.send("job-results", jsonResult)
                .get(5, TimeUnit.SECONDS);
            
        } catch (TimeoutException te) {
            logger.error("Kafka send timeout for result: {}", result.getRefId(), te);
        } catch (Exception e) {
            logger.error("Kafka send error for result: {}", result.getRefId(), e);
        }
    }
    /**
     * Handles the case when no jobs are available by sleeping for 5 seconds.
     * If interrupted during sleep, stops the consumer.
     */
    private void handleNoJobs() {
        try {
            logger.debug("No jobs found, sleeping...");
           // jobLogService.logDebug("AGENT", serviceId, "NO_JOBS", "Sleeping...");
            Thread.sleep(5000);
        } catch (InterruptedException ie) {
            logger.warn("Interrupted during sleep, stopping consumer.",ie);
           // jobLogService.logError("AGENT", serviceId, "AGENT_INTERRUPTED","Interrupted during sleep", ie);
            Thread.currentThread().interrupt();
            running.set(false);
        }
    }
    
    //Sleeps for the given duration, handling interruption by stopping the consumer.
    private void sleepWithInterruptCheck(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException ie) {
            logger.warn("Sleep interrupted, stopping consumer.");
            jobLogService.logInfo("INFO", serviceId, "INTERRUPTED", "Sleep interrupted, stopping consumer.");
            Thread.currentThread().interrupt();
            running.set(false);
        }
    }

    private void updateServiceStatusToStop() {
        try {
            Map<String,Object> serviceJson = readOnlyDatabaseService.getRecordById("service", serviceId);
            if (!serviceJson.isEmpty()) {
                com.ultimate.uff.multi.thread.generatedClass.Service service = new com.ultimate.uff.multi.thread.generatedClass.Service(serviceJson);
                service.setStatus("STOP");

                PostActionData actionData = new PostActionData();
                actionData.setTable("service");
                actionData.setFunction("insert");
                actionData.setId(service.getID());
                actionData.setBody(service.toMap());
                actionData.setAuthNum(0);

                postRecordService.handle(actionData);
                jobLogService.logInfo("SERVICE", serviceId, "AUTO_STOP", "Service status updated to STOP.");
            }
        } catch (Exception e) {
            jobLogService.logError("SERVICE", serviceId, "STATUS_UPDATE_FAILED", "Failed to update status to STOP."+e);
        }
    }
    
    // Returns true if the consumer is currently running.
    public boolean isRunning() {
        return running.get();
    }
}
