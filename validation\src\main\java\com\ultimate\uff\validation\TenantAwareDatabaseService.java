package com.ultimate.uff.validation;

import com.ultimate.uff.tenant.RecordContext;
import com.ultimate.uff.uql.service.DatabaseService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class TenantAwareDatabaseService {

    private final DatabaseService databaseService;

    @Autowired
    public TenantAwareDatabaseService(DatabaseService databaseService) {
        this.databaseService = databaseService;
    }

    private String resolveTableName(String companyCode, String tableName) {
        if (RecordContext.isTenantBased()) {
            return companyCode + "_" + tableName;
        }
        return tableName;
    }

    public void insertRecord(String companyCode, String tableName, String id, String jsonData) {
        String resolvedTable = resolveTableName(companyCode, tableName);
        databaseService.insertRecord(resolvedTable, id, jsonData);
    }

    public Map<String, Object> getRecordById(String companyCode, String tableName, String id) {
        String resolvedTable = resolveTableName(companyCode, tableName);
        return databaseService.getRecordById(resolvedTable, id);
    }

    public void deleteRecordById(String companyCode, String tableName, String id) {
        String resolvedTable = resolveTableName(companyCode, tableName);
        databaseService.deleteRecordById(resolvedTable, id);
    }

    // Add more passthrough methods if needed...
}
