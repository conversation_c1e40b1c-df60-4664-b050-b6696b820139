package com.ultimate.uff.uql.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ultimate.uff.uql.util.QueryBuilder;

import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

@Service
public class DatabaseService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);

    private final JdbcTemplate jdbcTemplate;
    private final QueryBuilder queryBuilder;
    private final ObjectMapper objectMapper;

    /**
     * Constructs a new DatabaseService instance.
     *
     * @param queryBuilder the SQL query builder used to construct SQL queries
     * @param jdbcTemplate    the JdbcTemplate used for executing SQL queries
     */
    @Autowired
    public DatabaseService(QueryBuilder queryBuilder, JdbcTemplate jdbcTemplate, ObjectMapper objectMapper) {
        this.queryBuilder = queryBuilder;
        this.jdbcTemplate = jdbcTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Creates a table in the database.
     *
     * @param tableName the name of the table to be created with
     *                  applyUFFTableNameRules function
     *                  Ex. Passed table name is "TEST" created table in the
     *                  database is "T_TEST"
     */
    @Transactional
    public void createTable(String tableName) {
        String uffTableName = applyUFFTableNameRules(tableName);
        executeAndLog(() -> {
            String query = queryBuilder.createTable(uffTableName);
            logger.debug("Executing SQL query: {}", query);
            String[] queries = query.split("; ");
            for (String q : queries) {
                jdbcTemplate.execute(q);
            }
        }, "create table", uffTableName);
    }

    /**
     * Creates a view in the database based on the specified table's metadata.
     *
     * @param tableName the name of the table whose metadata will be used to create
     *                  the view
     */
    @Transactional
    public void createView(String tableName) {
        executeAndLog(() -> {
            // Retrieve metadata JSON as a Map from F_formDefinition
            Map<String, Object> metadataResult = getRecordById("formDefinition", tableName);
            logger.debug("Retrieved metadata for table {}: {}", tableName, metadataResult);

            // Convert metadataResult map to JSON string
            String metadataJson = objectMapper.writeValueAsString(metadataResult);

            // Check if metadataJson is null or empty
            if (metadataJson == null || metadataJson.isEmpty()) {
                throw new RuntimeException("No metadata found for table: " + tableName);
            }

            // Generate the SQL query for creating the view based on metadata
            String query = queryBuilder.createView(applyUFFTableNameRules(tableName), metadataJson);
            logger.debug("Executing SQL query for creating view: {}", query);

            // Execute the SQL query to create the view
            jdbcTemplate.execute(query);
        }, "create view", tableName);
    }

    /**
     * Creates a view in the database based on the specified table's metadata.
     *
     * @param tableName the name of the table whose metadata will be used to create
     *                  the view
     */
    /*@Transactional
    public void createViewCross(String tableName) {
        executeAndLog(() -> {
            // Retrieve metadata JSON as a Map from F_formDefinition
            Map<String, Object> metadataResult = getRecordById("formDefinition", tableName);
            logger.debug("Retrieved metadata for table {}: {}", tableName, metadataResult);

            // Convert metadataResult map to JSON string
            String metadataJson = objectMapper.writeValueAsString(metadataResult);

            // Check if metadataJson is null or empty
            if (metadataJson == null || metadataJson.isEmpty()) {
                throw new RuntimeException("No metadata found for table: " + tableName);
            }

            // Generate the SQL query for creating the cross view based on metadata
            String query = queryBuilder.createViewCross(applyUFFTableNameRules(tableName), metadataJson);
            logger.debug("Executing SQL query for creating view: {}", query);

            // Execute the SQL query to create the view
            jdbcTemplate.execute(query);
        }, "create view", tableName);
    }*/

    /**
     * Checks if a table exists in the database.
     *
     * @param tableName the name of the table to check
     * @return true if the table exists, false otherwise
     */
    public boolean checkTableExistence(String tableName) {
        return executeAndLog(() -> {
            String uffTableName = applyUFFTableNameRules(tableName);
            String query = queryBuilder.checkTableExistence(uffTableName);
            logger.debug("Executing SQL query: {}", query);
            Integer count = jdbcTemplate.queryForObject(query, Integer.class);
            return count != null && count > 0;
        }, "check table existence", tableName);
    }

    /**
     * Inserts a record into the specified table.
     *
     * @param tableName    the name of the table where the record will be inserted
     * @param id           the ID of the record
     * @param recordDetail the details of the record in JSON format
     */
    @Transactional
    public void insertRecord(String tableName, String id, String recordDetail) {
        executeAndLog(() -> {
            String uffTableName = applyUFFTableNameRules(tableName);
            String query = queryBuilder.insertRecord(uffTableName, id, recordDetail);
            logger.debug("Executing SQL query: {}", query);
            jdbcTemplate.execute(query); // Execute without parameters since they are inlined
        }, "insert record", tableName);
    }

    /**
     * Deletes a record from the specified table.
     *
     * @param tableName the name of the table from which to delete the record
     * @param id        the ID of the record to delete
     */
    @Transactional
    public void deleteRecordById(String tableName, String id) {
        executeAndLog(() -> {
            String uffTableName = applyUFFTableNameRules(tableName);
            String query = queryBuilder.deleteRecordById(uffTableName, id);
            logger.debug("Executing SQL query: {}", query);
            jdbcTemplate.update(query,id); // Use jdbcTemplate.update for delete operations
        }, "delete record", tableName);
    }

    /**
     * Retrieves a JSON column value by its ID from the specified table and returns
     * it as a Map.
     *
     * @param tableName the name of the table from which to retrieve the record
     * @param id        the ID of the record to retrieve
     * @return a Map containing the JSON key-value pairs if found, or null if not
     *         found
     */
    public Map<String, Object> getRecordById(String tableName, String id) {
        return executeAndLog((DatabaseService.SupplierWithException<Map<String, Object>>) () -> {
            String uffTableName = applyUFFTableNameRules(tableName);
            String query = queryBuilder.getRecordById(uffTableName, id);
            logger.debug("Executing SQL query: {}", query);

            try {
                Map<String, Object> result = jdbcTemplate.queryForMap(query);
                if (result != null && !result.isEmpty()) {
                    String jsonRowDetail = (String) result.get("JSON_ROW_DETAIL");
                    if (jsonRowDetail != null) {
                        ObjectMapper objectMapper = new ObjectMapper();
                        return objectMapper.readValue(jsonRowDetail, Map.class);
                    }
                }
                return null;  // Return null if no data or if JSON_ROW_DETAIL is missing
            } catch (EmptyResultDataAccessException e) {
                logger.warn("No record found for ID: {}", id);
                return null;
            }
        }, "get record by ID", tableName);
    }


    /**
     * Checks if a record exists in the specified table.
     *
     * @param tableName the name of the table where the record might exist
     * @param id        the ID of the record to check
     * @return true if the record exists, false otherwise
     */
    public boolean checkRecordExistence(String tableName, String id) {
        return executeAndLog(() -> {
            String query = queryBuilder.getRecordById(applyUFFTableNameRules(tableName), id);
            logger.debug("Executing SQL query: {}", query);
            List<Map<String, Object>> records = jdbcTemplate.queryForList(query);
            return !records.isEmpty();
        }, "check record existence", tableName);
    }

    /**
     * Retrieves records using JSON-style criteria logic.
     *
     * @param tableName    the name of the table from which to retrieve records
     * @param criteriaJson a JSON-style criteria map (e.g., {"AND": [{"field1":
     *                     {"EQ": "value1"}}, ...]})
     * @return an ArrayNode containing matching records
     */
    public ArrayNode getRecordsByCriteria(String tableName, Map<String, Object> criteriaJson) {
        return executeAndLog(() -> {
            String uffTableName = applyUFFTableNameRules(tableName);
            String query = queryBuilder.getRecordsByCriteria(uffTableName, criteriaJson);

            logger.debug("Executing SQL query (JSON-style criteria): {}", query);

            List<Map<String, Object>> records = jdbcTemplate.queryForList(query);

            ArrayNode jsonArray = objectMapper.createArrayNode();
            for (Map<String, Object> record : records) {
                jsonArray.add(objectMapper.valueToTree(record));
            }

            return jsonArray;
        }, "get records by JSON-style criteria", tableName);
    }

    /**
     * Drops the specified table from the database.
     *
     * @param tableName the name of the table to drop
     */
    public void dropTable(String tableName) {
        String uffTableName = applyUFFTableNameRules(tableName);
        executeAndLog(() -> {
            String query = queryBuilder.dropTable(uffTableName);
            logger.debug("Executing SQL query: {}", query);
            jdbcTemplate.execute(query);
        }, "drop table", uffTableName);
    }

    /**
     * Applies specific naming rules to the given table name.
     *
     * @param tableName the original table name
     * @return the table name after applying UFF naming rules
     */
    private String applyUFFTableNameRules(String tableName) {
        return "F_" + tableName.toUpperCase();
    }

    /**
     * Executes a function and logs the operation.
     *
     * @param supplier  the function to execute
     * @param action    the action being performed (e.g., "create table", "insert
     *                  record")
     * @param tableName the name of the table on which the action is performed
     * @param <T>       the return type of the function
     * @return the result of the function
     * @throws RuntimeException if the function fails to execute
     */
    private <T> T executeAndLog(SupplierWithException<T> supplier, String action, String tableName) {
        try {
            logger.info("Attempting to {} for table: {}", action, tableName);
            T result = supplier.get();
            logger.info("{} successful for table: {}", action, tableName);
            return result;
        } catch (Exception e) {
            logger.error("Failed to {} for table: {}", action, tableName, e);
            throw new RuntimeException(e); // Convert to runtime exception
        }
    }

    /**
     * Executes a runnable and logs the operation.
     *
     * @param runnable  the runnable to execute
     * @param action    the action being performed (e.g., "create table", "insert
     *                  record")
     * @param tableName the name of the table on which the action is performed
     * @throws RuntimeException if the runnable fails to execute
     */
    private void executeAndLog(RunnableWithException runnable, String action, String tableName) {
        try {
            logger.info("Attempting to {} for table: {}", action, tableName);
            runnable.run();
            logger.info("{} successful for table: {}", action, tableName);
        } catch (Exception e) {
            logger.error("Failed to {} for table: {}", action, tableName, e);
            throw new RuntimeException(e); // Convert to runtime exception
        }
    }


    @FunctionalInterface
    private interface SupplierWithException<T> {
        T get() throws Exception;
    }

    @FunctionalInterface
    private interface RunnableWithException {
        void run() throws Exception;
    }
}
