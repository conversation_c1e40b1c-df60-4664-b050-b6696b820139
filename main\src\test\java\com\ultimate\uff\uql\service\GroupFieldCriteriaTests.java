package com.ultimate.uff.uql.service;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = com.ultimate.uff.main.UffMainApplication.class)
public class GroupFieldCriteriaTests extends BaseCriteriaTest {
    @Test
    public void test_CT_NestedArray() {
        runJsonTest("{ \"nestedArray[].nestedKey\": { \"CT\": \"nestedValue2\" } }", "nestedValue2");
    }

    @Test
    public void test_LK_NestedField_MultiArray() {
        runJsonTest("{ \"nestedArray[].nestedKey\": { \"LK\": \"nestedValue2\" } }", "nestedValue2");
    }

    @Test
    public void test_NE_MissingNestedField() {
        runJsonTest("{ \"nestedArray[].nonexistentKey\": { \"NE\": \"ghost\" } }", "singleValue");
    }

    @Test
    public void test_EQ_NestedGroup_ExactMatch() {
        runJsonTest("{ \"nestedArray[].nestedKey\": { \"EQ\": \"nestedValue2\" } }", "nestedValue2");
    }

    @Test
    public void test_NE_NestedGroup_ExactMismatch() {
        runJsonNegativeTest("{ \"nestedArray[].nestedKey\": { \"NE\": \"nestedValue2\" } }", "nestedKey",
                "nestedValue2");
    }

    @Test
    public void test_GT_NestedNumeric() {
        runJsonTest("{ \"nestedGroupArray[].amount:INT\": { \"GT\": \"50\" } }", "ROW_ID", List.of("T6", "T7"));
    }

    @Test
    public void test_RG_NestedAmount() {
        runJsonTest("{ \"nestedGroupArray[].amount:INT\": { \"RG\": [\"10\", \"60\"] } }", "ROW_ID",
                List.of("T1", "T2", "T6"));
    }

    @Test
    public void test_NR_NestedAmount() {
        runJsonNegativeTest("{ \"nestedGroupArray[].amount:INT\": { \"NR\": [\"10\", \"60\"] } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_NestedDateField() {
        runJsonTest("{ \"nestedGroupArray[].timestamp:DATE\": { \"EQ\": \"2024-01-01\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_NestedDate_Fallback() {
        runJsonTest("{ \"nestedGroupArray[].timestamp:DATE\": { \"EQ\": \"2024-02-01\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_Null_NestedKey_Missing() {
        runJsonTest("{ \"nestedArray[].missingKey\": { \"EQ\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_NE_Null_NestedKey() {
        runJsonNegativeTest("{ \"nestedArray[].missingKey\": { \"NE\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_LT_NestedAmount() {
        runJsonTest("{ \"nestedGroupArray[].amount:INT\": { \"LT\": \"30\" } }", "ROW_ID", List.of("T1", "T5"));
    }

    @Test
    public void test_GE_NestedAmount() {
        runJsonTest("{ \"nestedGroupArray[].amount:INT\": { \"GE\": \"60\" } }", "ROW_ID", List.of("T6", "T7"));
    }

    @Test
    public void test_LE_NestedAmount() {
        runJsonTest("{ \"nestedGroupArray[].amount:INT\": { \"LE\": \"10\" } }", "ROW_ID", "T5");
    }

    @Test
    public void test_RG_NestedTimestamp() {
        runJsonTest("{ \"nestedGroupArray[].timestamp:DATE\": { \"RG\": [\"2024-01-01\", \"2024-03-01\"] } }",
                "ROW_ID", List.of("T1", "T2"));
    }

    @Test
    public void test_NR_NestedTimestamp() {
        runJsonNegativeTest("{ \"nestedGroupArray[].timestamp:DATE\": { \"NR\": [\"2024-01-01\", \"2024-03-01\"] } }",
                "ROW_ID", "T1");
    }

    @Test
    public void test_DNBW_NestedKey() {
        runJsonTest("{ \"nestedArray[].nestedKey\": { \"DNBW\": \"nested\" } }", "ROW_ID", "T3");
    }

    @Test
    public void test_DNEW_NestedKey() {
        runJsonTest("{ \"nestedArray[].nestedKey\": { \"DNEW\": \"2\" } }", "ROW_ID", "T3");
    }

    @Test
    public void test_EQ_DeepNestedGroupField() {
        runJsonTest(
                "{ \"nestedArray[].nestedKey[].subkey[].subofsub\": { \"EQ\": \"DeepValue\" } }",
                "ROW_ID",
                "T8");
    }

    @Test
    public void test_EQ_DeepNestedArrayField() {
        runJsonTest("{ \"nestedArray[].nestedKey[].subkey[].subofsub[]\": { \"EQ\": \"two\" } }", "ROW_ID", "T9");
    }

    @Test
    public void test_CT_DeepNestedField() {
        runJsonTest("{ \"nestedArray[].nestedKey[].subkey[].subofsub\": { \"CT\": \"Deep\" } }", "ROW_ID", "T8");
    }

    @Test
    public void test_DNBW_DeepNestedField() {
        runJsonTest(
            "{ \"nestedArray[].nestedKey[].subkey[].subofsub\": { \"DNBW\": \"D\" } }",
            "ROW_ID",
            List.of("T1", "T2", "T3", "T4", "T5", "T6", "T7", "T9") // ✅ All except T8
        );
    }      

    @Test
    public void test_NR_NestedGroupDate() {
        runJsonNegativeTest("{ \"nestedGroupArray[].timestamp:DATE\": { \"NR\": [\"2024-01-01\", \"2024-12-31\"] } }",
                "ROW_ID", "T1");
    }

    @Test
    public void test_LK_NestedGroupField() {
        runJsonTest("{ \"nestedArray[].nestedKey\": { \"LK\": \"%nestedValue2\" } }", "nestedValue2");
    }

    @Test
    public void test_LI_NestedGroupField_CaseInsensitive() {
        runJsonTest("{ \"nestedArray[].nestedKey\": { \"LI\": \"%NESTEDVALUE2\" } }", "nestedValue2");
    }

    @Test
    public void test_LK_NestedGroupField_NoMatch() {
        runJsonNegativeTest("{ \"nestedArray[].nestedKey\": { \"LK\": \"%notthere%\" } }", "nestedKey", "notthere");
    }

    @Test
    public void test_LI_DeepNestedField() {
        runJsonTest("{ \"nestedArray[].nestedKey[].subkey[].subofsub\": { \"LI\": \"%deepvalue%\" } }", "ROW_ID", "T8");
    }


}
