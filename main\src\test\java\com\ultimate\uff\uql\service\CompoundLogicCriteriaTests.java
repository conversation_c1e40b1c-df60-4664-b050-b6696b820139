package com.ultimate.uff.uql.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

@SpringBootTest(classes = com.ultimate.uff.main.UffMainApplication.class)
public class CompoundLogicCriteriaTests extends BaseCriteriaTest {

    @Test
    public void test_AND_Compound() {
        runJsonTest("{ \"AND\": ["
                + " { \"singleField\": { \"EQ\": \"singleValue\" } },"
                + " { \"multiField[]\": { \"CT\": \"value2\" } } ] }", "value2");
    }

    @Test
    public void test_OR_Compound() {
        runJsonTest("{ \"OR\": ["
                + " { \"singleField\": { \"EQ\": \"noMatch\" } },"
                + " { \"singleField\": { \"EQ\": \"anotherValue\" } } ] }", "anotherValue");
    }

    @Test
    public void test_OR_CombinationAcrossFields() {
        runJsonTest("{ \"OR\": ["
                + " { \"singleField\": { \"EQ\": \"noMatchValue\" } },"
                + " { \"nestedArray[].nestedKey\": { \"EQ\": \"nestedValue2\" } } ] }", "nestedValue2");
    }

    @Test
    public void test_Complex_Nested_AND_OR_Sort() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"AND\": [ " +
                            "{ \"OR\": [ " +
                            "{ \"singleField\": { \"CT\": \"XValue\" } }, " +
                            "{ \"nestedArray[].nestedKey\": { \"CT\": \"deepValue3\" } }" +
                            " ] }, " +
                            "{ \"multiField[]\": { \"DNBW\": \"z\" } }" +
                            " ], " +
                            "\"_sort\": [\"numericField DESC\"] }",
                    new TypeReference<Map<String, Object>>() {
                    });

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);

            assertEquals(2, result.size(), "Expected 2 complex-matching records");

            List<String> actualOrder = new ArrayList<>();
            for (JsonNode node : result) {
                actualOrder.add(node.get("ROW_ID").asText());
            }

            List<String> expectedOrder = List.of("T6", "T7");
            assertEquals(expectedOrder, actualOrder, "Expected sort order by numericField DESC");

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Complex_Nested_AND_OR_Sort", e);
        }
    }

    @Test
    public void test_OR_WithNestedAND() {
        runJsonTest("""
                {
                  "OR": [
                    { "singleField": { "EQ": "ghost" } },
                    { "AND": [
                      { "multiField[]": { "CT": "value2" } },
                      { "multiIntField[]:INT": { "GE": "5" } }
                    ]}
                  ]
                }
                """, "value2");
    }

    @Test
    public void test_AND_OR_NE_Combination() {
        runJsonTest("""
                {
                  "AND": [
                    {
                      "OR": [
                        { "singleField": { "EQ": "XValue" } },
                        { "multiField[]": { "CT": "value3" } }
                      ]
                    },
                    { "numericField:INT": { "NE": "99" } }
                  ]
                }
                """, "ROW_ID", "T1");
    }

    @Test
    public void test_InvalidOperator_ShouldFail() {
        assertThrows(RuntimeException.class, () -> {
            runJsonTest("""
                    { "singleField": { "ZZZ": "value" } }
                    """, "singleField", "value");
        });
    }

    @Test
    public void test_Empty_OR_Group_ShouldReturnNothing() {
        runJsonNegativeTest("{ \"OR\": [] }", "ROW_ID", "T1");
    }

    @Test
    public void test_Compound_MixedStructures() {
        runJsonTest("""
                {
                  "AND": [
                    { "multiField[]": { "CT": "value" } },
                    { "nestedGroupArray[].timestamp:DATE": { "RG": ["2024-01-01", "2024-12-31"] } },
                    { "numericField:INT": { "GE": "10" } }
                  ]
                }
                """, "ROW_ID", "T1");
    }

    @Test
    public void test_DeeplyNested_AND_OR_GroupFields() {
        runJsonTest("""
                {
                  "AND": [
                    {
                      "OR": [
                        { "singleField": { "EQ": "ghost" } },
                        {
                          "AND": [
                            { "multiField[]": { "EQ": "value2" } },
                            { "numericField:INT": { "GE": "10" } }
                          ]
                        }
                      ]
                    },
                    { "nestedGroupArray[].amount:INT": { "LE": "60" } }
                  ]
                }
                """, "ROW_ID", "T1");
    }

    @Test
    public void test_Empty_AND_Group_ShouldReturnAll() {
        try {
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, Map.of("AND", List.of()));
            assertEquals(9, result.size(), "Expected all records to match when AND is empty");
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Empty_AND_Group_ShouldReturnAll", e);
        }
    }

}
