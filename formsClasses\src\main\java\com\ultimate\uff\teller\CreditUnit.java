package com.ultimate.uff.teller;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class CreditUnit extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String creditUnit;
    private final FieldValue<String> creditUnitVal = new FieldValue<>(null);
    private Double creditUnitValue;
    private final FieldValue<Double> creditUnitValueVal = new FieldValue<>(null);

    public CreditUnit() {}

    public CreditUnit(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object creditUnitObj = recordDetail.get("creditUnit");
        if (creditUnitObj != null) {
            this.setCreditUnit(creditUnitObj.toString());
        }
        Object creditUnitValueObj = recordDetail.get("creditUnitValue");
        if (creditUnitValueObj instanceof Number) {
            this.setCreditUnitValue(((Number) creditUnitValueObj).doubleValue());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (creditUnit != null) map.put("creditUnit", creditUnit);
        if (creditUnitValue != null) map.put("creditUnitValue", creditUnitValue);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "creditUnit", "creditUnitValue" -> true;
            default -> false;
        };
    }

    public void setCreditUnit(String creditUnit) {
        this.creditUnit = creditUnit;
        this.creditUnitVal.setValue(creditUnit);
    }
    public String getCreditUnit() { return this.creditUnit; }
    public FieldValue<String> creditUnitVal() { return creditUnitVal; }
    public void setCreditUnitValue(Double creditUnitValue) {
        this.creditUnitValue = creditUnitValue;
        this.creditUnitValueVal.setValue(creditUnitValue);
    }
    public Double getCreditUnitValue() { return this.creditUnitValue; }
    public FieldValue<Double> creditUnitValueVal() { return creditUnitValueVal; }
}
