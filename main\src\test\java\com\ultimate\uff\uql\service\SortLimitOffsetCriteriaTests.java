package com.ultimate.uff.uql.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

@SpringBootTest(classes = com.ultimate.uff.main.UffMainApplication.class)
public class SortLimitOffsetCriteriaTests extends BaseCriteriaTest {
    @Test
    public void test_SortingByTwoFields() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"singleField\": { \"CT\": \"Value\" }, \"_sort\": [\"numericField DESC\", \"singleField ASC\"] }",
                    new TypeReference<Map<String, Object>>() {
                    });

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);

            assertEquals(6, result.size(), "Expected 6 matching records");

            List<String> actualOrder = new ArrayList<>();
            for (JsonNode node : result) {
                actualOrder.add(node.get("ROW_ID").asText());
            }

            // 👇 Expected order based on CT 'value' and sort: numericField DESC
            List<String> expectedOrder = List.of("T4", "T6", "T1", "T2", "T7", "T3");

            assertEquals(expectedOrder, actualOrder, "Row order did not match expected sort");

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_SortingByTwoFields", e);
        }
    }

    @Test
    public void test_Sort_ASC_SingleField() {
        runJsonTest("{ \"_sort\": [\"singleField ASC\"] }", "singleValue");
    }

    @Test
    public void test_Sort_DESC_NumericField() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"singleField\": { \"CT\": \"Value\" }, \"_sort\": [\"numericField DESC\"] }",
                    new TypeReference<Map<String, Object>>() {
                    });

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);

            assertEquals(6, result.size(), "Expected 6 matching records");

            List<String> actualOrder = new ArrayList<>();
            for (JsonNode node : result) {
                actualOrder.add(node.get("ROW_ID").asText());
            }

            List<String> expectedOrder = List.of("T4", "T6", "T1", "T2", "T7", "T3");

            assertEquals(expectedOrder, actualOrder,
                    "Row order did not match expected descending sort by numericField");

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Sort_DESC_NumericField", e);
        }
    }

    @Test
    public void test_Limit_1() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"_limit\": 1 }", new TypeReference<Map<String, Object>>() {
                    });
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);
            assertEquals(1, result.size(), "Expected exactly 1 record due to _limit:1");
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Limit_1", e);
        }
    }

    @Test
    public void test_Offset_1() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"_sort\": [\"numericField ASC\"], \"_offset\": 1 }", new TypeReference<Map<String, Object>>() {
                    });
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);
            assertFalse(result.isEmpty(), "Expected non-empty result from offset");
            JsonNode first = result.get(0).get("JSON_ROW_DETAIL");
            assertNotNull(first);
            assertTrue(first.toString().contains("numericField"));
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Offset_1", e);
        }
    }

    @Test
    public void test_Limit_Offset_Together() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"_sort\": [\"numericField ASC\"], \"_limit\": 1, \"_offset\": 1 }",
                    new TypeReference<Map<String, Object>>() {
                    });
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);
            assertEquals(1, result.size(), "Expected exactly 1 record from _limit + _offset combo");
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Limit_Offset_Together", e);
        }
    }

    @Test
    public void test_Limit_Offset_Page1() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"_sort\": [\"numericField:INT ASC\"], \"_limit\": 2, \"_offset\": 0 }",
                    new TypeReference<Map<String, Object>>() {
                    });

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);
            List<String> expectedOrder = List.of("T5", "T3");
            assertPageResults(result, expectedOrder);

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Limit_Offset_Page1", e);
        }
    }

    @Test
    public void test_Limit_Offset_Page2() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"_sort\": [\"numericField:INT ASC\"], \"_limit\": 2, \"_offset\": 2 }",
                    new TypeReference<Map<String, Object>>() {
                    });

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);
            List<String> expectedOrder = List.of("T7", "T2");
            assertPageResults(result, expectedOrder);

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Limit_Offset_Page2", e);
        }
    }

    @Test
    public void test_Limit_Offset_Page3() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"_sort\": [\"numericField:INT ASC\"], \"_limit\": 2, \"_offset\": 4 }",
                    new TypeReference<Map<String, Object>>() {
                    });

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);
            List<String> expectedOrder = List.of("T1", "T6");
            assertPageResults(result, expectedOrder);

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Limit_Offset_Page3", e);
        }
    }

    @Test
    public void test_SortOnNonexistentField_ShouldReturnAll_Unordered() {
        Map<String, Object> criteria = Map.of("_sort", List.of("ghostField ASC"));
        ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
        assertFalse(result.isEmpty(), "Should not throw error, but may return unordered or empty data");
    }

    @Test
    public void test_SortWithTypeSuffixOnly() {
        runJsonTest("{ \"_sort\": [\"numericField:INT DESC\"] }", "ROW_ID", "T4");
    }

    @Test
    public void test_NegativeLimitOrOffset_ShouldThrow() {
        assertThrows(RuntimeException.class, () -> {
            Map<String, Object> criteria = Map.of("_limit", -1, "_offset", -5);
            databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
        });
    }

    @Test
    public void test_Limit_Zero_ShouldReturnNone() {
        Map<String, Object> criteria = Map.of("_limit", 0);
        ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
        assertEquals(0, result.size(), "Expected 0 records when _limit is 0");
    }

    @Test
    public void test_Offset_TooHigh_ShouldReturnEmpty() {
        Map<String, Object> criteria = Map.of("_offset", 9999);
        ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
        assertEquals(0, result.size(), "Expected empty result when offset is beyond range");
    }

}
