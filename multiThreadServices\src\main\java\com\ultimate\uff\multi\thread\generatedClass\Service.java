package com.ultimate.uff.multi.thread.generatedClass;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Service extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String serviceName;
    private final FieldValue<String> serviceNameVal = new FieldValue<>(null);
    private String description;
    private final FieldValue<String> descriptionVal = new FieldValue<>(null);
    private String user;
    private final FieldValue<String> userVal = new FieldValue<>(null);
    private Integer countAgents;
    private final FieldValue<Integer> countAgentsVal = new FieldValue<>(null);
    private String status;
    private final FieldValue<String> statusVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private List<String> recordAuthorizer = new ArrayList<>();
    private final FieldValue<List<String>> recordAuthorizerVal = new FieldValue<>(new ArrayList<>());
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public Service() {}

    public Service(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object serviceNameObj = recordDetail.get("serviceName");
        if (serviceNameObj != null) {
            this.setServiceName(serviceNameObj.toString());
        }
        Object descriptionObj = recordDetail.get("description");
        if (descriptionObj != null) {
            this.setDescription(descriptionObj.toString());
        }
        Object userObj = recordDetail.get("user");
        if (userObj != null) {
            this.setUser(userObj.toString());
        }
        Object countAgentsObj = recordDetail.get("countAgents");
        if (countAgentsObj instanceof Number) {
            this.setCountAgents(((Number) countAgentsObj).intValue());
        }
        Object statusObj = recordDetail.get("status");
        if (statusObj != null) {
            this.setStatus(statusObj.toString());
        }
        Object IDObj = recordDetail.get("ID");
        if (IDObj != null) {
            this.setID(IDObj.toString());
        }
        Object recordStatusObj = recordDetail.get("recordStatus");
        if (recordStatusObj != null) {
            this.setRecordStatus(recordStatusObj.toString());
        }
        Object recordInputterObj = recordDetail.get("recordInputter");
        if (recordInputterObj != null) {
            this.setRecordInputter(recordInputterObj.toString());
        }
        Object listObj = recordDetail.get("recordAuthorizer");
        if (listObj instanceof List<?>) {
            this.setRecordAuthorizer((List<String>) listObj);
        }
        Object recordCountObj = recordDetail.get("recordCount");
        if (recordCountObj != null) {
            this.setRecordCount(recordCountObj.toString());
        }
        Object dateTimeObj = recordDetail.get("dateTime");
        if (dateTimeObj != null) {
            this.setDateTime(dateTimeObj.toString());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (serviceName != null) map.put("serviceName", serviceName);
        if (description != null) map.put("description", description);
        if (user != null) map.put("user", user);
        if (countAgents != null) map.put("countAgents", countAgents);
        if (status != null) map.put("status", status);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "serviceName", "description", "user", "countAgents", "status", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
        this.serviceNameVal.setValue(serviceName);
    }
    public String getServiceName() { return this.serviceName; }
    public FieldValue<String> serviceNameVal() { return serviceNameVal; }
    public void setDescription(String description) {
        this.description = description;
        this.descriptionVal.setValue(description);
    }
    public String getDescription() { return this.description; }
    public FieldValue<String> descriptionVal() { return descriptionVal; }
    public void setUser(String user) {
        this.user = user;
        this.userVal.setValue(user);
    }
    public String getUser() { return this.user; }
    public FieldValue<String> userVal() { return userVal; }
    public void setCountAgents(Integer countAgents) {
        this.countAgents = countAgents;
        this.countAgentsVal.setValue(countAgents);
    }
    public Integer getCountAgents() { return this.countAgents; }
    public FieldValue<Integer> countAgentsVal() { return countAgentsVal; }
    public void setStatus(String status) {
        this.status = status;
        this.statusVal.setValue(status);
    }
    public String getStatus() { return this.status; }
    public FieldValue<String> statusVal() { return statusVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(List<String> recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public List<String> getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<List<String>> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}
