package com.ultimate.uff.queue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.tenant.RecordContext;
import com.ultimate.uff.validation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class RecordProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(RecordProcessingService.class);
    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_ERROR = "error";

    @Autowired private RecordValidationService recordValidationService;
    @Autowired private DefaultingService defaultingService;
    @Autowired private RecordInsertionService recordInsertionService;
    @Autowired private RecordQueueProducer recordQueueProducer;
    @Autowired private ObjectMapper objectMapper;

    public ResponseEntity<Map<String, Object>> processRecord(String tableName, Map<String, Object> request, int authorizeNumber) {
        if (request == null || request.isEmpty()) {
            return buildResponse(new GlobalResponse(false, "Record details cannot be empty"), HttpStatus.BAD_REQUEST);
        }

        logger.info("Processing new record for table [{}] with authorizeNumber [{}], tenantBased = [{}]",
                tableName, authorizeNumber, RecordContext.isTenantBased());

        request = defaultingService.applyDefaults(tableName, request);
        ValidationResponse validationResult = recordValidationService.validateRecord(tableName, request);

        if (validationResult.hasErrors()) {
            try {
                String jsonErrors = objectMapper.writeValueAsString(validationResult.getFieldErrors());
                return buildResponse(new GlobalResponse(false, jsonErrors, request), HttpStatus.BAD_REQUEST);
            } catch (JsonProcessingException e) {
                return buildResponse(new GlobalResponse(false, "Validation and formatting error: " + e.getMessage(), request), HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }

        GlobalResponse kafkaResponse = recordQueueProducer.sendInsertAndWait(
                tableName, request, authorizeNumber,
                RecordContext.getCurrentCompany(),
                RecordContext.getPrimaryCompany(),
                RecordContext.isTenantBased()
        );

        if (authorizeNumber == 0 && kafkaResponse.isSuccess()) {
            authorizeRecord(tableName, request.get("ID").toString(), authorizeNumber);
        }

        return buildResponse(kafkaResponse, kafkaResponse.isSuccess() ? HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ResponseEntity<Map<String, Object>> authorizeRecord(String tableName, String id, int authorizeNumber) {
        if (id == null || id.isEmpty()) {
            return buildResponse(new GlobalResponse(false, "ID cannot be null or empty"), HttpStatus.BAD_REQUEST);
        }

        GlobalResponse kafkaResponse = recordQueueProducer.sendAuthorizeAndWait(
                tableName, id,
                RecordContext.getCurrentCompany(),
                RecordContext.getPrimaryCompany(),
                authorizeNumber,
                RecordContext.isTenantBased()
        );

        return buildResponse(kafkaResponse, kafkaResponse.isSuccess() ? HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ResponseEntity<Map<String, Object>> deleteRecord(String tableName, String id, int authorizeNumber) {
        if (id == null || id.isEmpty()) {
            return buildResponse(new GlobalResponse(false, "ID cannot be null or empty"), HttpStatus.BAD_REQUEST);
        }

        GlobalResponse kafkaResponse = recordQueueProducer.sendDeleteAndWait(
                tableName, id,
                RecordContext.getCurrentCompany(),
                RecordContext.getPrimaryCompany(),
                authorizeNumber,
                RecordContext.isTenantBased()
        );

        return buildResponse(kafkaResponse, kafkaResponse.isSuccess() ? HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ResponseEntity<Map<String, Object>> rejectRecord(String tableName, String id, int authorizeNumber) {
        if (id == null || id.isEmpty()) {
            return buildResponse(new GlobalResponse(false, "ID cannot be null or empty"), HttpStatus.BAD_REQUEST);
        }

        GlobalResponse kafkaResponse = recordQueueProducer.sendRejectAndWait(
                tableName, id,
                RecordContext.getCurrentCompany(),
                RecordContext.getPrimaryCompany(),
                authorizeNumber,
                RecordContext.isTenantBased()
        );

        return buildResponse(kafkaResponse, kafkaResponse.isSuccess() ? HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ResponseEntity<Map<String, Object>> generateId(String tableName) {
        GlobalResponse response = recordInsertionService.generateNextId(tableName);
        return buildResponse(response, response.isSuccess() ? HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR);
    }

    public ResponseEntity<Map<String, Object>> getTableMetadata(String tableName) {
        logger.debug("Fetching metadata for table [{}]", tableName);
        GlobalResponse metadataResponse = recordInsertionService.getTableMetadata(tableName);
        return buildResponse(metadataResponse, metadataResponse.isSuccess() ? HttpStatus.OK : HttpStatus.NOT_FOUND);
    }

    public ResponseEntity<Map<String, Object>> getRecord(String tableName, String id) {
        if (id == null || id.isEmpty()) {
            return buildResponse(new GlobalResponse(false, "ID cannot be null or empty"), HttpStatus.BAD_REQUEST);
        }

        GlobalResponse recordResponse = recordInsertionService.getRecord(tableName, id);
        return buildResponse(recordResponse, recordResponse.isSuccess() ? HttpStatus.OK : HttpStatus.NOT_FOUND);
    }

    public ResponseEntity<Map<String, Object>> validateRecord(String tableName, Map<String, Object> request) {
        if (request == null || request.isEmpty()) {
            return buildResponse(new GlobalResponse(false, "Record details cannot be empty"), HttpStatus.BAD_REQUEST);
        }

        request = defaultingService.applyDefaults(tableName, request);
        ValidationResponse validationResult = recordValidationService.validateRecord(tableName, request);

        if (validationResult.hasErrors()) {
            try {
                String jsonErrors = objectMapper.writeValueAsString(validationResult.getFieldErrors());
                logger.warn("Validation failed for [{}]: {}", tableName, jsonErrors);
                return buildResponse(new GlobalResponse(false, jsonErrors, request), HttpStatus.BAD_REQUEST);
            } catch (JsonProcessingException e) {
                logger.error("Failed to serialize validation error: {}", e.getMessage(), e);
                return buildResponse(new GlobalResponse(false, "Validation failed, and error formatting failed: " + e.getMessage(), request), HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }

        return buildResponse(new GlobalResponse(true, "Record validated successfully", request), HttpStatus.OK);
    }

    private ResponseEntity<Map<String, Object>> buildResponse(GlobalResponse globalResponse, HttpStatus httpStatus) {
        return new ResponseEntity<>(
                globalResponse.getData() != null
                        ? Map.of(
                            "status", globalResponse.isSuccess() ? STATUS_SUCCESS : STATUS_ERROR,
                            "message", globalResponse.getMessage(),
                            "data", globalResponse.getData()
                        )
                        : Map.of(
                            "status", globalResponse.isSuccess() ? STATUS_SUCCESS : STATUS_ERROR,
                            "message", globalResponse.getMessage()
                        ),
                httpStatus
        );
    }
}
