package com.ultimate.uff.service;


import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;

import jakarta.servlet.http.HttpSession;

@Component("privilegeChecker")
public class PrivilegeChecker {

    @Autowired
    private HttpSession session;

    public boolean hasAccessToTable(String tableName) {
        Object raw = session.getAttribute("privileges");
        if (raw == null) {
            System.out.println("✅ No privileges found. Granting superuser access.");
            return true;
        }
        if (raw == null || !(raw instanceof Map)) return false;

        Map<String, Object> privileges = (Map<String, Object>) raw;
        Object typeObj = privileges.get("type");

        if (!(typeObj instanceof List)) return false;

        List<?> typeList = (List<?>) typeObj;
        for (Object entryObj : typeList) {
            if (!(entryObj instanceof Map)) continue;

            Map<?, ?> entry = (Map<?, ?>) entryObj;
            String type = String.valueOf(entry.get("type"));
            String appName = String.valueOf(entry.get("applicationName"));

            if ("table".equals(type) && tableName.equals(appName)) {
                return true;
            }
        }

        return false;
    }
}
