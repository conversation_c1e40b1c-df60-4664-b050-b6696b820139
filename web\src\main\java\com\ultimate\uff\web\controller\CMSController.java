package com.ultimate.uff.web.controller;

import java.util.List;
import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ultimate.uff.multi.thread.services.ServiceManager;


@RestController
@RequestMapping("/api/cms/services")
public class CMSController {
private final ServiceManager serviceManager;
            public CMSController(ServiceManager serviceManager) {
                this.serviceManager = serviceManager;
            }

        // ✅ Start CMS
    @PostMapping("/startCMS")
    public ResponseEntity<String> startCMS() {
        String result = serviceManager.startService("CMS");
        return ResponseEntity.ok(result);
    }

    // ✅ Stop CMS
    @PostMapping("/stopCMS")
    public ResponseEntity<String> stopCMS() {
        String result = serviceManager.stopService("CMS");
        return ResponseEntity.ok(result);
    }

     // Start one service
    @PostMapping("/start/{serviceId}")
    public ResponseEntity<String> startOneService(@PathVariable String serviceId) {
        String result = serviceManager.startService(serviceId);
        return ResponseEntity.ok(result);
    }

    // Stop one service
    @PostMapping("/stop/{serviceId}")
    public ResponseEntity<String> stopOneService(@PathVariable String serviceId) {
        String result = serviceManager.stopService(serviceId);
        return ResponseEntity.ok(result);
    }
    // get statuses services
    @GetMapping("/status")
    public ResponseEntity<List<Map<String, Object>>> getAllServiceStates() {
        List<Map<String, Object>> states = serviceManager.getAllServiceStates();
        return ResponseEntity.ok(states);
    }
        // Get status one service
    @GetMapping("/status/{serviceId}")
    public ResponseEntity<Map<String, Object>> getServiceState(@PathVariable String serviceId) {
        Map<String, Object> state = serviceManager.getServiceState(serviceId);
        return ResponseEntity.ok(state);
    }
     //get detailes service with agents
    @GetMapping("/details")
    public ResponseEntity<List<Map<String, Object>>> getAllServiceDetails() {
            List<Map<String, Object>> serviceDetails = serviceManager.getAllServiceDetails();
            return ResponseEntity.ok(serviceDetails);
        }
    // get log
    @GetMapping("/joblogs")
    public ResponseEntity<List<Map<String, Object>>> getAllJobLogs() {
        // TODO: jobLogService لاحقًا
        return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).body(null);
    }
    
}



/* 
@RestController
@RequestMapping("/cms")
public class CMSController {
    private final CMS cms;
    public CMSController(CMS cms) {
        this.cms = cms;
    }

    // 🟢 Start CMS: load and start AUTO/START services
    @PostMapping("/start")
    public ResponseEntity<String> startCMS() {
        cms.init();
        return ResponseEntity.ok("✅ CMS started. Auto/Start services loaded and running.");
    }
        // 🔴 Stop CMS: stop all agents
        @PostMapping("/stop")
        public ResponseEntity<String> stopCMS() {
            for (ManagerService service : cms.getServices()) {
                service.stop();
            }
            return ResponseEntity.ok("🛑 CMS stopped. All agents have been stopped.");
        }

 /*public ResponseEntity<String> stopCMS() {
        cms.getServices().forEach(ManagerService::stop);
        return ResponseEntity.ok("🛑 CMS stopped. All agents have been stopped.");
    }*/

  /* // 🧩 Start all services with status AUTO or START
    @PostMapping("/services/startAll")
    public ResponseEntity<String> startAllServices() {
        int startedCount = cms.startAutoServices();
        if (startedCount == 0) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("⚠️ No services found to start.");
        }
        return ResponseEntity.ok("✅ " + startedCount + " services started successfully.");
    }*/

 // 🧩 Start all services with status AUTO or START

/*@PostMapping("/services/startAll")
public ResponseEntity<String> startAllServices() {
    List<ManagerService> servicesToStart = cms.getServices().stream()
        .filter(service -> {
            try {
                ServiceStatus status = service.getServiceStatus();
                return status == ServiceStatus.START || status == ServiceStatus.AUTO;
            } catch (IllegalArgumentException | NullPointerException e) {
            return false;
            }
        })
        .collect(Collectors.toList());

    if (servicesToStart.isEmpty()) {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("⚠️ No services found to start.");
    }

    servicesToStart.forEach(ManagerService::start);
    return ResponseEntity.ok("✅ " + servicesToStart.size() + " services started successfully.");
}
/*
    // 🟢 Start individual service by ID
    @PostMapping("/services/{id}/start")
    public ResponseEntity<String> startService(@PathVariable String id) {
        boolean started = cms.startServiceById(id);
        if (!started) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("❌ Service with ID " + id + " not found.");
        }
        return ResponseEntity.ok("✅ Service ID " + id + " started and JAR executed.");
    }*/

     // 🟢 Start individual service by ID
    /*@PostMapping("/services/start/{id}")
    public ResponseEntity<String> startService(@PathVariable String id) {
        Optional<ManagerService> opt = cms.findServiceById(id);
        if (opt.isEmpty()) 
        //return ResponseEntity.notFound().build();
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("❌ Service with ID " + id + " not found.");
        opt.get().start();
        return ResponseEntity.ok("✅ Service ID " + id + " started and JAR executed.");
    }

     // 🔴 Stop individual service by ID
    @PostMapping("/services/stop/{id}")
    public ResponseEntity<String> stopService(@PathVariable String id) {
        Optional<ManagerService> opt = cms.findServiceById(id);
        if (opt.isEmpty())
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("❌ Service with ID " + id + " not found.");
        //return ResponseEntity.notFound().build();
        opt.get().stop();
        return ResponseEntity.ok("🛑 Service ID " + id + " stopped.");
    }
      // 📋 Get all services
    @GetMapping("/service/list")
    public ResponseEntity<List<Service>> getAllServices() {
            return ResponseEntity.ok(cms.getAllServicesRaw());
        }

          //  Get full details of all services with their agents
   /*  @GetMapping("/services/details/all")
    public ResponseEntity<List<ServiceDetailsWithAgentsDTO>> getAllServiceDetails() {
        List<ServiceDetailsWithAgentsDTO> details = cms.getServiceDetailsWithAgents();
        return ResponseEntity.ok(details);
    }*/
        /*@GetMapping("/services/alldet")
    public List<Map<String, Object>> getAllServicesDetails() {
            return cms.getServiceDetailsWithAgents();
        }
        


    
    //  Get summary (basic info) of all services
    /*@GetMapping("/services/sumarydet")
    public ResponseEntity<List<ServiceBasicInfoDTO>> getServiceSummaries() {
        List<ServiceBasicInfoDTO> summaries = cms.getServiceSummaries();
        return ResponseEntity.ok(summaries);
    }*/
    /*@GetMapping("/services/summaries")
    public List<Map<String, Object>> getServiceSummaries() {
        return cms.getServiceSummaries();
    }

    //  Get details of services with their own agents .
    @GetMapping("/services/details/{id}")
    public Map<String, Object> getServiceDetails(@PathVariable String id) {
        //return cms.getServiceDetailsWithAgents(id);
        return null;
    }

}*/
