<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ultimate.uff</groupId>
        <artifactId>uff</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>web</artifactId>

    <dependencies>

    <dependency>
    	<groupId>com.ultimate.uff</groupId>
    	<artifactId>service</artifactId>
    	<version>0.0.1-SNAPSHOT</version>
    </dependency>
    
            <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>


            <dependency>
            	<groupId>com.ultimate.uff</groupId>
            	<artifactId>validation</artifactId>
            	<version>0.0.1-SNAPSHOT</version>
            </dependency>

            <dependency>
            	<groupId>com.ultimate.uff</groupId>
            	<artifactId>multiThreadServices</artifactId>
            	<version>0.0.1-SNAPSHOT</version>
            </dependency>
    </dependencies>
    

</project>