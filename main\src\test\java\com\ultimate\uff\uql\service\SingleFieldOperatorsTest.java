package com.ultimate.uff.uql.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

@SpringBootTest(classes = com.ultimate.uff.main.UffMainApplication.class)
public class SingleFieldOperatorsTest extends BaseCriteriaTest {

    @Test
    public void test_EQ_SingleField() {
        runJsonTest("{ \"singleField\": { \"EQ\": \"singleValue\" } }", "singleValue");
    }

    @Test
    public void test_NE_Negation() {
        runJsonNegativeTest("{ \"singleField\": { \"NE\": \"singleValue\" } }", "singleField", "singleValue");
    }

    @Test
    public void test_EQ_NonexistentField() {
        runJsonNegativeTest("{ \"ghostField\": { \"EQ\": \"ghost\" } }", "ghostField", "ghost");
    }

    @Test
    public void test_EQ_Null_SingleField() {
        runJsonNegativeTest("{ \"singleField\": { \"EQ\": null } }", "singleField", "singleValue");
    }

    @Test
    public void test_RG_Operator() {
        try {
            Map<String, Object> criteria = Map.of(
                    "numericField:INT", Map.of("RG", List.of("10", "50")));

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
            assertFalse(result.isEmpty(), "Expected results within numeric range 10 to 50");

            for (JsonNode row : result) {
                JsonNode json = mapper.readTree(row.get("JSON_ROW_DETAIL").asText());
                int val = json.get("numericField").asInt();
                assertTrue(val >= 10 && val <= 50, "Found value outside range: " + val);
            }

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_RG_Operator", e);
        }
    }

    @Test
    public void test_GE_Operator() {
        try {
            Map<String, Object> criteria = Map.of(
                    "numericField:INT", Map.of("GE", "30"));

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
            assertFalse(result.isEmpty(), "Expected records with value >= 30");

            for (JsonNode row : result) {
                JsonNode json = mapper.readTree(row.get("JSON_ROW_DETAIL").asText());
                int val = json.get("numericField").asInt();
                assertTrue(val >= 30, "Found value less than 30: " + val);
            }

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_GE_Operator", e);
        }
    }

    @Test
    public void test_LE_Operator() {
        try {
            Map<String, Object> criteria = Map.of(
                    "numericField:INT", Map.of("LE", "20"));

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
            assertFalse(result.isEmpty(), "Expected records with value <= 20");

            for (JsonNode row : result) {
                JsonNode json = mapper.readTree(row.get("JSON_ROW_DETAIL").asText());
                int val = json.get("numericField").asInt();
                assertTrue(val <= 20, "Found value greater than 20: " + val);
            }

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_LE_Operator", e);
        }
    }

    @Test
    public void test_NR_Operator() {
        try {
            Map<String, Object> criteriaMap = mapper.readValue(
                    "{ \"numericField\": { \"NR\": [\"10\", \"20\"] } }", new TypeReference<>() {
                    });

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteriaMap);
            assertTrue(result.size() > 0, "Expected records NOT between 10 and 20");

            for (JsonNode node : result) {
                JsonNode json = mapper.readTree(node.get("JSON_ROW_DETAIL").asText());

                if (!json.has("numericField")) {
                    System.out.println("⚠️ Skipping record with no numericField: " + json);
                    continue;
                }

                int num = json.get("numericField").asInt();
                assertTrue(num < 10 || num > 20, "Record falls within excluded range: " + num);
            }

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_NR_Operator", e);
        }
    }

    @Test
    public void test_RG_DateOperator() {
        try {
            Map<String, Object> criteria = Map.of(
                    "createdDate:DATE", Map.of("RG", List.of("2024-01-01", "2024-12-31")));

            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
            assertFalse(result.isEmpty(), "Expected records within date range");

            OffsetDateTime start = OffsetDateTime.parse("2024-01-01T00:00:00Z");
            OffsetDateTime end = OffsetDateTime.parse("2024-12-31T23:59:59Z");

            for (JsonNode row : result) {
                String dateStr = mapper.readTree(row.get("JSON_ROW_DETAIL").asText())
                        .get("createdDate").asText();

                OffsetDateTime actual;
                try {
                    actual = OffsetDateTime.parse(dateStr); // full ISO timestamp
                } catch (DateTimeParseException e) {
                    // Fallback to LocalDate if it's just "YYYY-MM-DD"
                    LocalDate fallbackDate = LocalDate.parse(dateStr);
                    actual = fallbackDate.atStartOfDay().atOffset(OffsetDateTime.now().getOffset());
                }

                assertTrue((!actual.isBefore(start) && !actual.isAfter(end)),
                        "Date out of range: " + actual);
            }

        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_RG_DateOperator", e);
        }
    }

    @Test
    public void test_EQ_Date_WithTime() {
        runJsonTest("{ \"createdDate:DATE\": { \"EQ\": \"2024-01-01T00:00:00Z\" } }", "2024-01-01T00:00:00Z");
    }

    @Test
    public void test_EQ_Date_Only() {
        runJsonTest("{ \"createdDate:DATE\": { \"EQ\": \"2024-03-01\" } }", "2024-03-01T00:00:00Z");
    }

    @Test
    public void test_NE_Date() {
        runJsonNegativeTest("{ \"createdDate:DATE\": { \"NE\": \"2024-01-01\" } }", "createdDate",
                "2024-01-01T00:00:00Z");
    }

    @Test
    public void test_GT_SingleField() {
        runJsonTest("{ \"numericField:INT\": { \"GT\": \"20\" } }", "ROW_ID", "T1"); // numericField = 42
    }

    @Test
    public void test_LT_SingleField() {
        runJsonTest("{ \"numericField:INT\": { \"LT\": \"50\" } }", "ROW_ID", "T1"); // numericField = 42
    }

    @Test
    public void test_DNBW_CreatedDate() {
        runJsonNegativeTest("{ \"createdDate\": { \"DNBW\": \"2024\" } }", "createdDate", "2024-01-01T00:00:00Z");
    }

    @Test
    public void test_DNEW_CreatedDate() {
        runJsonNegativeTest("{ \"createdDate\": { \"DNEW\": \"Z\" } }", "createdDate", "2024-01-01T00:00:00Z");
    }

    @Test
    public void test_LK_SingleField() {
        runJsonTest("{ \"singleField\": { \"LK\": \"%Value\" } }", "singleValue");
    }

    @Test
    public void test_CT_SingleField() {
        runJsonTest("{ \"singleField\": { \"CT\": \"Val\" } }", "singleValue");
    }

    @Test
    public void test_EQ_Numeric_Zero() {
        runJsonNegativeTest("{ \"numericField:INT\": { \"EQ\": \"0\" } }", "numericField", "0");
    }

    @Test
    public void test_NE_Numeric_UpperBound() {
        runJsonNegativeTest("{ \"numericField:INT\": { \"NE\": \"88\" } }", "numericField", "88");
    }

    @Test
    public void test_GE_Numeric_ExactMatch() {
        runJsonTest("{ \"numericField:INT\": { \"GE\": \"42\" } }", "numericField", "42");
    }

    @Test
    public void test_LE_Numeric_ExactMatch() {
        runJsonTest("{ \"numericField:INT\": { \"LE\": \"10\" } }", "numericField", "10");
    }

    @Test
    public void test_RG_Numeric_ReversedRange() {
        Map<String, Object> criteria = Map.of("numericField:INT", Map.of("RG", List.of("50", "10")));
        ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
        assertEquals(0, result.size(), "Expected no results for reversed range");
    }

    @Test
    public void test_EQ_Date_ExactTimestamp() {
        runJsonTest("{ \"createdDate:DATE\": { \"EQ\": \"2024-01-01T00:00:00Z\" } }", "createdDate",
                "2024-01-01T00:00:00Z");
    }

    @Test
    public void test_EQ_Date_WithoutTime() {
        runJsonTest("{ \"createdDate:DATE\": { \"EQ\": \"2024-01-01\" } }", "createdDate", "2024-01-01T00:00:00Z");
    }

    @Test
    public void test_NE_Date_ExcludeMatch() {
        runJsonNegativeTest("{ \"createdDate:DATE\": { \"NE\": \"2025-06-29\" } }", "createdDate",
                "2025-06-29T00:00:00Z");
    }

    @Test
    public void test_EQ_Date_InvalidFormat() {
        assertThrows(RuntimeException.class, () -> {
            Map<String, Object> criteria = Map.of("createdDate:DATE", Map.of("EQ", "not-a-date"));
            databaseService.getRecordsByCriteria(TEST_TABLE, criteria);
        });
    }

    @Test
    public void test_EQ_Date_Null() {
        runJsonNegativeTest("{ \"createdDate:DATE\": { \"EQ\": null } }", "createdDate", "2024-01-01T00:00:00Z");
    }

    @Test
    public void test_NE_NonNullDateField() {
        runJsonTest("{ \"createdDate:DATE\": { \"NE\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_Null_OnMissingField_ShouldMatch() {
        runJsonTest("{ \"ghostDateField:DATE\": { \"EQ\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_NE_Null_OnMissingField_ShouldNotMatch() {
        runJsonNegativeTest("{ \"ghostDateField:DATE\": { \"NE\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_NE_OnMissingField_ShouldNotMatch() {
        // Expect no match because NE null requires the field to exist and be NOT NULL
        runJsonNegativeTest("{ \"ghostDateField:DATE\": { \"NE\": null } }", "ROW_ID", "T1");
    }

    @Test
    public void test_EQ_MissingField_ShouldNotMatch() {
        runJsonNegativeTest("{ \"nonexistentField\": { \"EQ\": \"someValue\" } }", "ROW_ID", "T1");
    }

    @Test
    public void test_NE_FieldWithNull_ShouldNotMatch() {
        // You may want to add a record with `"nullableField": null`
        runJsonNegativeTest("{ \"nullableField\": { \"NE\": null } }", "nullableField", "any");
    }

    @Test
    public void test_EQ_IntField_AsString() {
        runJsonTest("{ \"numericField:INT\": { \"EQ\": \"42\" } }", "numericField", "42");
    }

    @Test
    public void test_LK_SingleField_FullMatch() {
        runJsonTest("{ \"singleField\": { \"LK\": \"%single%\" } }", "singleValue");
    }

    @Test
    public void test_LK_SingleField_SuffixMatch() {
        runJsonTest("{ \"singleField\": { \"LK\": \"%Value\" } }", "singleValue");
    }

    @Test
    public void test_LI_SingleField_CaseInsensitive() {
        runJsonTest("{ \"singleField\": { \"LI\": \"%VALUE%\" } }", "singleValue");
    }

    @Test
    public void test_LK_SingleField_NoMatch() {
        runJsonNegativeTest("{ \"singleField\": { \"LK\": \"%xyz%\" } }", "singleField", "singleValue");
    }

}
