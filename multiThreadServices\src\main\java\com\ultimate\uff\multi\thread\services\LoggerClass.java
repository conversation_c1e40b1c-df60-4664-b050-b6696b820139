package com.ultimate.uff.multi.thread.services;



    import ch.qos.logback.classic.Logger;
    import ch.qos.logback.classic.LoggerContext;
    import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
    import ch.qos.logback.core.FileAppender;
    import jakarta.annotation.PostConstruct;
    import org.slf4j.LoggerFactory;
    import org.springframework.stereotype.Component;
    import java.io.File;
    @Component
    public class LoggerClass {
        private Logger fileLogger;
    
        @PostConstruct
        public void initLogger() {
            try {
                File logsDir = new File("logs");
                if (!logsDir.exists()) logsDir.mkdirs();
    
                LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
    
                PatternLayoutEncoder encoder = new PatternLayoutEncoder();
                encoder.setContext(loggerContext);
                encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n");
                encoder.start();
    
                FileAppender fileAppender = new FileAppender<>();
                fileAppender.setContext(loggerContext);
                fileAppender.setFile("logs/ServiceLog.log");
                fileAppender.setEncoder(encoder);
                fileAppender.setAppend(true);
                fileAppender.start();
    
                Logger logger = loggerContext.getLogger("ServiceLog");
                logger.detachAndStopAllAppenders();
                logger.addAppender(fileAppender);
                logger.setAdditive(false);
    
                this.fileLogger = logger;
            } catch (Exception e) {
                System.err.println("❌ Failed to initialize logger: " + e.getMessage());
            }
        }
    
        public Logger getLogger() {
            return fileLogger;
        }
    }
    
