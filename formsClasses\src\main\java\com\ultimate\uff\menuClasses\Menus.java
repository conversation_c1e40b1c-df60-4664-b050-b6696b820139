package com.ultimate.uff.menuClasses;

import java.util.HashMap;
import java.util.Map;

public class Menus {

    private String application;
    private String type;
    

    // Constructor to initialize from a map
    public Menus(Map<String, Object> fieldDetail) {
        this.application = (String) fieldDetail.get("application");
        this.type = (String) fieldDetail.get("type");

    }
       

    // Convert Menus object to Map to put back into recordDetail
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("application", this.application);
        map.put("type", this.type);
   
        return map;
    }

    // Getters and setters
    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


}
