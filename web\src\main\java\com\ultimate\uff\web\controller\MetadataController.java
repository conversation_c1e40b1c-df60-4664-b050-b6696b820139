package com.ultimate.uff.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.ultimate.uff.service.MergingService;

@RestController
public class MetadataController {

    @Autowired
    private MergingService mergingService;

    // New API endpoint to get merged metadata
    @GetMapping("api/merged/{tableName}/metadata")
    public ResponseEntity<String> getMergedTableMetadata(@PathVariable String tableName) {
        // Call the merging service to get the merged data
        String mergedMetadata = mergingService.mergeApiResponses(tableName);

        if (mergedMetadata != null && !mergedMetadata.isEmpty()) {
            return ResponseEntity.ok(mergedMetadata);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}
