#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd9838e4b3, pid=2532, tid=30156
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x3de4b3]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-3aa034fe4722eeb2418aea1040b4c666-sock

Host: Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz, 8 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.1)
Time: Tue May 27 14:27:08 2025 Egypt Daylight Time elapsed time: 69.558080 seconds (0d 0h 1m 9s)

---------------  T H R E A D  ---------------

Current thread (0x000002654777c900):  JavaThread "LocalFile Deleter" daemon [_thread_in_vm, id=30156, stack(0x0000006c4ba00000,0x0000006c4bb00000) (1024K)]

Stack: [0x0000006c4ba00000,0x0000006c4bb00000],  sp=0x0000006c4baff460,  free space=1021k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x3de4b3]
V  [jvm.dll+0x6ce032]
V  [jvm.dll+0x3df13b]
V  [jvm.dll+0x3df3c7]
V  [jvm.dll+0x472b45]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
