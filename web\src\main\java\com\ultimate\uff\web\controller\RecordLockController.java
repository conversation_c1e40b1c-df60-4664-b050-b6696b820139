package com.ultimate.uff.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.ultimate.uff.validation.RecordLockService;

import java.util.Map;

import jakarta.servlet.http.HttpSession;

@RestController
@RequestMapping("/api/tables")
public class RecordLockController {

    @Autowired
    private RecordLockService recordLockService;

    @Autowired
    private HttpSession session;

    // Endpoint to lock a record
    @PostMapping("/{tableName}/records/{id}/lock")
    public ResponseEntity<Map<String, Object>> lockRecord(@PathVariable String tableName, @PathVariable String id) {
        // Get the current user's ID from session
        String userId = getUserIdFromSession();

        // Attempt to lock the record
        boolean locked = recordLockService.lockRecord(tableName, id, userId);

        if (locked) {
            return ResponseEntity.ok(Map.of("status", "success", "message", "Record locked successfully"));
        } else {
            return ResponseEntity.status(409).body(Map.of("status", "error", "message", "Record is already locked by another user"));
        }
    }

    // Helper method to get user ID from session
    private String getUserIdFromSession() {
        Object user = session.getAttribute("user");
        if (user instanceof JsonNode jsonUser) {
            return jsonUser.get("ROW_ID").asText();
        }
        return "UNKNOWN_USER"; // Default fallback (should not happen in a real system)
    }

    // Force unlock a record (Admin/Supervisor Only)
    @DeleteMapping("/{tableName}/records/{id}/force-unlock")
    public ResponseEntity<Map<String, Object>> forceUnlockRecord(@PathVariable String tableName, @PathVariable String id) {
        recordLockService.unlockRecord(tableName, id);
        return ResponseEntity.ok(Map.of("status", "success", "message", "Record unlocked successfully"));
    }
}
