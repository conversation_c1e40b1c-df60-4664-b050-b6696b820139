#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1610416 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=19148, tid=200
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4a6d688438150d7a4e656f3ed6a5a7e9-sock

Host: Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz, 8 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.1)
Time: Tue May 27 14:25:57 2025 Egypt Daylight Time elapsed time: 172.317938 seconds (0d 0h 2m 52s)

---------------  T H R E A D  ---------------

Current thread (0x000001df57a52550):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=200, stack(0x0000004fcbc00000,0x0000004fcbd00000) (1024K)]


Current CompileTask:
C2:172318 17276       4       org.eclipse.jdt.internal.core.JavaProject$ModuleLookup::addTransitive (71 bytes)

Stack: [0x0000004fcbc00000,0x0000004fcbd00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b68f2]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x43340]
C  [KERNEL32.DLL+0x31fd7]
C  [ntdll.dll+0x6d7d0]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001df62aeda60, length=64, elements={
0x000001df4368bbf0, 0x000001df57a445a0, 0x000001df57a46d10, 0x000001df57a48cf0,
0x000001df57a49940, 0x000001df57a4e1a0, 0x000001df57a4ed20, 0x000001df57a52550,
0x000001df57a6a4d0, 0x000001df594e0cd0, 0x000001df5972b6c0, 0x000001df5e995000,
0x000001df5ea299e0, 0x000001df5ec2f850, 0x000001df5eaa0c80, 0x000001df5ea68360,
0x000001df5ec731c0, 0x000001df5ee9b8d0, 0x000001df5ee9e030, 0x000001df5ee9d9a0,
0x000001df5ee9bf60, 0x000001df5ee9c5f0, 0x000001df5ee9e6c0, 0x000001df5ee9ed50,
0x000001df60d58240, 0x000001df60d58f60, 0x000001df60d57520, 0x000001df60d595f0,
0x000001df60d59c80, 0x000001df60d588d0, 0x000001df60d5a310, 0x000001df60d5b030,
0x000001df60d5a9a0, 0x000001df60d5de20, 0x000001df60d5e4b0, 0x000001df60d5ca70,
0x000001df5e57d140, 0x000001df5e57eb80, 0x000001df5e57d7d0, 0x000001df5e581970,
0x000001df5e57e4f0, 0x000001df5e582690, 0x000001df60d5d100, 0x000001df60d5d790,
0x000001df60d5eb40, 0x000001df60d5b6c0, 0x000001df5ee9d310, 0x000001df5fa3e0e0,
0x000001df5fa3fb20, 0x000001df5fa3e770, 0x000001df5fa40840, 0x000001df5fa3d3c0,
0x000001df5fa3ee00, 0x000001df5fa3f490, 0x000001df60d5c3e0, 0x000001df61c52030,
0x000001df61c54100, 0x000001df61c55b40, 0x000001df61c54790, 0x000001df61c54e20,
0x000001df61c56ef0, 0x000001df61c53a70, 0x000001df6b98b910, 0x000001df5f4fda60
}

Java Threads: ( => current thread )
  0x000001df4368bbf0 JavaThread "main"                              [_thread_blocked, id=24452, stack(0x0000004fcb200000,0x0000004fcb300000) (1024K)]
  0x000001df57a445a0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=19084, stack(0x0000004fcb600000,0x0000004fcb700000) (1024K)]
  0x000001df57a46d10 JavaThread "Finalizer"                  daemon [_thread_blocked, id=34224, stack(0x0000004fcb700000,0x0000004fcb800000) (1024K)]
  0x000001df57a48cf0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=20420, stack(0x0000004fcb800000,0x0000004fcb900000) (1024K)]
  0x000001df57a49940 JavaThread "Attach Listener"            daemon [_thread_blocked, id=30724, stack(0x0000004fcb900000,0x0000004fcba00000) (1024K)]
  0x000001df57a4e1a0 JavaThread "Service Thread"             daemon [_thread_blocked, id=17032, stack(0x0000004fcba00000,0x0000004fcbb00000) (1024K)]
  0x000001df57a4ed20 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=5828, stack(0x0000004fcbb00000,0x0000004fcbc00000) (1024K)]
=>0x000001df57a52550 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=200, stack(0x0000004fcbc00000,0x0000004fcbd00000) (1024K)]
  0x000001df57a6a4d0 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=33896, stack(0x0000004fcbd00000,0x0000004fcbe00000) (1024K)]
  0x000001df594e0cd0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=9652, stack(0x0000004fcbe00000,0x0000004fcbf00000) (1024K)]
  0x000001df5972b6c0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=19812, stack(0x0000004fcbf00000,0x0000004fcc000000) (1024K)]
  0x000001df5e995000 JavaThread "Active Thread: Equinox Container: d8c09fcc-4368-4f50-8994-db023961d503"        [_thread_blocked, id=35724, stack(0x0000004fcc800000,0x0000004fcc900000) (1024K)]
  0x000001df5ea299e0 JavaThread "Refresh Thread: Equinox Container: d8c09fcc-4368-4f50-8994-db023961d503" daemon [_thread_blocked, id=15940, stack(0x0000004fcc900000,0x0000004fcca00000) (1024K)]
  0x000001df5ec2f850 JavaThread "Framework Event Dispatcher: Equinox Container: d8c09fcc-4368-4f50-8994-db023961d503" daemon [_thread_blocked, id=16048, stack(0x0000004fccb00000,0x0000004fccc00000) (1024K)]
  0x000001df5eaa0c80 JavaThread "Start Level: Equinox Container: d8c09fcc-4368-4f50-8994-db023961d503" daemon [_thread_blocked, id=29196, stack(0x0000004fccc00000,0x0000004fccd00000) (1024K)]
  0x000001df5ea68360 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=16008, stack(0x0000004fccd00000,0x0000004fcce00000) (1024K)]
  0x000001df5ec731c0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=33876, stack(0x0000004fcce00000,0x0000004fccf00000) (1024K)]
  0x000001df5ee9b8d0 JavaThread "Worker-JM"                         [_thread_blocked, id=32068, stack(0x0000004fcd000000,0x0000004fcd100000) (1024K)]
  0x000001df5ee9e030 JavaThread "Worker-0"                          [_thread_blocked, id=5112, stack(0x0000004fcc100000,0x0000004fcc200000) (1024K)]
  0x000001df5ee9d9a0 JavaThread "Worker-1"                          [_thread_blocked, id=5100, stack(0x0000004fcd100000,0x0000004fcd200000) (1024K)]
  0x000001df5ee9bf60 JavaThread "Worker-2: Initialize Workspace"        [_thread_blocked, id=19324, stack(0x0000004fcc000000,0x0000004fcc100000) (1024K)]
  0x000001df5ee9c5f0 JavaThread "Worker-3"                          [_thread_blocked, id=13652, stack(0x0000004fcd400000,0x0000004fcd500000) (1024K)]
  0x000001df5ee9e6c0 JavaThread "Java indexing"              daemon [_thread_in_vm, id=19876, stack(0x0000004fcd500000,0x0000004fcd600000) (1024K)]
  0x000001df5ee9ed50 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=14776, stack(0x0000004fcd600000,0x0000004fcd700000) (1024K)]
  0x000001df60d58240 JavaThread "Thread-2"                   daemon [_thread_in_native, id=19976, stack(0x0000004fcdc00000,0x0000004fcdd00000) (1024K)]
  0x000001df60d58f60 JavaThread "Thread-3"                   daemon [_thread_in_native, id=33496, stack(0x0000004fcdd00000,0x0000004fcde00000) (1024K)]
  0x000001df60d57520 JavaThread "Thread-4"                   daemon [_thread_in_native, id=32236, stack(0x0000004fcde00000,0x0000004fcdf00000) (1024K)]
  0x000001df60d595f0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=19980, stack(0x0000004fcdf00000,0x0000004fce000000) (1024K)]
  0x000001df60d59c80 JavaThread "Thread-6"                   daemon [_thread_in_native, id=29308, stack(0x0000004fce000000,0x0000004fce100000) (1024K)]
  0x000001df60d588d0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=16460, stack(0x0000004fce100000,0x0000004fce200000) (1024K)]
  0x000001df60d5a310 JavaThread "Thread-8"                   daemon [_thread_in_native, id=20732, stack(0x0000004fce200000,0x0000004fce300000) (1024K)]
  0x000001df60d5b030 JavaThread "Thread-9"                   daemon [_thread_in_native, id=10024, stack(0x0000004fce300000,0x0000004fce400000) (1024K)]
  0x000001df60d5a9a0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=22136, stack(0x0000004fce400000,0x0000004fce500000) (1024K)]
  0x000001df60d5de20 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=8528, stack(0x0000004fce500000,0x0000004fce600000) (1024K)]
  0x000001df60d5e4b0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=20780, stack(0x0000004fce600000,0x0000004fce700000) (1024K)]
  0x000001df60d5ca70 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=27500, stack(0x0000004fce700000,0x0000004fce800000) (1024K)]
  0x000001df5e57d140 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=29252, stack(0x0000004fcef00000,0x0000004fcf000000) (1024K)]
  0x000001df5e57eb80 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=33880, stack(0x0000004fcf100000,0x0000004fcf200000) (1024K)]
  0x000001df5e57d7d0 JavaThread "Worker-6"                          [_thread_blocked, id=25360, stack(0x0000004fcaf00000,0x0000004fcb000000) (1024K)]
  0x000001df5e581970 JavaThread "Worker-7: Initialize workspace"        [_thread_blocked, id=22044, stack(0x0000004fcb100000,0x0000004fcb200000) (1024K)]
  0x000001df5e57e4f0 JavaThread "Worker-8: Java indexing... "        [_thread_blocked, id=19648, stack(0x0000004fcd200000,0x0000004fcd300000) (1024K)]
  0x000001df5e582690 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=25556, stack(0x0000004fcd300000,0x0000004fcd400000) (1024K)]
  0x000001df60d5d100 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=24212, stack(0x0000004fcda00000,0x0000004fcdb00000) (1024K)]
  0x000001df60d5d790 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=8640, stack(0x0000004fcdb00000,0x0000004fcdc00000) (1024K)]
  0x000001df60d5eb40 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=14016, stack(0x0000004fce800000,0x0000004fce900000) (1024K)]
  0x000001df60d5b6c0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=26716, stack(0x0000004fce900000,0x0000004fcea00000) (1024K)]
  0x000001df5ee9d310 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=21972, stack(0x0000004fcea00000,0x0000004fceb00000) (1024K)]
  0x000001df5fa3e0e0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=26004, stack(0x0000004fced00000,0x0000004fcee00000) (1024K)]
  0x000001df5fa3fb20 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=14612, stack(0x0000004fcee00000,0x0000004fcef00000) (1024K)]
  0x000001df5fa3e770 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=18652, stack(0x0000004fcf000000,0x0000004fcf100000) (1024K)]
  0x000001df5fa40840 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=22788, stack(0x0000004fcf300000,0x0000004fcf400000) (1024K)]
  0x000001df5fa3d3c0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=17820, stack(0x0000004fcf400000,0x0000004fcf500000) (1024K)]
  0x000001df5fa3ee00 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=28752, stack(0x0000004fcf500000,0x0000004fcf600000) (1024K)]
  0x000001df5fa3f490 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=23796, stack(0x0000004fcf600000,0x0000004fcf700000) (1024K)]
  0x000001df60d5c3e0 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=32588, stack(0x0000004fcf700000,0x0000004fcf800000) (1024K)]
  0x000001df61c52030 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=21660, stack(0x0000004fcf800000,0x0000004fcf900000) (1024K)]
  0x000001df61c54100 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=27740, stack(0x0000004fcf900000,0x0000004fcfa00000) (1024K)]
  0x000001df61c55b40 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=26128, stack(0x0000004fcfa00000,0x0000004fcfb00000) (1024K)]
  0x000001df61c54790 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=20860, stack(0x0000004fcfb00000,0x0000004fcfc00000) (1024K)]
  0x000001df61c54e20 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=21924, stack(0x0000004fcfc00000,0x0000004fcfd00000) (1024K)]
  0x000001df61c56ef0 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=26664, stack(0x0000004fcb000000,0x0000004fcb100000) (1024K)]
  0x000001df61c53a70 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=15424, stack(0x0000004fcd700000,0x0000004fcd800000) (1024K)]
  0x000001df6b98b910 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=26696, stack(0x0000004fcd800000,0x0000004fcd900000) (1024K)]
  0x000001df5f4fda60 JavaThread "C2 CompilerThread2"         daemon [_thread_in_vm, id=22920, stack(0x0000004fceb00000,0x0000004fcec00000) (1024K)]
Total: 64

Other Threads:
  0x000001df57a225f0 VMThread "VM Thread"                           [id=11500, stack(0x0000004fcb500000,0x0000004fcb600000) (1024K)]
  0x000001df5795ca00 WatcherThread "VM Periodic Task Thread"        [id=18824, stack(0x0000004fcb400000,0x0000004fcb500000) (1024K)]
  0x000001df436aaf90 WorkerThread "GC Thread#0"                     [id=33536, stack(0x0000004fcb300000,0x0000004fcb400000) (1024K)]
  0x000001df5e38c7a0 WorkerThread "GC Thread#1"                     [id=14584, stack(0x0000004fcc200000,0x0000004fcc300000) (1024K)]
  0x000001df5e390820 WorkerThread "GC Thread#2"                     [id=18132, stack(0x0000004fcc300000,0x0000004fcc400000) (1024K)]
  0x000001df5e390bc0 WorkerThread "GC Thread#3"                     [id=33668, stack(0x0000004fcc400000,0x0000004fcc500000) (1024K)]
  0x000001df5e546580 WorkerThread "GC Thread#4"                     [id=31376, stack(0x0000004fcc500000,0x0000004fcc600000) (1024K)]
  0x000001df5e546920 WorkerThread "GC Thread#5"                     [id=12084, stack(0x0000004fcc600000,0x0000004fcc700000) (1024K)]
  0x000001df5e546cc0 WorkerThread "GC Thread#6"                     [id=2592, stack(0x0000004fcc700000,0x0000004fcc800000) (1024K)]
  0x000001df5eacd5c0 WorkerThread "GC Thread#7"                     [id=19472, stack(0x0000004fcca00000,0x0000004fccb00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  172350 17276       4       org.eclipse.jdt.internal.core.JavaProject$ModuleLookup::addTransitive (71 bytes)
C1 CompilerThread0  172350 17396       3       lombok.eclipse.TransformEclipseAST::transform_swapped (6 bytes)
C2 CompilerThread1  172350 17311       4       java.lang.reflect.Field::get (39 bytes)
C2 CompilerThread2  172350 17378       4       org.eclipse.jdt.internal.compiler.parser.Parser::consumeRule (7748 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001df00000000-0x000001df00ba0000-0x000001df00ba0000), size 12189696, SharedBaseAddress: 0x000001df00000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001df01000000-0x000001df41000000, reserved size: 1073741824
Narrow klass base: 0x000001df00000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 8066M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 4608K, used 3603K [0x00000000eab00000, 0x00000000eb280000, 0x0000000100000000)
  eden space 3584K, 72% used [0x00000000eab00000,0x00000000ead8a760,0x00000000eae80000)
  from space 1024K, 97% used [0x00000000eb000000,0x00000000eb0fa830,0x00000000eb100000)
  to   space 1536K, 0% used [0x00000000eb100000,0x00000000eb100000,0x00000000eb280000)
 ParOldGen       total 333824K, used 333541K [0x00000000c0000000, 0x00000000d4600000, 0x00000000eab00000)
  object space 333824K, 99% used [0x00000000c0000000,0x00000000d45b9710,0x00000000d4600000)
 Metaspace       used 77124K, committed 78720K, reserved 1179648K
  class space    used 7844K, committed 8576K, reserved 1048576K

Card table byte_map: [0x000001df43040000,0x000001df43250000] _byte_map_base: 0x000001df42a40000

Marking Bits: (ParMarkBitMap*) 0x00007ffd98c631f0
 Begin Bits: [0x000001df55880000, 0x000001df56880000)
 End Bits:   [0x000001df56880000, 0x000001df57880000)

Polling page: 0x000001df42e20000

Metaspace:

Usage:
  Non-class:     67.66 MB used.
      Class:      7.66 MB used.
       Both:     75.32 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      68.50 MB ( 54%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       8.38 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      76.88 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  10.56 MB
       Class:  7.45 MB
        Both:  18.02 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 127.50 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1380.
num_arena_deaths: 38.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1230.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 57.
num_chunks_taken_from_freelist: 4900.
num_chunk_merges: 20.
num_chunk_splits: 2998.
num_chunks_enlarged: 1712.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=14850Kb max_used=15473Kb free=105149Kb
 bounds [0x000001df4e350000, 0x000001df4f270000, 0x000001df55880000]
CodeHeap 'profiled nmethods': size=120000Kb used=35145Kb max_used=38194Kb free=84854Kb
 bounds [0x000001df46880000, 0x000001df48e60000, 0x000001df4ddb0000]
CodeHeap 'non-nmethods': size=5760Kb used=1500Kb max_used=1606Kb free=4259Kb
 bounds [0x000001df4ddb0000, 0x000001df4e020000, 0x000001df4e350000]
 total_blobs=16032 nmethods=15212 adapters=725
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 171.302 Thread 0x000001df5f4fda60 17294       4       org.eclipse.jdt.internal.compiler.lookup.LookupEnvironment::convertUnresolvedBinaryToRawType (291 bytes)
Event: 171.304 Thread 0x000001df57a6a4d0 17371       3       java.util.LinkedList::indexOf (73 bytes)
Event: 171.304 Thread 0x000001df57a6a4d0 nmethod 17371 0x000001df46a70710 code [0x000001df46a70900, 0x000001df46a70d68]
Event: 171.304 Thread 0x000001df57a6a4d0 17372       1       org.eclipse.jdt.internal.core.jdom.CompilationUnit::getMainTypeName (5 bytes)
Event: 171.304 Thread 0x000001df57a6a4d0 nmethod 17372 0x000001df4e77dc10 code [0x000001df4e77dda0, 0x000001df4e77de68]
Event: 171.304 Thread 0x000001df57a6a4d0 17373       3       org.eclipse.jdt.internal.compiler.parser.Parser::goForBlockStatementsopt (16 bytes)
Event: 171.305 Thread 0x000001df57a6a4d0 nmethod 17373 0x000001df46a2e890 code [0x000001df46a2ea20, 0x000001df46a2eb60]
Event: 171.306 Thread 0x000001df57a6a4d0 17374       3       org.eclipse.jdt.internal.compiler.parser.Parser::consumeImportDeclaration (76 bytes)
Event: 171.306 Thread 0x000001df57a6a4d0 nmethod 17374 0x000001df46af5d90 code [0x000001df46af5f60, 0x000001df46af63d8]
Event: 171.306 Thread 0x000001df57a6a4d0 17375       3       org.eclipse.jdt.internal.compiler.ast.MethodDeclaration::parseStatements (7 bytes)
Event: 171.307 Thread 0x000001df57a6a4d0 nmethod 17375 0x000001df46b07e90 code [0x000001df46b08040, 0x000001df46b08218]
Event: 171.307 Thread 0x000001df57a6a4d0 17376   !   3       org.eclipse.jdt.internal.compiler.parser.Parser::parse (397 bytes)
Event: 171.309 Thread 0x000001df57a6a4d0 nmethod 17376 0x000001df46938e10 code [0x000001df469391c0, 0x000001df4693af28]
Event: 171.309 Thread 0x000001df57a6a4d0 17377       3       org.eclipse.jdt.internal.compiler.parser.Parser::newMessageSend (65 bytes)
Event: 171.310 Thread 0x000001df57a6a4d0 nmethod 17377 0x000001df469b4110 code [0x000001df469b4300, 0x000001df469b48a0]
Event: 171.310 Thread 0x000001df5f4fda60 nmethod 17294 0x000001df4e98c710 code [0x000001df4e98c960, 0x000001df4e98d038]
Event: 171.310 Thread 0x000001df5f4fda60 17378       4       org.eclipse.jdt.internal.compiler.parser.Parser::consumeRule (7748 bytes)
Event: 171.310 Thread 0x000001df57a6a4d0 17379       3       lombok.eclipse.agent.PatchVal::matches (44 bytes)
Event: 171.311 Thread 0x000001df57a6a4d0 nmethod 17379 0x000001df47759690 code [0x000001df477598c0, 0x000001df4775a0d8]
Event: 171.314 Thread 0x000001df57a6a4d0 17380       3       org.eclipse.jdt.internal.compiler.parser.Parser::pushOnGenericsStack (106 bytes)

GC Heap History (20 events):
Event: 168.755 GC heap before
{Heap before GC invocations=1330 (full 4):
 PSYoungGen      total 4096K, used 3616K [0x00000000eab00000, 0x00000000eb100000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 1536K, 68% used [0x00000000ead80000,0x00000000eae88000,0x00000000eaf00000)
  to   space 1536K, 0% used [0x00000000eaf80000,0x00000000eaf80000,0x00000000eb100000)
 ParOldGen       total 328192K, used 328137K [0x00000000c0000000, 0x00000000d4080000, 0x00000000eab00000)
  object space 328192K, 99% used [0x00000000c0000000,0x00000000d40725d8,0x00000000d4080000)
 Metaspace       used 76955K, committed 78592K, reserved 1179648K
  class space    used 7834K, committed 8576K, reserved 1048576K
}
Event: 168.761 GC heap after
{Heap after GC invocations=1330 (full 4):
 PSYoungGen      total 3584K, used 790K [0x00000000eab00000, 0x00000000eb080000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 1024K, 77% used [0x00000000eaf80000,0x00000000eb045bc8,0x00000000eb080000)
  to   space 1024K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf80000)
 ParOldGen       total 329216K, used 329069K [0x00000000c0000000, 0x00000000d4180000, 0x00000000eab00000)
  object space 329216K, 99% used [0x00000000c0000000,0x00000000d415b468,0x00000000d4180000)
 Metaspace       used 76955K, committed 78592K, reserved 1179648K
  class space    used 7834K, committed 8576K, reserved 1048576K
}
Event: 168.856 GC heap before
{Heap before GC invocations=1331 (full 4):
 PSYoungGen      total 3584K, used 3146K [0x00000000eab00000, 0x00000000eb080000, 0x0000000100000000)
  eden space 2560K, 92% used [0x00000000eab00000,0x00000000ead4cf58,0x00000000ead80000)
  from space 1024K, 77% used [0x00000000eaf80000,0x00000000eb045bc8,0x00000000eb080000)
  to   space 1024K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf80000)
 ParOldGen       total 329216K, used 329069K [0x00000000c0000000, 0x00000000d4180000, 0x00000000eab00000)
  object space 329216K, 99% used [0x00000000c0000000,0x00000000d415b468,0x00000000d4180000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.859 GC heap after
{Heap after GC invocations=1331 (full 4):
 PSYoungGen      total 3584K, used 991K [0x00000000eab00000, 0x00000000eb080000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 1024K, 96% used [0x00000000eae80000,0x00000000eaf77e20,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000eaf80000,0x00000000eaf80000,0x00000000eb080000)
 ParOldGen       total 329728K, used 329698K [0x00000000c0000000, 0x00000000d4200000, 0x00000000eab00000)
  object space 329728K, 99% used [0x00000000c0000000,0x00000000d41f88c8,0x00000000d4200000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.925 GC heap before
{Heap before GC invocations=1332 (full 4):
 PSYoungGen      total 3584K, used 3551K [0x00000000eab00000, 0x00000000eb080000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 1024K, 96% used [0x00000000eae80000,0x00000000eaf77e20,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000eaf80000,0x00000000eaf80000,0x00000000eb080000)
 ParOldGen       total 329728K, used 329698K [0x00000000c0000000, 0x00000000d4200000, 0x00000000eab00000)
  object space 329728K, 99% used [0x00000000c0000000,0x00000000d41f88c8,0x00000000d4200000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.928 GC heap after
{Heap after GC invocations=1332 (full 4):
 PSYoungGen      total 3584K, used 1022K [0x00000000eab00000, 0x00000000eb180000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 1024K, 99% used [0x00000000eaf80000,0x00000000eb07f918,0x00000000eb080000)
  to   space 2048K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eaf80000)
 ParOldGen       total 330752K, used 330294K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 99% used [0x00000000c0000000,0x00000000d428d8e0,0x00000000d4300000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.940 GC heap before
{Heap before GC invocations=1333 (full 4):
 PSYoungGen      total 3584K, used 3582K [0x00000000eab00000, 0x00000000eb180000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 1024K, 99% used [0x00000000eaf80000,0x00000000eb07f918,0x00000000eb080000)
  to   space 2048K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eaf80000)
 ParOldGen       total 330752K, used 330294K [0x00000000c0000000, 0x00000000d4300000, 0x00000000eab00000)
  object space 330752K, 99% used [0x00000000c0000000,0x00000000d428d8e0,0x00000000d4300000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.943 GC heap after
{Heap after GC invocations=1333 (full 4):
 PSYoungGen      total 3072K, used 160K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 512K, 31% used [0x00000000ead80000,0x00000000eada8000,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eb000000)
 ParOldGen       total 331776K, used 331345K [0x00000000c0000000, 0x00000000d4400000, 0x00000000eab00000)
  object space 331776K, 99% used [0x00000000c0000000,0x00000000d4394728,0x00000000d4400000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.965 GC heap before
{Heap before GC invocations=1334 (full 4):
 PSYoungGen      total 3072K, used 2720K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 512K, 31% used [0x00000000ead80000,0x00000000eada8000,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eb000000)
 ParOldGen       total 331776K, used 331345K [0x00000000c0000000, 0x00000000d4400000, 0x00000000eab00000)
  object space 331776K, 99% used [0x00000000c0000000,0x00000000d4394728,0x00000000d4400000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.970 GC heap after
{Heap after GC invocations=1334 (full 4):
 PSYoungGen      total 3072K, used 192K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 512K, 37% used [0x00000000eaf00000,0x00000000eaf30000,0x00000000eaf80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 331776K, used 331409K [0x00000000c0000000, 0x00000000d4400000, 0x00000000eab00000)
  object space 331776K, 99% used [0x00000000c0000000,0x00000000d43a4728,0x00000000d4400000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.989 GC heap before
{Heap before GC invocations=1335 (full 4):
 PSYoungGen      total 3072K, used 2752K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 512K, 37% used [0x00000000eaf00000,0x00000000eaf30000,0x00000000eaf80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 331776K, used 331409K [0x00000000c0000000, 0x00000000d4400000, 0x00000000eab00000)
  object space 331776K, 99% used [0x00000000c0000000,0x00000000d43a4728,0x00000000d4400000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 168.991 GC heap after
{Heap after GC invocations=1335 (full 4):
 PSYoungGen      total 3072K, used 192K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 512K, 37% used [0x00000000ead80000,0x00000000eadb0000,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eaf80000)
 ParOldGen       total 331776K, used 331473K [0x00000000c0000000, 0x00000000d4400000, 0x00000000eab00000)
  object space 331776K, 99% used [0x00000000c0000000,0x00000000d43b4728,0x00000000d4400000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 169.003 GC heap before
{Heap before GC invocations=1336 (full 4):
 PSYoungGen      total 3072K, used 2752K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 512K, 37% used [0x00000000ead80000,0x00000000eadb0000,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eaf80000)
 ParOldGen       total 331776K, used 331473K [0x00000000c0000000, 0x00000000d4400000, 0x00000000eab00000)
  object space 331776K, 99% used [0x00000000c0000000,0x00000000d43b4728,0x00000000d4400000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 169.005 GC heap after
{Heap after GC invocations=1336 (full 4):
 PSYoungGen      total 3584K, used 256K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eae00000)
  from space 512K, 50% used [0x00000000eaf00000,0x00000000eaf40000,0x00000000eaf80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 331776K, used 331537K [0x00000000c0000000, 0x00000000d4400000, 0x00000000eab00000)
  object space 331776K, 99% used [0x00000000c0000000,0x00000000d43c4728,0x00000000d4400000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 169.013 GC heap before
{Heap before GC invocations=1337 (full 4):
 PSYoungGen      total 3584K, used 3328K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 3072K, 100% used [0x00000000eab00000,0x00000000eae00000,0x00000000eae00000)
  from space 512K, 50% used [0x00000000eaf00000,0x00000000eaf40000,0x00000000eaf80000)
  to   space 512K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf00000)
 ParOldGen       total 331776K, used 331537K [0x00000000c0000000, 0x00000000d4400000, 0x00000000eab00000)
  object space 331776K, 99% used [0x00000000c0000000,0x00000000d43c4728,0x00000000d4400000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 170.023 GC heap after
{Heap after GC invocations=1337 (full 4):
 PSYoungGen      total 4096K, used 489K [0x00000000eab00000, 0x00000000eb300000, 0x0000000100000000)
  eden space 3584K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eae80000)
  from space 512K, 95% used [0x00000000eae80000,0x00000000eaefa758,0x00000000eaf00000)
  to   space 2048K, 0% used [0x00000000eb100000,0x00000000eb100000,0x00000000eb300000)
 ParOldGen       total 332800K, used 332585K [0x00000000c0000000, 0x00000000d4500000, 0x00000000eab00000)
  object space 332800K, 99% used [0x00000000c0000000,0x00000000d44ca650,0x00000000d4500000)
 Metaspace       used 76997K, committed 78656K, reserved 1179648K
  class space    used 7836K, committed 8576K, reserved 1048576K
}
Event: 170.071 GC heap before
{Heap before GC invocations=1338 (full 4):
 PSYoungGen      total 4096K, used 4073K [0x00000000eab00000, 0x00000000eb300000, 0x0000000100000000)
  eden space 3584K, 100% used [0x00000000eab00000,0x00000000eae80000,0x00000000eae80000)
  from space 512K, 95% used [0x00000000eae80000,0x00000000eaefa758,0x00000000eaf00000)
  to   space 2048K, 0% used [0x00000000eb100000,0x00000000eb100000,0x00000000eb300000)
 ParOldGen       total 332800K, used 332585K [0x00000000c0000000, 0x00000000d4500000, 0x00000000eab00000)
  object space 332800K, 99% used [0x00000000c0000000,0x00000000d44ca650,0x00000000d4500000)
 Metaspace       used 77008K, committed 78656K, reserved 1179648K
  class space    used 7838K, committed 8576K, reserved 1048576K
}
Event: 170.073 GC heap after
{Heap after GC invocations=1338 (full 4):
 PSYoungGen      total 4608K, used 519K [0x00000000eab00000, 0x00000000eb200000, 0x0000000100000000)
  eden space 3584K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eae80000)
  from space 1024K, 50% used [0x00000000eb100000,0x00000000eb181d30,0x00000000eb200000)
  to   space 1024K, 0% used [0x00000000eb000000,0x00000000eb000000,0x00000000eb100000)
 ParOldGen       total 333312K, used 333058K [0x00000000c0000000, 0x00000000d4580000, 0x00000000eab00000)
  object space 333312K, 99% used [0x00000000c0000000,0x00000000d4540800,0x00000000d4580000)
 Metaspace       used 77008K, committed 78656K, reserved 1179648K
  class space    used 7838K, committed 8576K, reserved 1048576K
}
Event: 171.186 GC heap before
{Heap before GC invocations=1339 (full 4):
 PSYoungGen      total 4608K, used 4103K [0x00000000eab00000, 0x00000000eb200000, 0x0000000100000000)
  eden space 3584K, 100% used [0x00000000eab00000,0x00000000eae80000,0x00000000eae80000)
  from space 1024K, 50% used [0x00000000eb100000,0x00000000eb181d30,0x00000000eb200000)
  to   space 1024K, 0% used [0x00000000eb000000,0x00000000eb000000,0x00000000eb100000)
 ParOldGen       total 333312K, used 333058K [0x00000000c0000000, 0x00000000d4580000, 0x00000000eab00000)
  object space 333312K, 99% used [0x00000000c0000000,0x00000000d4540800,0x00000000d4580000)
 Metaspace       used 77041K, committed 78656K, reserved 1179648K
  class space    used 7844K, committed 8576K, reserved 1048576K
}
Event: 171.188 GC heap after
{Heap after GC invocations=1339 (full 4):
 PSYoungGen      total 4608K, used 1002K [0x00000000eab00000, 0x00000000eb280000, 0x0000000100000000)
  eden space 3584K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eae80000)
  from space 1024K, 97% used [0x00000000eb000000,0x00000000eb0fa830,0x00000000eb100000)
  to   space 1536K, 0% used [0x00000000eb100000,0x00000000eb100000,0x00000000eb280000)
 ParOldGen       total 333824K, used 333541K [0x00000000c0000000, 0x00000000d4600000, 0x00000000eab00000)
  object space 333824K, 99% used [0x00000000c0000000,0x00000000d45b9710,0x00000000d4600000)
 Metaspace       used 77041K, committed 78656K, reserved 1179648K
  class space    used 7844K, committed 8576K, reserved 1048576K
}

Dll operation events (14 events):
Event: 0.019 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.165 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.221 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.233 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.236 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.241 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.302 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.515 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 3.250 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 7.065 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 7.073 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
Event: 7.570 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna5246896601022756585.dll
Event: 21.088 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll
Event: 21.395 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 170.176 Thread 0x000001df5ee9e6c0 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x000001df4f08f22c method=org.eclipse.jdt.internal.compiler.lookup.ReferenceBinding.compare([C[CII)I @ 46 c2
Event: 170.176 Thread 0x000001df5ee9e6c0 DEOPT PACKING pc=0x000001df4f08f22c sp=0x0000004fcd5fd940
Event: 170.176 Thread 0x000001df5ee9e6c0 DEOPT UNPACKING pc=0x000001df4de03aa2 sp=0x0000004fcd5fd8b0 mode 2
Event: 171.264 Thread 0x000001df5ee9e6c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001df4f215880 relative=0x0000000000001680
Event: 171.264 Thread 0x000001df5ee9e6c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001df4f215880 method=org.eclipse.jdt.internal.core.search.matching.TypeDeclarationPattern.createIndexKey(I[C[C[[CZ)[C @ 85 c2
Event: 171.264 Thread 0x000001df5ee9e6c0 DEOPT PACKING pc=0x000001df4f215880 sp=0x0000004fcd5fea90
Event: 171.264 Thread 0x000001df5ee9e6c0 DEOPT UNPACKING pc=0x000001df4de03aa2 sp=0x0000004fcd5fe9c8 mode 2
Event: 171.265 Thread 0x000001df5ee9e6c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001df4eb47ec0 relative=0x00000000000005c0
Event: 171.265 Thread 0x000001df5ee9e6c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001df4eb47ec0 method=java.util.Arrays.hashCode([C)I @ 8 c2
Event: 171.265 Thread 0x000001df5ee9e6c0 DEOPT PACKING pc=0x000001df4eb47ec0 sp=0x0000004fcd5fea90
Event: 171.265 Thread 0x000001df5ee9e6c0 DEOPT UNPACKING pc=0x000001df4de03aa2 sp=0x0000004fcd5fe950 mode 2
Event: 171.265 Thread 0x000001df5ee9e6c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001df4ecbe248 relative=0x0000000000002668
Event: 171.265 Thread 0x000001df5ee9e6c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001df4ecbe248 method=org.eclipse.jdt.internal.core.search.matching.SuperTypeReferencePattern.createIndexKey(I[C[C[[C[[CC[CC)[C @ 388 c2
Event: 171.265 Thread 0x000001df5ee9e6c0 DEOPT PACKING pc=0x000001df4ecbe248 sp=0x0000004fcd5fea80
Event: 171.265 Thread 0x000001df5ee9e6c0 DEOPT UNPACKING pc=0x000001df4de03aa2 sp=0x0000004fcd5fea20 mode 2
Event: 171.266 Thread 0x000001df5ee9e6c0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001df4ed7895c relative=0x000000000000057c
Event: 171.266 Thread 0x000001df5ee9e6c0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001df4ed7895c method=org.eclipse.jdt.internal.core.search.matching.TypeDeclarationPattern.createIndexKey(I[C[C[[CZ)[C @ 85 c2
Event: 171.266 Thread 0x000001df5ee9e6c0 DEOPT PACKING pc=0x000001df4ed7895c sp=0x0000004fcd5fea20
Event: 171.266 Thread 0x000001df5ee9e6c0 DEOPT UNPACKING pc=0x000001df4de03aa2 sp=0x0000004fcd5fe9c0 mode 2
Event: 171.326 Thread 0x000001df5ee9e6c0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001df4e93f57c relative=0x00000000000005bc

Classes loaded (20 events):
Event: 103.886 Loading class java/util/EnumMap$EntryIterator$Entry
Event: 103.886 Loading class java/util/EnumMap$EntryIterator$Entry done
Event: 104.054 Loading class java/util/stream/ReferencePipeline$4
Event: 104.054 Loading class java/util/stream/ReferencePipeline$4 done
Event: 104.055 Loading class java/util/stream/Nodes$IntFixedNodeBuilder
Event: 104.055 Loading class java/util/stream/Node$Builder$OfInt
Event: 104.055 Loading class java/util/stream/Node$Builder$OfInt done
Event: 104.055 Loading class java/util/stream/Nodes$IntArrayNode
Event: 104.055 Loading class java/util/stream/Nodes$IntArrayNode done
Event: 104.055 Loading class java/util/stream/Nodes$IntFixedNodeBuilder done
Event: 104.056 Loading class java/util/stream/ReferencePipeline$4$1
Event: 104.056 Loading class java/util/stream/ReferencePipeline$4$1 done
Event: 108.514 Loading class java/nio/BufferMismatch
Event: 108.514 Loading class java/nio/BufferMismatch done
Event: 155.134 Loading class sun/security/ssl/Alert
Event: 155.135 Loading class sun/security/ssl/Alert done
Event: 155.137 Loading class sun/security/ssl/Alert$AlertConsumer
Event: 155.137 Loading class sun/security/ssl/Alert$AlertConsumer done
Event: 155.137 Loading class sun/security/ssl/Alert$Level
Event: 155.138 Loading class sun/security/ssl/Alert$Level done

Classes unloaded (19 events):
Event: 5.997 Thread 0x000001df57a225f0 Unloading class 0x000001df011a4400 'java/lang/invoke/LambdaForm$MH+0x000001df011a4400'
Event: 5.997 Thread 0x000001df57a225f0 Unloading class 0x000001df011a4000 'java/lang/invoke/LambdaForm$MH+0x000001df011a4000'
Event: 5.997 Thread 0x000001df57a225f0 Unloading class 0x000001df011a3c00 'java/lang/invoke/LambdaForm$MH+0x000001df011a3c00'
Event: 5.997 Thread 0x000001df57a225f0 Unloading class 0x000001df011a3800 'java/lang/invoke/LambdaForm$MH+0x000001df011a3800'
Event: 5.997 Thread 0x000001df57a225f0 Unloading class 0x000001df011a3400 'java/lang/invoke/LambdaForm$BMH+0x000001df011a3400'
Event: 5.997 Thread 0x000001df57a225f0 Unloading class 0x000001df011a3000 'java/lang/invoke/LambdaForm$DMH+0x000001df011a3000'
Event: 5.997 Thread 0x000001df57a225f0 Unloading class 0x000001df011a1c00 'java/lang/invoke/LambdaForm$DMH+0x000001df011a1c00'
Event: 8.000 Thread 0x000001df57a225f0 Unloading class 0x000001df012b6400 'java/lang/invoke/LambdaForm$MH+0x000001df012b6400'
Event: 8.000 Thread 0x000001df57a225f0 Unloading class 0x000001df012b5800 'java/lang/invoke/LambdaForm$MH+0x000001df012b5800'
Event: 8.000 Thread 0x000001df57a225f0 Unloading class 0x000001df012b5000 'java/lang/invoke/LambdaForm$MH+0x000001df012b5000'
Event: 8.000 Thread 0x000001df57a225f0 Unloading class 0x000001df012b4800 'java/lang/invoke/LambdaForm$MH+0x000001df012b4800'
Event: 8.000 Thread 0x000001df57a225f0 Unloading class 0x000001df012b4400 'java/lang/invoke/LambdaForm$DMH+0x000001df012b4400'
Event: 8.000 Thread 0x000001df57a225f0 Unloading class 0x000001df012b3800 'java/lang/invoke/LambdaForm$MH+0x000001df012b3800'
Event: 80.032 Thread 0x000001df57a225f0 Unloading class 0x000001df0158d400 'java/lang/invoke/LambdaForm$DMH+0x000001df0158d400'
Event: 80.032 Thread 0x000001df57a225f0 Unloading class 0x000001df0158cc00 'java/lang/invoke/LambdaForm$DMH+0x000001df0158cc00'
Event: 80.032 Thread 0x000001df57a225f0 Unloading class 0x000001df0158d000 'java/lang/invoke/LambdaForm$DMH+0x000001df0158d000'
Event: 80.032 Thread 0x000001df57a225f0 Unloading class 0x000001df0158c800 'java/lang/invoke/LambdaForm$DMH+0x000001df0158c800'
Event: 153.395 Thread 0x000001df57a225f0 Unloading class 0x000001df01861000 'java/lang/invoke/LambdaForm$DMH+0x000001df01861000'
Event: 153.396 Thread 0x000001df57a225f0 Unloading class 0x000001df01860400 'java/lang/invoke/LambdaForm$DMH+0x000001df01860400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 163.965 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac66640}> (0x00000000eac66640) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 164.038 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae8a690}> (0x00000000eae8a690) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 164.068 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaea4bf8}> (0x00000000eaea4bf8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 164.195 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead58a48}> (0x00000000ead58a48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 164.621 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead29fe0}> (0x00000000ead29fe0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 164.966 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaea3488}> (0x00000000eaea3488) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 165.206 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac4a0d0}> (0x00000000eac4a0d0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 165.344 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eadfef18}> (0x00000000eadfef18) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 165.830 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac859d8}> (0x00000000eac859d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 166.050 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eae2e010}> (0x00000000eae2e010) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 166.142 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead7f888}> (0x00000000ead7f888) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 166.482 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000ead3a6b0}> (0x00000000ead3a6b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 166.622 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eadd1a80}> (0x00000000eadd1a80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 166.639 Thread 0x000001df5ee9e6c0 Exception <a 'java/io/FileNotFoundException'{0x00000000eae41f10}> (0x00000000eae41f10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 166.644 Thread 0x000001df5ee9e6c0 Exception <a 'java/io/FileNotFoundException'{0x00000000eae532c8}> (0x00000000eae532c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 167.498 Thread 0x000001df5ee9e6c0 Implicit null exception at 0x000001df4f201584 to 0x000001df4f2033ec
Event: 167.991 Thread 0x000001df5ee9e6c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eac57f88}> (0x00000000eac57f88) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 168.438 Thread 0x000001df5ee9e6c0 Implicit null exception at 0x000001df4f1d46a6 to 0x000001df4f1d4834
Event: 168.439 Thread 0x000001df5ee9e6c0 Implicit null exception at 0x000001df4f168cb3 to 0x000001df4f16b5d8
Event: 168.772 Thread 0x000001df5ee9e6c0 Implicit null exception at 0x000001df4eee9a03 to 0x000001df4eee9d1c

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 168.859 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 168.925 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 168.928 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 168.940 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 168.943 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 168.965 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 168.970 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 168.989 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 168.991 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 169.003 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 169.005 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 169.013 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 170.023 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 170.071 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 170.073 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 170.207 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 171.188 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 171.225 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 171.225 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 172.238 Executing VM operation: Cleanup

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 154.291 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f49d90
Event: 154.291 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f4ac90
Event: 154.291 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f4cb90
Event: 154.291 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f4d790
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f52790
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f53890
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f54c10
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f55690
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f58a90
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f59e10
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f5c210
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47f60a90
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47fc3d10
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47fc4510
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47fc4f10
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47fc6110
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47fc6c90
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47fc9390
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df47fcf790
Event: 154.292 Thread 0x000001df57a225f0 flushing  nmethod 0x000001df48019210

Events (20 events):
Event: 113.161 Thread 0x000001df62ad1a10 Thread exited: 0x000001df62ad1a10
Event: 119.618 Thread 0x000001df57a6a4d0 Thread added: 0x000001df62ad1a10
Event: 119.618 Thread 0x000001df57a6a4d0 Thread added: 0x000001df61158020
Event: 120.357 Thread 0x000001df61158020 Thread exited: 0x000001df61158020
Event: 120.357 Thread 0x000001df62ad1a10 Thread exited: 0x000001df62ad1a10
Event: 121.253 Thread 0x000001df57a6a4d0 Thread added: 0x000001df60d15df0
Event: 121.628 Thread 0x000001df57a52550 Thread added: 0x000001df6b98b910
Event: 121.996 Thread 0x000001df6b98b910 Thread exited: 0x000001df6b98b910
Event: 123.706 Thread 0x000001df60d15df0 Thread exited: 0x000001df60d15df0
Event: 132.663 Thread 0x000001df57a6a4d0 Thread added: 0x000001df6b98b910
Event: 134.420 Thread 0x000001df6b98b910 Thread exited: 0x000001df6b98b910
Event: 162.180 Thread 0x000001df5e57b700 Thread exited: 0x000001df5e57b700
Event: 164.667 Thread 0x000001df60d5bd50 Thread exited: 0x000001df60d5bd50
Event: 166.875 Thread 0x000001df5ee9cc80 Thread exited: 0x000001df5ee9cc80
Event: 168.480 Thread 0x000001df57a6a4d0 Thread added: 0x000001df6b98b910
Event: 170.038 Thread 0x000001df6b98b910 Thread exited: 0x000001df6b98b910
Event: 170.061 Thread 0x000001df61c54100 Thread added: 0x000001df61c56ef0
Event: 170.074 Thread 0x000001df61c52030 Thread added: 0x000001df61c53a70
Event: 171.188 Thread 0x000001df57a6a4d0 Thread added: 0x000001df6b98b910
Event: 171.188 Thread 0x000001df57a6a4d0 Thread added: 0x000001df5f4fda60


Dynamic libraries:
0x00007ff70c070000 - 0x00007ff70c07e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe0b1b0000 - 0x00007ffe0b405000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe0a820000 - 0x00007ffe0a8e7000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe088b0000 - 0x00007ffe08c50000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe08760000 - 0x00007ffe088aa000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe02010000 - 0x00007ffe0202e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe01b30000 - 0x00007ffe01b48000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffdebb00000 - 0x00007ffdebd90000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076\COMCTL32.dll
0x00007ffe0a2e0000 - 0x00007ffe0a49f000 	C:\Windows\System32\USER32.dll
0x00007ffe09b90000 - 0x00007ffe09c39000 	C:\Windows\System32\msvcrt.dll
0x00007ffe08730000 - 0x00007ffe08757000 	C:\Windows\System32\win32u.dll
0x00007ffe09840000 - 0x00007ffe0986a000 	C:\Windows\System32\GDI32.dll
0x00007ffe08600000 - 0x00007ffe08721000 	C:\Windows\System32\gdi32full.dll
0x00007ffe08c60000 - 0x00007ffe08d03000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe0a570000 - 0x00007ffe0a59f000 	C:\Windows\System32\IMM32.DLL
0x00007ffe020d0000 - 0x00007ffe020dc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffdeacb0000 - 0x00007ffdead3d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffd97fb0000 - 0x00007ffd98d40000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe0a4b0000 - 0x00007ffe0a562000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe09fd0000 - 0x00007ffe0a076000 	C:\Windows\System32\sechost.dll
0x00007ffe098f0000 - 0x00007ffe09a09000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe09870000 - 0x00007ffe098e4000 	C:\Windows\System32\WS2_32.dll
0x00007ffe01030000 - 0x00007ffe0103b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffded780000 - 0x00007ffded7b6000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe07e80000 - 0x00007ffe07ece000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffe07e60000 - 0x00007ffe07e74000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe074a0000 - 0x00007ffe074ba000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe02000000 - 0x00007ffe0200a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffdec8b0000 - 0x00007ffdecaf1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe0a8f0000 - 0x00007ffe0ac65000 	C:\Windows\System32\combase.dll
0x00007ffe0a6a0000 - 0x00007ffe0a776000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdef220000 - 0x00007ffdef259000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe08fd0000 - 0x00007ffe09069000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe01b90000 - 0x00007ffe01b9f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffdf6f10000 - 0x00007ffdf6f2f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe09150000 - 0x00007ffe09836000 	C:\Windows\System32\SHELL32.dll
0x00007ffe06430000 - 0x00007ffe06c5b000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffe09a20000 - 0x00007ffe09af3000 	C:\Windows\System32\SHCORE.dll
0x00007ffe09070000 - 0x00007ffe090cd000 	C:\Windows\System32\shlwapi.dll
0x00007ffe08520000 - 0x00007ffe08544000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdf6ef0000 - 0x00007ffdf6f08000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffdfa0f0000 - 0x00007ffdfa100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe05e30000 - 0x00007ffe05f4d000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffe07940000 - 0x00007ffe079a8000 	C:\Windows\system32\mswsock.dll
0x00007ffdeae20000 - 0x00007ffdeae36000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffdf7ba0000 - 0x00007ffdf7bb0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffde9ba0000 - 0x00007ffde9be5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffe0a140000 - 0x00007ffe0a2d5000 	C:\Windows\System32\ole32.dll
0x00007ffe07ce0000 - 0x00007ffe07cfc000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffe07400000 - 0x00007ffe07438000 	C:\Windows\system32\rsaenh.dll
0x00007ffe079e0000 - 0x00007ffe07a0b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffe07f70000 - 0x00007ffe07f96000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffe07b60000 - 0x00007ffe07b6c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe06ee0000 - 0x00007ffe06f10000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffe0a130000 - 0x00007ffe0a13a000 	C:\Windows\System32\NSI.dll
0x00007ffdec4e0000 - 0x00007ffdec4ea000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ffdeab40000 - 0x00007ffdeab4b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ffe0a810000 - 0x00007ffe0a818000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdd3780000 - 0x00007ffdd37c9000 	C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna5246896601022756585.dll
0x00007ffe003e0000 - 0x00007ffe003fc000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffe05af0000 - 0x00007ffe05b12000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffe06f60000 - 0x00007ffe0707b000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffe06c60000 - 0x00007ffe06c6b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffe05b50000 - 0x00007ffe05bd5000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffe020f0000 - 0x00007ffe020f9000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll
0x00007ffe03490000 - 0x00007ffe0349e000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
0x00007ffe08dd0000 - 0x00007ffe08f46000 	C:\Windows\System32\CRYPT32.dll
0x00007ffe07f30000 - 0x00007ffe07f60000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffe07ee0000 - 0x00007ffe07f1f000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffe05fb0000 - 0x00007ffe05fe5000 	C:\Windows\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-63116079

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-4a6d688438150d7a4e656f3ed6a5a7e9-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.1)
OS uptime: 0 days 3:27 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 1992, Current Mhz: 1792, Mhz Limit: 1792

Memory: 4k page, system-wide physical 8066M (717M free)
TotalPageFile size 14210M (AvailPageFile size 3M)
current process WorkingSet (physical memory assigned to process): 638M, peak: 934M
current process commit charge ("private bytes"): 646M, peak: 1008M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
