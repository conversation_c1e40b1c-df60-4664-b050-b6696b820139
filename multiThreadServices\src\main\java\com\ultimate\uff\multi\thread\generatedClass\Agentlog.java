package com.ultimate.uff.multi.thread.generatedClass;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Agentlog extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String serviceId;
    private final FieldValue<String> serviceIdVal = new FieldValue<>(null);
    private String agentId;
    private final FieldValue<String> agentIdVal = new FieldValue<>(null);
    private String action;
    private final FieldValue<String> actionVal = new FieldValue<>(null);
    private String message;
    private final FieldValue<String> messageVal = new FieldValue<>(null);
    private String timestamp;
    private final FieldValue<String> timestampVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private List<String> recordAuthorizer = new ArrayList<>();
    private final FieldValue<List<String>> recordAuthorizerVal = new FieldValue<>(new ArrayList<>());
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public Agentlog() {}

    public Agentlog(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object serviceIdObj = recordDetail.get("serviceId");
        if (serviceIdObj != null) {
            this.setServiceId(serviceIdObj.toString());
        }
        Object agentIdObj = recordDetail.get("agentId");
        if (agentIdObj != null) {
            this.setAgentId(agentIdObj.toString());
        }
        Object actionObj = recordDetail.get("action");
        if (actionObj != null) {
            this.setAction(actionObj.toString());
        }
        Object messageObj = recordDetail.get("message");
        if (messageObj != null) {
            this.setMessage(messageObj.toString());
        }
        Object timestampObj = recordDetail.get("timestamp");
        if (timestampObj != null) {
            this.setTimestamp(timestampObj.toString());
        }
        Object IDObj = recordDetail.get("ID");
        if (IDObj != null) {
            this.setID(IDObj.toString());
        }
        Object recordStatusObj = recordDetail.get("recordStatus");
        if (recordStatusObj != null) {
            this.setRecordStatus(recordStatusObj.toString());
        }
        Object recordInputterObj = recordDetail.get("recordInputter");
        if (recordInputterObj != null) {
            this.setRecordInputter(recordInputterObj.toString());
        }
        Object listObj = recordDetail.get("recordAuthorizer");
        if (listObj instanceof List<?>) {
            this.setRecordAuthorizer((List<String>) listObj);
        }
        Object recordCountObj = recordDetail.get("recordCount");
        if (recordCountObj != null) {
            this.setRecordCount(recordCountObj.toString());
        }
        Object dateTimeObj = recordDetail.get("dateTime");
        if (dateTimeObj != null) {
            this.setDateTime(dateTimeObj.toString());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (serviceId != null) map.put("serviceId", serviceId);
        if (agentId != null) map.put("agentId", agentId);
        if (action != null) map.put("action", action);
        if (message != null) map.put("message", message);
        if (timestamp != null) map.put("timestamp", timestamp);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "serviceId", "agentId", "action", "message", "timestamp", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
        this.serviceIdVal.setValue(serviceId);
    }
    public String getServiceId() { return this.serviceId; }
    public FieldValue<String> serviceIdVal() { return serviceIdVal; }
    public void setAgentId(String agentId) {
        this.agentId = agentId;
        this.agentIdVal.setValue(agentId);
    }
    public String getAgentId() { return this.agentId; }
    public FieldValue<String> agentIdVal() { return agentIdVal; }
    public void setAction(String action) {
        this.action = action;
        this.actionVal.setValue(action);
    }
    public String getAction() { return this.action; }
    public FieldValue<String> actionVal() { return actionVal; }
    public void setMessage(String message) {
        this.message = message;
        this.messageVal.setValue(message);
    }
    public String getMessage() { return this.message; }
    public FieldValue<String> messageVal() { return messageVal; }
    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
        this.timestampVal.setValue(timestamp);
    }
    public String getTimestamp() { return this.timestamp; }
    public FieldValue<String> timestampVal() { return timestampVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(List<String> recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public List<String> getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<List<String>> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}
