#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1659872 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=12832, tid=20324
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-8154ffe81a4f693c8417fe24f69e4c8e-sock

Host: Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz, 8 cores, 7G,  Windows 11 , 64 bit Build 26100 (10.0.26100.1)
Time: Tue May 27 14:27:37 2025 Egypt Daylight Time elapsed time: 28.970422 seconds (0d 0h 0m 28s)

---------------  T H R E A D  ---------------

Current thread (0x0000020985795270):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=20324, stack(0x0000002231600000,0x0000002231700000) (1024K)]


Current CompileTask:
C2:28970 13545 %     4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractReferenceFromConstantPool @ 155 (358 bytes)

Stack: [0x0000002231600000,0x0000002231700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x43340]
C  [KERNEL32.DLL+0x31fd7]
C  [ntdll.dll+0x6d7d0]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000020989687c90, length=49, elements={
0x00000209a9cc0e90, 0x00000209ff1c8870, 0x00000209ff1cb000, 0x00000209ff1ccd00,
0x00000209ff1cd950, 0x00000209ff1d1c00, 0x00000209ff1d5e10, 0x00000209ff1d6ea0,
0x00000209ff1db680, 0x00000209a9d2bc20, 0x0000020980dc4120, 0x000002098580f3f0,
0x00000209854d69e0, 0x00000209859fc250, 0x0000020985cea180, 0x00000209860f0680,
0x00000209863f5710, 0x0000020985f48690, 0x0000020985f48000, 0x0000020985f458a0,
0x0000020985f45f30, 0x0000020985f465c0, 0x0000020985f472e0, 0x0000020985f47970,
0x0000020985f48d20, 0x0000020985f46c50, 0x0000020986fe6f60, 0x0000020986fe7c80,
0x0000020986fe5bb0, 0x0000020986fe6240, 0x0000020986fe5520, 0x0000020986fe8310,
0x0000020986fe68d0, 0x0000020986fe4e90, 0x0000020986fe75f0, 0x00000209875b3040,
0x00000209875b5110, 0x00000209875b29b0, 0x00000209875b43f0, 0x00000209875b4a80,
0x00000209875b2320, 0x0000020985795270, 0x00000209875b3d60, 0x00000209875b9940,
0x00000209875b7f00, 0x00000209875b92b0, 0x00000209875b8590, 0x0000020988470f40,
0x000002098846e7e0
}

Java Threads: ( => current thread )
  0x00000209a9cc0e90 JavaThread "main"                              [_thread_blocked, id=31368, stack(0x0000002230900000,0x0000002230a00000) (1024K)]
  0x00000209ff1c8870 JavaThread "Reference Handler"          daemon [_thread_blocked, id=27352, stack(0x0000002230d00000,0x0000002230e00000) (1024K)]
  0x00000209ff1cb000 JavaThread "Finalizer"                  daemon [_thread_blocked, id=23784, stack(0x0000002230e00000,0x0000002230f00000) (1024K)]
  0x00000209ff1ccd00 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=21156, stack(0x0000002230f00000,0x0000002231000000) (1024K)]
  0x00000209ff1cd950 JavaThread "Attach Listener"            daemon [_thread_blocked, id=14396, stack(0x0000002231000000,0x0000002231100000) (1024K)]
  0x00000209ff1d1c00 JavaThread "Service Thread"             daemon [_thread_blocked, id=15652, stack(0x0000002231100000,0x0000002231200000) (1024K)]
  0x00000209ff1d5e10 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=12280, stack(0x0000002231200000,0x0000002231300000) (1024K)]
  0x00000209ff1d6ea0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=30040, stack(0x0000002231300000,0x0000002231400000) (1024K)]
  0x00000209ff1db680 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=12164, stack(0x0000002231400000,0x0000002231500000) (1024K)]
  0x00000209a9d2bc20 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=23588, stack(0x0000002231500000,0x0000002231600000) (1024K)]
  0x0000020980dc4120 JavaThread "Notification Thread"        daemon [_thread_blocked, id=29000, stack(0x0000002231700000,0x0000002231800000) (1024K)]
  0x000002098580f3f0 JavaThread "Active Thread: Equinox Container: 726115e8-395e-493c-b822-197eb6d8bfe4"        [_thread_blocked, id=30652, stack(0x0000002231f00000,0x0000002232000000) (1024K)]
  0x00000209854d69e0 JavaThread "Refresh Thread: Equinox Container: 726115e8-395e-493c-b822-197eb6d8bfe4" daemon [_thread_blocked, id=24900, stack(0x0000002232000000,0x0000002232100000) (1024K)]
  0x00000209859fc250 JavaThread "Framework Event Dispatcher: Equinox Container: 726115e8-395e-493c-b822-197eb6d8bfe4" daemon [_thread_blocked, id=32828, stack(0x0000002232200000,0x0000002232300000) (1024K)]
  0x0000020985cea180 JavaThread "Start Level: Equinox Container: 726115e8-395e-493c-b822-197eb6d8bfe4" daemon [_thread_blocked, id=30548, stack(0x0000002232300000,0x0000002232400000) (1024K)]
  0x00000209860f0680 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=33100, stack(0x0000002232400000,0x0000002232500000) (1024K)]
  0x00000209863f5710 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=29148, stack(0x0000002232500000,0x0000002232600000) (1024K)]
  0x0000020985f48690 JavaThread "Worker-JM"                         [_thread_blocked, id=32020, stack(0x0000002232600000,0x0000002232700000) (1024K)]
  0x0000020985f48000 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=30396, stack(0x0000002232700000,0x0000002232800000) (1024K)]
  0x0000020985f458a0 JavaThread "Worker-0: Updating workspace"        [_thread_blocked, id=30572, stack(0x0000002232800000,0x0000002232900000) (1024K)]
  0x0000020985f45f30 JavaThread "Worker-1: Initialize Workspace"        [_thread_blocked, id=21748, stack(0x0000002232900000,0x0000002232a00000) (1024K)]
  0x0000020985f465c0 JavaThread "Java indexing"              daemon [_thread_blocked, id=20208, stack(0x0000002232b00000,0x0000002232c00000) (1024K)]
  0x0000020985f472e0 JavaThread "Worker-2: Initialize workspace"        [_thread_blocked, id=31028, stack(0x0000002232f00000,0x0000002233000000) (1024K)]
  0x0000020985f47970 JavaThread "Worker-3: Java indexing... "        [_thread_blocked, id=25168, stack(0x0000002233000000,0x0000002233100000) (1024K)]
  0x0000020985f48d20 JavaThread "Worker-4"                          [_thread_blocked, id=1668, stack(0x0000002233100000,0x0000002233200000) (1024K)]
  0x0000020985f46c50 JavaThread "Thread-2"                   daemon [_thread_in_native, id=33568, stack(0x0000002233200000,0x0000002233300000) (1024K)]
  0x0000020986fe6f60 JavaThread "Thread-3"                   daemon [_thread_in_native, id=31112, stack(0x0000002233300000,0x0000002233400000) (1024K)]
  0x0000020986fe7c80 JavaThread "Thread-4"                   daemon [_thread_in_native, id=25508, stack(0x0000002233400000,0x0000002233500000) (1024K)]
  0x0000020986fe5bb0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=17896, stack(0x0000002233500000,0x0000002233600000) (1024K)]
  0x0000020986fe6240 JavaThread "Thread-6"                   daemon [_thread_in_native, id=30032, stack(0x0000002233600000,0x0000002233700000) (1024K)]
  0x0000020986fe5520 JavaThread "Thread-7"                   daemon [_thread_in_native, id=22984, stack(0x0000002233700000,0x0000002233800000) (1024K)]
  0x0000020986fe8310 JavaThread "Thread-8"                   daemon [_thread_in_native, id=17952, stack(0x0000002233800000,0x0000002233900000) (1024K)]
  0x0000020986fe68d0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=30412, stack(0x0000002233900000,0x0000002233a00000) (1024K)]
  0x0000020986fe4e90 JavaThread "Thread-10"                  daemon [_thread_in_native, id=13232, stack(0x0000002233a00000,0x0000002233b00000) (1024K)]
  0x0000020986fe75f0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=35832, stack(0x0000002233b00000,0x0000002233c00000) (1024K)]
  0x00000209875b3040 JavaThread "Worker-5: Building"                [_thread_blocked, id=32728, stack(0x0000002233c00000,0x0000002233d00000) (1024K)]
  0x00000209875b5110 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=25596, stack(0x0000002233d00000,0x0000002233e00000) (1024K)]
  0x00000209875b29b0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=15364, stack(0x0000002233e00000,0x0000002233f00000) (1024K)]
  0x00000209875b43f0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=27688, stack(0x0000002231800000,0x0000002231900000) (1024K)]
  0x00000209875b4a80 JavaThread "Compiler Processing Task"   daemon [_thread_blocked, id=26776, stack(0x0000002233f00000,0x0000002234000000) (1024K)]
  0x00000209875b2320 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=2788, stack(0x0000002234000000,0x0000002234100000) (1024K)]
=>0x0000020985795270 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=20324, stack(0x0000002231600000,0x0000002231700000) (1024K)]
  0x00000209875b3d60 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=5932, stack(0x0000002232a00000,0x0000002232b00000) (1024K)]
  0x00000209875b9940 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=31424, stack(0x0000002234100000,0x0000002234200000) (1024K)]
  0x00000209875b7f00 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=31844, stack(0x0000002234200000,0x0000002234300000) (1024K)]
  0x00000209875b92b0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=28888, stack(0x0000002234300000,0x0000002234400000) (1024K)]
  0x00000209875b8590 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=28796, stack(0x0000002234400000,0x0000002234500000) (1024K)]
  0x0000020988470f40 JavaThread "Compiler Class File Writer" daemon [_thread_blocked, id=30312, stack(0x0000002234500000,0x0000002234600000) (1024K)]
  0x000002098846e7e0 JavaThread "Worker-6"                          [_thread_blocked, id=19072, stack(0x0000002234b00000,0x0000002234c00000) (1024K)]
Total: 49

Other Threads:
  0x00000209a791e9f0 VMThread "VM Thread"                           [id=29904, stack(0x0000002230c00000,0x0000002230d00000) (1024K)]
  0x00000209ff0dca00 WatcherThread "VM Periodic Task Thread"        [id=14968, stack(0x0000002230b00000,0x0000002230c00000) (1024K)]
  0x00000209a9ce05d0 WorkerThread "GC Thread#0"                     [id=33076, stack(0x0000002230a00000,0x0000002230b00000) (1024K)]
  0x000002098542a970 WorkerThread "GC Thread#1"                     [id=11564, stack(0x0000002231900000,0x0000002231a00000) (1024K)]
  0x000002098542ad10 WorkerThread "GC Thread#2"                     [id=13472, stack(0x0000002231a00000,0x0000002231b00000) (1024K)]
  0x000002098542b0b0 WorkerThread "GC Thread#3"                     [id=26216, stack(0x0000002231b00000,0x0000002231c00000) (1024K)]
  0x00000209853c2880 WorkerThread "GC Thread#4"                     [id=28312, stack(0x0000002231c00000,0x0000002231d00000) (1024K)]
  0x00000209853c2c20 WorkerThread "GC Thread#5"                     [id=26396, stack(0x0000002231d00000,0x0000002231e00000) (1024K)]
  0x00000209853c2fc0 WorkerThread "GC Thread#6"                     [id=28428, stack(0x0000002231e00000,0x0000002231f00000) (1024K)]
  0x00000209854d7040 WorkerThread "GC Thread#7"                     [id=8976, stack(0x0000002232100000,0x0000002232200000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  29017 13408   !   4       org.eclipse.osgi.internal.loader.classpath.ClasspathManager::defineClass (636 bytes)
C2 CompilerThread1  29017 13545 %     4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractReferenceFromConstantPool @ 155 (358 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000209be000000-0x00000209beba0000-0x00000209beba0000), size 12189696, SharedBaseAddress: 0x00000209be000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000209bf000000-0x00000209ff000000, reserved size: 1073741824
Narrow klass base: 0x00000209be000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 8066M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 4096K, used 696K [0x00000000eab00000, 0x00000000eb100000, 0x0000000100000000)
  eden space 3072K, 4% used [0x00000000eab00000,0x00000000eab20430,0x00000000eae00000)
  from space 1024K, 55% used [0x00000000eae00000,0x00000000eae8ded8,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000eb000000,0x00000000eb000000,0x00000000eb100000)
 ParOldGen       total 430080K, used 429698K [0x00000000c0000000, 0x00000000da400000, 0x00000000eab00000)
  object space 430080K, 99% used [0x00000000c0000000,0x00000000da3a0940,0x00000000da400000)
 Metaspace       used 62747K, committed 64256K, reserved 1114112K
  class space    used 6428K, committed 7104K, reserved 1048576K

Card table byte_map: [0x00000209a9660000,0x00000209a9870000] _byte_map_base: 0x00000209a9060000

Marking Bits: (ParMarkBitMap*) 0x00007ffd98c631f0
 Begin Bits: [0x00000209bbeb0000, 0x00000209bceb0000)
 End Bits:   [0x00000209bceb0000, 0x00000209bdeb0000)

Polling page: 0x00000209a9290000

Metaspace:

Usage:
  Non-class:     55.00 MB used.
      Class:      6.28 MB used.
       Both:     61.28 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      55.81 MB ( 87%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       6.94 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      62.75 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  7.95 MB
       Class:  9.10 MB
        Both:  17.05 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1216.
num_arena_deaths: 16.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1004.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 25.
num_chunks_taken_from_freelist: 4222.
num_chunk_merges: 15.
num_chunk_splits: 2559.
num_chunks_enlarged: 1406.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=9430Kb max_used=9430Kb free=110569Kb
 bounds [0x00000209b4980000, 0x00000209b52c0000, 0x00000209bbeb0000]
CodeHeap 'profiled nmethods': size=120000Kb used=29211Kb max_used=29211Kb free=90788Kb
 bounds [0x00000209aceb0000, 0x00000209aeb40000, 0x00000209b43e0000]
CodeHeap 'non-nmethods': size=5760Kb used=1432Kb max_used=1518Kb free=4327Kb
 bounds [0x00000209b43e0000, 0x00000209b4650000, 0x00000209b4980000]
 total_blobs=13578 nmethods=12837 adapters=646
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 27.908 Thread 0x00000209ff1db680 nmethod 13681 0x00000209aeb2f410 code [0x00000209aeb2f5a0, 0x00000209aeb2f6c0]
Event: 27.911 Thread 0x00000209ff1db680 13682       2       org.xml.sax.helpers.DefaultHandler::characters (1 bytes)
Event: 27.911 Thread 0x00000209ff1db680 nmethod 13682 0x00000209aeb2f790 code [0x00000209aeb2f920, 0x00000209aeb2fa28]
Event: 27.914 Thread 0x00000209ff1db680 13684       1       com.sun.org.apache.xerces.internal.impl.validation.ValidationState::setUsingNamespaces (6 bytes)
Event: 27.914 Thread 0x00000209ff1db680 nmethod 13684 0x00000209b52b5210 code [0x00000209b52b53a0, 0x00000209b52b5470]
Event: 27.915 Thread 0x00000209ff1db680 13685       2       java.util.ImmutableCollections$ListN::toArray (13 bytes)
Event: 27.915 Thread 0x00000209ff1db680 nmethod 13685 0x00000209aeb2fa90 code [0x00000209aeb2fc40, 0x00000209aeb2fd88]
Event: 27.917 Thread 0x00000209ff1db680 13686       2       com.sun.org.apache.xerces.internal.impl.XMLErrorReporter::getMessageFormatter (14 bytes)
Event: 27.917 Thread 0x00000209ff1db680 nmethod 13686 0x00000209aeb2fe90 code [0x00000209aeb30040, 0x00000209aeb301d0]
Event: 27.942 Thread 0x00000209ff1db680 13687       1       org.eclipse.core.internal.content.ContentTypeMatcher::getContext (5 bytes)
Event: 27.943 Thread 0x00000209ff1db680 nmethod 13687 0x00000209b52b5510 code [0x00000209b52b56a0, 0x00000209b52b5768]
Event: 27.949 Thread 0x00000209ff1db680 13688       2       org.eclipse.core.internal.content.ContentType::hashCode (8 bytes)
Event: 27.950 Thread 0x00000209ff1db680 nmethod 13688 0x00000209aeb30290 code [0x00000209aeb30440, 0x00000209aeb30568]
Event: 27.950 Thread 0x00000209ff1db680 13689       2       org.eclipse.jface.text.AbstractLineTracker$1::nextDelimiterInfo (10 bytes)
Event: 27.951 Thread 0x00000209ff1db680 nmethod 13689 0x00000209aeb30610 code [0x00000209aeb307c0, 0x00000209aeb308f0]
Event: 27.951 Thread 0x00000209ff1db680 13690       2       org.eclipse.jface.text.Line::<init> (24 bytes)
Event: 27.951 Thread 0x00000209ff1db680 nmethod 13690 0x00000209aeb30a10 code [0x00000209aeb30ba0, 0x00000209aeb30cd0]
Event: 28.019 Thread 0x00000209ff1db680 13691       2       com.google.gson.stream.JsonWriter::openScope (19 bytes)
Event: 28.020 Thread 0x00000209ff1db680 nmethod 13691 0x00000209aeb30d90 code [0x00000209aeb30f60, 0x00000209aeb31108]
Event: 28.020 Thread 0x00000209ff1db680 13692       2       com.google.gson.stream.JsonWriter::closeScope (95 bytes)

GC Heap History (20 events):
Event: 27.744 GC heap before
{Heap before GC invocations=686 (full 3):
 PSYoungGen      total 2560K, used 2496K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 87% used [0x00000000eae00000,0x00000000eae70000,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 424960K, used 424643K [0x00000000c0000000, 0x00000000d9f00000, 0x00000000eab00000)
  object space 424960K, 99% used [0x00000000c0000000,0x00000000d9eb0e90,0x00000000d9f00000)
 Metaspace       used 62620K, committed 64128K, reserved 1114112K
  class space    used 6415K, committed 7104K, reserved 1048576K
}
Event: 27.747 GC heap after
{Heap after GC invocations=686 (full 3):
 PSYoungGen      total 2560K, used 468K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 91% used [0x00000000ead80000,0x00000000eadf5068,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 424960K, used 424945K [0x00000000c0000000, 0x00000000d9f00000, 0x00000000eab00000)
  object space 424960K, 99% used [0x00000000c0000000,0x00000000d9efc610,0x00000000d9f00000)
 Metaspace       used 62620K, committed 64128K, reserved 1114112K
  class space    used 6415K, committed 7104K, reserved 1048576K
}
Event: 27.762 GC heap before
{Heap before GC invocations=687 (full 3):
 PSYoungGen      total 2560K, used 2502K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 99% used [0x00000000eab00000,0x00000000eacfc8d0,0x00000000ead00000)
  from space 512K, 91% used [0x00000000ead80000,0x00000000eadf5068,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 424960K, used 424945K [0x00000000c0000000, 0x00000000d9f00000, 0x00000000eab00000)
  object space 424960K, 99% used [0x00000000c0000000,0x00000000d9efc610,0x00000000d9f00000)
 Metaspace       used 62622K, committed 64128K, reserved 1114112K
  class space    used 6415K, committed 7104K, reserved 1048576K
}
Event: 27.765 GC heap after
{Heap after GC invocations=687 (full 3):
 PSYoungGen      total 2560K, used 416K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 81% used [0x00000000eae00000,0x00000000eae68000,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 425472K, used 425229K [0x00000000c0000000, 0x00000000d9f80000, 0x00000000eab00000)
  object space 425472K, 99% used [0x00000000c0000000,0x00000000d9f43780,0x00000000d9f80000)
 Metaspace       used 62622K, committed 64128K, reserved 1114112K
  class space    used 6415K, committed 7104K, reserved 1048576K
}
Event: 27.782 GC heap before
{Heap before GC invocations=688 (full 3):
 PSYoungGen      total 2560K, used 2464K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 81% used [0x00000000eae00000,0x00000000eae68000,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 425472K, used 425229K [0x00000000c0000000, 0x00000000d9f80000, 0x00000000eab00000)
  object space 425472K, 99% used [0x00000000c0000000,0x00000000d9f43780,0x00000000d9f80000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.785 GC heap after
{Heap after GC invocations=688 (full 3):
 PSYoungGen      total 2560K, used 352K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 68% used [0x00000000ead80000,0x00000000eadd8000,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 425984K, used 425517K [0x00000000c0000000, 0x00000000da000000, 0x00000000eab00000)
  object space 425984K, 99% used [0x00000000c0000000,0x00000000d9f8b780,0x00000000da000000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.801 GC heap before
{Heap before GC invocations=689 (full 3):
 PSYoungGen      total 2560K, used 2393K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 99% used [0x00000000eab00000,0x00000000eacfe4e0,0x00000000ead00000)
  from space 512K, 68% used [0x00000000ead80000,0x00000000eadd8000,0x00000000eae00000)
  to   space 512K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eae80000)
 ParOldGen       total 425984K, used 425517K [0x00000000c0000000, 0x00000000da000000, 0x00000000eab00000)
  object space 425984K, 99% used [0x00000000c0000000,0x00000000d9f8b780,0x00000000da000000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.804 GC heap after
{Heap after GC invocations=689 (full 3):
 PSYoungGen      total 2560K, used 416K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 81% used [0x00000000eae00000,0x00000000eae68000,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 425984K, used 425813K [0x00000000c0000000, 0x00000000da000000, 0x00000000eab00000)
  object space 425984K, 99% used [0x00000000c0000000,0x00000000d9fd5780,0x00000000da000000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.817 GC heap before
{Heap before GC invocations=690 (full 3):
 PSYoungGen      total 2560K, used 2463K [0x00000000eab00000, 0x00000000eae80000, 0x0000000100000000)
  eden space 2048K, 99% used [0x00000000eab00000,0x00000000eacfff88,0x00000000ead00000)
  from space 512K, 81% used [0x00000000eae00000,0x00000000eae68000,0x00000000eae80000)
  to   space 512K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae00000)
 ParOldGen       total 425984K, used 425813K [0x00000000c0000000, 0x00000000da000000, 0x00000000eab00000)
  object space 425984K, 99% used [0x00000000c0000000,0x00000000d9fd5780,0x00000000da000000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.820 GC heap after
{Heap after GC invocations=690 (full 3):
 PSYoungGen      total 2560K, used 512K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000ead80000,0x00000000eae00000,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf00000)
 ParOldGen       total 426496K, used 426133K [0x00000000c0000000, 0x00000000da080000, 0x00000000eab00000)
  object space 426496K, 99% used [0x00000000c0000000,0x00000000da025780,0x00000000da080000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.829 GC heap before
{Heap before GC invocations=691 (full 3):
 PSYoungGen      total 2560K, used 2560K [0x00000000eab00000, 0x00000000eaf00000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 512K, 100% used [0x00000000ead80000,0x00000000eae00000,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf00000)
 ParOldGen       total 426496K, used 426133K [0x00000000c0000000, 0x00000000da080000, 0x00000000eab00000)
  object space 426496K, 99% used [0x00000000c0000000,0x00000000da025780,0x00000000da080000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.833 GC heap after
{Heap after GC invocations=691 (full 3):
 PSYoungGen      total 3072K, used 544K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 53% used [0x00000000eae00000,0x00000000eae88000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 427008K, used 426595K [0x00000000c0000000, 0x00000000da100000, 0x00000000eab00000)
  object space 427008K, 99% used [0x00000000c0000000,0x00000000da098d90,0x00000000da100000)
 Metaspace       used 62667K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.842 GC heap before
{Heap before GC invocations=692 (full 3):
 PSYoungGen      total 3072K, used 2591K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 99% used [0x00000000eab00000,0x00000000eacffff8,0x00000000ead00000)
  from space 1024K, 53% used [0x00000000eae00000,0x00000000eae88000,0x00000000eaf00000)
  to   space 1024K, 0% used [0x00000000ead00000,0x00000000ead00000,0x00000000eae00000)
 ParOldGen       total 427008K, used 426595K [0x00000000c0000000, 0x00000000da100000, 0x00000000eab00000)
  object space 427008K, 99% used [0x00000000c0000000,0x00000000da098d90,0x00000000da100000)
 Metaspace       used 62671K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.845 GC heap after
{Heap after GC invocations=692 (full 3):
 PSYoungGen      total 3072K, used 608K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead00000)
  from space 1024K, 59% used [0x00000000ead00000,0x00000000ead98020,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf80000)
 ParOldGen       total 427008K, used 426955K [0x00000000c0000000, 0x00000000da100000, 0x00000000eab00000)
  object space 427008K, 99% used [0x00000000c0000000,0x00000000da0f2d90,0x00000000da100000)
 Metaspace       used 62671K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.853 GC heap before
{Heap before GC invocations=693 (full 3):
 PSYoungGen      total 3072K, used 2656K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2048K, 100% used [0x00000000eab00000,0x00000000ead00000,0x00000000ead00000)
  from space 1024K, 59% used [0x00000000ead00000,0x00000000ead98020,0x00000000eae00000)
  to   space 1024K, 0% used [0x00000000eae80000,0x00000000eae80000,0x00000000eaf80000)
 ParOldGen       total 427008K, used 426957K [0x00000000c0000000, 0x00000000da100000, 0x00000000eab00000)
  object space 427008K, 99% used [0x00000000c0000000,0x00000000da0f37b0,0x00000000da100000)
 Metaspace       used 62671K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.857 GC heap after
{Heap after GC invocations=693 (full 3):
 PSYoungGen      total 3584K, used 736K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 1024K, 71% used [0x00000000eae80000,0x00000000eaf38000,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 427520K, used 427389K [0x00000000c0000000, 0x00000000da180000, 0x00000000eab00000)
  object space 427520K, 99% used [0x00000000c0000000,0x00000000da15f7b0,0x00000000da180000)
 Metaspace       used 62671K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.868 GC heap before
{Heap before GC invocations=694 (full 3):
 PSYoungGen      total 3584K, used 3296K [0x00000000eab00000, 0x00000000eaf80000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 1024K, 71% used [0x00000000eae80000,0x00000000eaf38000,0x00000000eaf80000)
  to   space 1024K, 0% used [0x00000000ead80000,0x00000000ead80000,0x00000000eae80000)
 ParOldGen       total 427520K, used 427389K [0x00000000c0000000, 0x00000000da180000, 0x00000000eab00000)
  object space 427520K, 99% used [0x00000000c0000000,0x00000000da15f7b0,0x00000000da180000)
 Metaspace       used 62671K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.872 GC heap after
{Heap after GC invocations=694 (full 3):
 PSYoungGen      total 3584K, used 608K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2560K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ead80000)
  from space 1024K, 59% used [0x00000000ead80000,0x00000000eae18000,0x00000000eae80000)
  to   space 1024K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eb000000)
 ParOldGen       total 428032K, used 427997K [0x00000000c0000000, 0x00000000da200000, 0x00000000eab00000)
  object space 428032K, 99% used [0x00000000c0000000,0x00000000da1f77b0,0x00000000da200000)
 Metaspace       used 62671K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.886 GC heap before
{Heap before GC invocations=695 (full 3):
 PSYoungGen      total 3584K, used 3168K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 2560K, 100% used [0x00000000eab00000,0x00000000ead80000,0x00000000ead80000)
  from space 1024K, 59% used [0x00000000ead80000,0x00000000eae18000,0x00000000eae80000)
  to   space 1024K, 0% used [0x00000000eaf00000,0x00000000eaf00000,0x00000000eb000000)
 ParOldGen       total 428032K, used 427997K [0x00000000c0000000, 0x00000000da200000, 0x00000000eab00000)
  object space 428032K, 99% used [0x00000000c0000000,0x00000000da1f77b0,0x00000000da200000)
 Metaspace       used 62679K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}
Event: 27.890 GC heap after
{Heap after GC invocations=695 (full 3):
 PSYoungGen      total 4096K, used 544K [0x00000000eab00000, 0x00000000eb000000, 0x0000000100000000)
  eden space 3072K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000eae00000)
  from space 1024K, 53% used [0x00000000eaf00000,0x00000000eaf88000,0x00000000eb000000)
  to   space 1024K, 0% used [0x00000000eae00000,0x00000000eae00000,0x00000000eaf00000)
 ParOldGen       total 428544K, used 428413K [0x00000000c0000000, 0x00000000da280000, 0x00000000eab00000)
  object space 428544K, 99% used [0x00000000c0000000,0x00000000da25f7b0,0x00000000da280000)
 Metaspace       used 62679K, committed 64192K, reserved 1114112K
  class space    used 6422K, committed 7104K, reserved 1048576K
}

Dll operation events (12 events):
Event: 0.047 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.210 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.243 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.250 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.253 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.262 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.309 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.386 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.476 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 4.590 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna18155179574216130057.dll
Event: 6.348 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 6.351 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll

Deoptimization events (20 events):
Event: 27.850 Thread 0x0000020988471c60 DEOPT PACKING pc=0x00000209b51619cc sp=0x00000022346fe4c0
Event: 27.850 Thread 0x000002098846f500 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000209b5124550 relative=0x0000000000002d10
Event: 27.850 Thread 0x000002098846f500 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000209b5124550 method=java.util.concurrent.ConcurrentHashMap$Traverser.advance()Ljava/util/concurrent/ConcurrentHashMap$Node; @ 15 c2
Event: 27.850 Thread 0x0000020988471c60 DEOPT UNPACKING pc=0x00000209b4433aa2 sp=0x00000022346fe470 mode 2
Event: 27.850 Thread 0x000002098846f500 DEOPT PACKING pc=0x00000209b5124550 sp=0x00000022347fe150
Event: 27.850 Thread 0x000002098846f500 DEOPT UNPACKING pc=0x00000209b4433aa2 sp=0x00000022347fddf0 mode 2
Event: 27.850 Thread 0x000002098846f500 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000209b51619cc relative=0x0000000000000c4c
Event: 27.850 Thread 0x000002098846f500 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000209b51619cc method=org.apache.maven.model.interpolation.StringVisitorModelInterpolator$ModelVisitor.visit(Lorg/apache/maven/model/DependencyManagement;)V @ 20 c2
Event: 27.850 Thread 0x000002098846f500 DEOPT PACKING pc=0x00000209b51619cc sp=0x00000022347fe070
Event: 27.850 Thread 0x000002098846f500 DEOPT UNPACKING pc=0x00000209b4433aa2 sp=0x00000022347fe020 mode 2
Event: 27.859 Thread 0x0000020985f465c0 DEOPT PACKING pc=0x00000209aeaa5923 sp=0x0000002232bfec30
Event: 27.859 Thread 0x0000020985f465c0 DEOPT UNPACKING pc=0x00000209b4434242 sp=0x0000002232bfe1b0 mode 0
Event: 27.883 Thread 0x0000020985f465c0 DEOPT PACKING pc=0x00000209aeaac1bb sp=0x0000002232bfebf0
Event: 27.883 Thread 0x0000020985f465c0 DEOPT UNPACKING pc=0x00000209b4434242 sp=0x0000002232bfe128 mode 0
Event: 27.904 Thread 0x0000020985f465c0 DEOPT PACKING pc=0x00000209aeaa5923 sp=0x0000002232bfec30
Event: 27.904 Thread 0x0000020985f465c0 DEOPT UNPACKING pc=0x00000209b4434242 sp=0x0000002232bfe1b0 mode 0
Event: 27.923 Thread 0x00000209875b3040 Uncommon trap: trap_request=0xffffff6e fr.pc=0x00000209b49bc004 relative=0x0000000000000164
Event: 27.923 Thread 0x00000209875b3040 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x00000209b49bc004 method=java.lang.StringLatin1.lastIndexOf([BII)I @ 19 c2
Event: 27.923 Thread 0x00000209875b3040 DEOPT PACKING pc=0x00000209b49bc004 sp=0x0000002233cfc530
Event: 27.923 Thread 0x00000209875b3040 DEOPT UNPACKING pc=0x00000209b4433aa2 sp=0x0000002233cfc4b0 mode 2

Classes loaded (20 events):
Event: 26.095 Loading class com/sun/org/apache/xerces/internal/xni/XNIException
Event: 26.096 Loading class com/sun/org/apache/xerces/internal/xni/XNIException done
Event: 26.100 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLParseException
Event: 26.101 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLParseException done
Event: 26.360 Loading class sun/nio/fs/WindowsFileCopy
Event: 26.361 Loading class sun/nio/fs/WindowsFileCopy done
Event: 26.407 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable
Event: 26.407 Loading class java/util/concurrent/ForkJoinTask$AdaptedCallable done
Event: 26.425 Loading class java/util/TreeMap$AscendingSubMap
Event: 26.425 Loading class java/util/TreeMap$NavigableSubMap
Event: 26.430 Loading class java/util/TreeMap$NavigableSubMap done
Event: 26.431 Loading class java/util/TreeMap$AscendingSubMap done
Event: 26.432 Loading class java/util/TreeMap$AscendingSubMap$AscendingEntrySetView
Event: 26.432 Loading class java/util/TreeMap$NavigableSubMap$EntrySetView
Event: 26.433 Loading class java/util/TreeMap$NavigableSubMap$EntrySetView done
Event: 26.433 Loading class java/util/TreeMap$AscendingSubMap$AscendingEntrySetView done
Event: 26.433 Loading class java/util/TreeMap$NavigableSubMap$SubMapEntryIterator
Event: 26.433 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator
Event: 26.434 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator done
Event: 26.434 Loading class java/util/TreeMap$NavigableSubMap$SubMapEntryIterator done

Classes unloaded (8 events):
Event: 5.365 Thread 0x00000209a791e9f0 Unloading class 0x00000209bf1a9400 'java/lang/invoke/LambdaForm$MH+0x00000209bf1a9400'
Event: 5.365 Thread 0x00000209a791e9f0 Unloading class 0x00000209bf1a9000 'java/lang/invoke/LambdaForm$MH+0x00000209bf1a9000'
Event: 5.365 Thread 0x00000209a791e9f0 Unloading class 0x00000209bf1a8c00 'java/lang/invoke/LambdaForm$MH+0x00000209bf1a8c00'
Event: 5.365 Thread 0x00000209a791e9f0 Unloading class 0x00000209bf1a8800 'java/lang/invoke/LambdaForm$MH+0x00000209bf1a8800'
Event: 5.365 Thread 0x00000209a791e9f0 Unloading class 0x00000209bf1a8400 'java/lang/invoke/LambdaForm$BMH+0x00000209bf1a8400'
Event: 5.365 Thread 0x00000209a791e9f0 Unloading class 0x00000209bf1a8000 'java/lang/invoke/LambdaForm$DMH+0x00000209bf1a8000'
Event: 5.365 Thread 0x00000209a791e9f0 Unloading class 0x00000209bf1a6c00 'java/lang/invoke/LambdaForm$DMH+0x00000209bf1a6c00'
Event: 18.164 Thread 0x00000209a791e9f0 Unloading class 0x00000209bf664800 'java/lang/invoke/LambdaForm$DMH+0x00000209bf664800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 26.289 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab90030}> (0x00000000eab90030) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.289 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab90880}> (0x00000000eab90880) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.294 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaba1ab0}> (0x00000000eaba1ab0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.294 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaba2828}> (0x00000000eaba2828) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.356 Thread 0x00000209875b3040 Implicit null exception at 0x00000209b4dd2484 to 0x00000209b4dd2790
Event: 26.361 Thread 0x0000020985f465c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eaba5238}> (0x00000000eaba5238) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.389 Thread 0x0000020985f465c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab53de0}> (0x00000000eab53de0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.402 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab8c8f8}> (0x00000000eab8c8f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.413 Thread 0x00000209875b3d60 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab09e10}> (0x00000000eab09e10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.417 Thread 0x00000209875b9940 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab25bf8}> (0x00000000eab25bf8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.419 Thread 0x00000209875b9940 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab35c30}> (0x00000000eab35c30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 26.420 Thread 0x00000209875b9940 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab504f0}> (0x00000000eab504f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 27.749 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab124c8}> (0x00000000eab124c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 27.750 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab12c40}> (0x00000000eab12c40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 27.750 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab3bde8}> (0x00000000eab3bde8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 27.750 Thread 0x00000209875b3040 Exception <a 'sun/nio/fs/WindowsException'{0x00000000eab48878}> (0x00000000eab48878) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 27.769 Thread 0x00000209875b3040 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eab21328}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eab21328) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 27.849 Thread 0x000002098846f500 Implicit null exception at 0x00000209b5122824 to 0x00000209b512452c
Event: 27.850 Thread 0x000002098846f500 Implicit null exception at 0x00000209b5122824 to 0x00000209b512452c
Event: 27.850 Thread 0x000002098846f500 Implicit null exception at 0x00000209b5122824 to 0x00000209b512452c

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 27.781 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.785 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.801 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.804 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.817 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.820 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.829 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.833 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.842 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.845 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.852 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.852 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.853 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.857 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.868 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.872 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.886 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.890 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 27.990 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 27.990 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad7dbc10
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad827c90
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad82bf90
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad82c910
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad83e190
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad83fa90
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad840090
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad841010
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad843010
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad847710
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad853010
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad857390
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad860390
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad86da90
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad89cc10
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad8c0710
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad8c2090
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad8e7f90
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad902310
Event: 18.186 Thread 0x00000209a791e9f0 flushing  nmethod 0x00000209ad904510

Events (20 events):
Event: 23.971 Thread 0x0000020985795270 Thread exited: 0x0000020985795270
Event: 24.712 Thread 0x00000209ff1db680 Thread added: 0x0000020985795270
Event: 25.750 Thread 0x00000209875b3d60 Thread exited: 0x00000209875b3d60
Event: 26.408 Thread 0x00000209875b3040 Thread added: 0x00000209875b3d60
Event: 26.414 Thread 0x00000209875b3d60 Thread added: 0x00000209875b9940
Event: 26.418 Thread 0x00000209875b3d60 Thread added: 0x00000209875b7f00
Event: 26.418 Thread 0x00000209875b9940 Thread added: 0x00000209875b92b0
Event: 26.478 Thread 0x00000209875b3040 Thread added: 0x00000209875b8590
Event: 27.739 Thread 0x00000209875b3040 Thread added: 0x0000020988470f40
Event: 27.821 Thread 0x00000209875b3040 Thread added: 0x0000020988471c60
Event: 27.821 Thread 0x00000209875b3040 Thread added: 0x000002098846f500
Event: 27.822 Thread 0x00000209875b3040 Thread added: 0x000002098846ee70
Event: 27.822 Thread 0x00000209875b3040 Thread added: 0x0000020988470220
Event: 27.822 Thread 0x00000209875b3040 Thread added: 0x000002098846fb90
Event: 27.894 Thread 0x0000020985f48d20 Thread added: 0x000002098846e7e0
Event: 27.897 Thread 0x000002098846f500 Thread exited: 0x000002098846f500
Event: 27.897 Thread 0x000002098846fb90 Thread exited: 0x000002098846fb90
Event: 27.898 Thread 0x0000020988471c60 Thread exited: 0x0000020988471c60
Event: 27.898 Thread 0x0000020988470220 Thread exited: 0x0000020988470220
Event: 27.899 Thread 0x000002098846ee70 Thread exited: 0x000002098846ee70


Dynamic libraries:
0x00007ff70c070000 - 0x00007ff70c07e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe0b1b0000 - 0x00007ffe0b405000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe0a820000 - 0x00007ffe0a8e7000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe088b0000 - 0x00007ffe08c50000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe08760000 - 0x00007ffe088aa000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe01b30000 - 0x00007ffe01b48000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe02010000 - 0x00007ffe0202e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe0a2e0000 - 0x00007ffe0a49f000 	C:\Windows\System32\USER32.dll
0x00007ffe08730000 - 0x00007ffe08757000 	C:\Windows\System32\win32u.dll
0x00007ffdebb00000 - 0x00007ffdebd90000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076\COMCTL32.dll
0x00007ffe09840000 - 0x00007ffe0986a000 	C:\Windows\System32\GDI32.dll
0x00007ffe09b90000 - 0x00007ffe09c39000 	C:\Windows\System32\msvcrt.dll
0x00007ffe08600000 - 0x00007ffe08721000 	C:\Windows\System32\gdi32full.dll
0x00007ffe08c60000 - 0x00007ffe08d03000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe0a570000 - 0x00007ffe0a59f000 	C:\Windows\System32\IMM32.DLL
0x00007ffe020d0000 - 0x00007ffe020dc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffdeacb0000 - 0x00007ffdead3d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffd97fb0000 - 0x00007ffd98d40000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe0a4b0000 - 0x00007ffe0a562000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe09fd0000 - 0x00007ffe0a076000 	C:\Windows\System32\sechost.dll
0x00007ffe098f0000 - 0x00007ffe09a09000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe09870000 - 0x00007ffe098e4000 	C:\Windows\System32\WS2_32.dll
0x00007ffe07e80000 - 0x00007ffe07ece000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffded780000 - 0x00007ffded7b6000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe01030000 - 0x00007ffe0103b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffe07e60000 - 0x00007ffe07e74000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe074a0000 - 0x00007ffe074ba000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe02000000 - 0x00007ffe0200a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffdec8b0000 - 0x00007ffdecaf1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe0a8f0000 - 0x00007ffe0ac65000 	C:\Windows\System32\combase.dll
0x00007ffe0a6a0000 - 0x00007ffe0a776000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdef220000 - 0x00007ffdef259000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe08fd0000 - 0x00007ffe09069000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe01b90000 - 0x00007ffe01b9f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffdf6f10000 - 0x00007ffdf6f2f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe09150000 - 0x00007ffe09836000 	C:\Windows\System32\SHELL32.dll
0x00007ffe06430000 - 0x00007ffe06c5b000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffe09a20000 - 0x00007ffe09af3000 	C:\Windows\System32\SHCORE.dll
0x00007ffe09070000 - 0x00007ffe090cd000 	C:\Windows\System32\shlwapi.dll
0x00007ffe08520000 - 0x00007ffe08544000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdf6ef0000 - 0x00007ffdf6f08000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffdfa0f0000 - 0x00007ffdfa100000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe05e30000 - 0x00007ffe05f4d000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffe07940000 - 0x00007ffe079a8000 	C:\Windows\system32\mswsock.dll
0x00007ffdeae20000 - 0x00007ffdeae36000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffdf7ba0000 - 0x00007ffdf7bb0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffddde70000 - 0x00007ffdddeb5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffe0a140000 - 0x00007ffe0a2d5000 	C:\Windows\System32\ole32.dll
0x00007ffe07ce0000 - 0x00007ffe07cfc000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffe07400000 - 0x00007ffe07438000 	C:\Windows\system32\rsaenh.dll
0x00007ffe079e0000 - 0x00007ffe07a0b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffe07f70000 - 0x00007ffe07f96000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffe07b60000 - 0x00007ffe07b6c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffe06ee0000 - 0x00007ffe06f10000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffe0a130000 - 0x00007ffe0a13a000 	C:\Windows\System32\NSI.dll
0x00007ffdd3780000 - 0x00007ffdd37c9000 	C:\Users\<USER>\AppData\Local\Temp\jna-63116079\jna18155179574216130057.dll
0x00007ffe0a810000 - 0x00007ffe0a818000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdec4e0000 - 0x00007ffdec4ea000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ffdeab40000 - 0x00007ffdeab4b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ffe003e0000 - 0x00007ffe003fc000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffe05af0000 - 0x00007ffe05b12000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.1_none_fc2d3fda0fd46076;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-63116079

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-8154ffe81a4f693c8417fe24f69e4c8e-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\64e1b4979cb66575ca19ed4f66b63e65\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\ProgramData\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin
USERNAME=Admin
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.1)
OS uptime: 0 days 3:29 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 1992, Current Mhz: 1792, Mhz Limit: 1792

Memory: 4k page, system-wide physical 8066M (444M free)
TotalPageFile size 14210M (AvailPageFile size 10M)
current process WorkingSet (physical memory assigned to process): 709M, peak: 709M
current process commit charge ("private bytes"): 695M, peak: 695M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
