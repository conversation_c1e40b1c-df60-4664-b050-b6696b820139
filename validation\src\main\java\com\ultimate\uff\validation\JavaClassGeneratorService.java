package com.ultimate.uff.validation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.util.*;

@Service
public class JavaClassGeneratorService {

    private static final Logger logger = LoggerFactory.getLogger(JavaClassGeneratorService.class);
    private static final String PACKAGE_NAME = "com.ultimate.uff.generated";
    private static final String OUTPUT_DIR = "src/main/java/com/ultimate/uff/generated";

    private static final Map<String, String> TYPE_MAPPING = Map.of(
            "string", "String",
            "int", "Integer",
            "double", "Double",
            "date", "String" // dates treated as string for now
    );

    public void generateJavaClass(String jsonMetadata) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(jsonMetadata);
            String className = capitalize(root.get("ID").asText());

            Map<String, List<JsonNode>> groupMap = new TreeMap<>();
            List<JsonNode> rootFields = new ArrayList<>();

            for (JsonNode field : root.get("fieldName")) {
                String groupPath = field.has("Group") ? field.get("Group").asText().trim() : null;
                if (groupPath == null || groupPath.isBlank()) {
                    rootFields.add(field);
                } else {
                    groupMap.computeIfAbsent(groupPath, g -> new ArrayList<>()).add(field);
                }
            }

            generatePojoClass(className, rootFields, groupMap, "", OUTPUT_DIR);
            logger.info("✅ Generated root class: {}", className);

        } catch (Exception e) {
            logger.error("❌ Failed to generate class from metadata", e);
        }
    }

    private void generatePojoClass(String className, List<JsonNode> fields, Map<String, List<JsonNode>> allGroups, String parentPath, String path) {
        try {
            List<String> lines = new ArrayList<>();
            lines.add("package " + PACKAGE_NAME + ";");
            lines.add("");
            lines.add("import com.ultimate.uff.validation.FieldValue;");
            lines.add("import com.ultimate.uff.validation.ValidatableRecord;");
            lines.add("import java.util.*;");
            lines.add("");
            lines.add("public class " + className + " extends ValidatableRecord {");
            lines.add("");
            lines.add("    private final Map<String, Object> extraFields = new HashMap<>();");

            List<String> fieldDefs = new ArrayList<>();
            List<String> constructorSetters = new ArrayList<>();
            List<String> toMapLines = new ArrayList<>();
            List<String> methods = new ArrayList<>();
            Set<String> handled = new LinkedHashSet<>();

            Map<String, List<JsonNode>> directSubGroups = getSubGroups(allGroups, parentPath);

            for (JsonNode field : fields) {
                String name = field.get("fieldName").asText();
                boolean isMulti = field.has("isMulti") && field.get("isMulti").asBoolean();
                String rawType = TYPE_MAPPING.getOrDefault(field.get("type").asText(), "String");
                String type = isMulti ? "List<" + rawType + ">" : rawType;
                String cap = capitalize(name);

                if (isMulti) {
                    fieldDefs.add("    private " + type + " " + name + " = new ArrayList<>();");
                    fieldDefs.add("    private final FieldValue<" + type + "> " + name + "Val = new FieldValue<>(new ArrayList<>());");
                } else {
                    fieldDefs.add("    private " + type + " " + name + ";");
                    fieldDefs.add("    private final FieldValue<" + type + "> " + name + "Val = new FieldValue<>(null);");
                }

                // Setters & Getters
                methods.add("    public void set" + cap + "(" + type + " " + name + ") {");
                methods.add("        this." + name + " = " + name + ";");
                methods.add("        this." + name + "Val.setValue(" + name + ");");
                methods.add("    }");
                methods.add("    public " + type + " get" + cap + "() { return this." + name + "; }");
                methods.add("    public FieldValue<" + type + "> " + name + "Val() { return " + name + "Val; }");

                // Constructor logic
                if (!isMulti && "Double".equals(rawType)) {
                    constructorSetters.add("        Object " + name + "Obj = recordDetail.get(\"" + name + "\");");
                    constructorSetters.add("        if (" + name + "Obj instanceof Number) {");
                    constructorSetters.add("            this.set" + cap + "(((Number) " + name + "Obj).doubleValue());");
                    constructorSetters.add("        }");
                } else if (!isMulti && "Integer".equals(rawType)) {
                    constructorSetters.add("        Object " + name + "Obj = recordDetail.get(\"" + name + "\");");
                    constructorSetters.add("        if (" + name + "Obj instanceof Number) {");
                    constructorSetters.add("            this.set" + cap + "(((Number) " + name + "Obj).intValue());");
                    constructorSetters.add("        }");
                } else if (isMulti) {
                    constructorSetters.add("        Object listObj = recordDetail.get(\"" + name + "\");");
                    constructorSetters.add("        if (listObj instanceof List<?>) {");
                    constructorSetters.add("            this.set" + cap + "((List<" + rawType + ">) listObj);");
                    constructorSetters.add("        }");
                } else {
                    constructorSetters.add("        Object " + name + "Obj = recordDetail.get(\"" + name + "\");");
                    constructorSetters.add("        if (" + name + "Obj != null) {");
                    constructorSetters.add("            this.set" + cap + "(" + name + "Obj.toString());");
                    constructorSetters.add("        }");
                }

                toMapLines.add("        if (" + name + " != null) map.put(\"" + name + "\", " + name + ");");
                handled.add(name);
            }

            // Handle subgroups
            for (String groupPath : directSubGroups.keySet()) {
                String groupName = getLastPart(groupPath);
                String groupClassName = capitalize(groupName);
                String listField = groupName + "List";

                fieldDefs.add("    private final List<" + groupClassName + "> " + listField + " = new ArrayList<>();");

                constructorSetters.add("        Object " + groupName + "Obj = recordDetail.get(\"" + groupName + "\");");
                constructorSetters.add("        if (" + groupName + "Obj instanceof List<?>) {");
                constructorSetters.add("            for (Object item : (List<?>) " + groupName + "Obj) {");
                constructorSetters.add("                if (item instanceof Map) {");
                constructorSetters.add("                    " + listField + ".add(new " + groupClassName + "((Map<String, Object>) item));");
                constructorSetters.add("                }");
                constructorSetters.add("            }");
                constructorSetters.add("        }");

                toMapLines.add("        if (!" + listField + ".isEmpty()) {");
                toMapLines.add("            List<Map<String, Object>> tmp = new ArrayList<>();");
                toMapLines.add("            for (" + groupClassName + " u : " + listField + ") tmp.add(u.toMap());");
                toMapLines.add("            map.put(\"" + groupName + "\", tmp);");
                toMapLines.add("        }");

                methods.add("    public List<" + groupClassName + "> get" + capitalize(listField) + "() { return " + listField + "; }");

                handled.add(groupName);

                generatePojoClass(groupClassName, directSubGroups.get(groupPath), allGroups, groupPath, path);
            }

            // Finish building class
            lines.addAll(fieldDefs);
            lines.add("");
            lines.add("    public " + className + "() {}");
            lines.add("");
            lines.add("    public " + className + "(Map<String, Object> recordDetail) {");
            lines.add("        if (recordDetail == null) return;");
            lines.addAll(constructorSetters);
            lines.add("        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {");
            lines.add("            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());");
            lines.add("        }");
            lines.add("    }");
            lines.add("");
            lines.add("    public Map<String, Object> toMap() {");
            lines.add("        Map<String, Object> map = new HashMap<>();");
            lines.addAll(toMapLines);
            lines.add("        map.putAll(extraFields);");
            lines.add("        return map;");
            lines.add("    }");
            lines.add("");
            lines.add("    private boolean isFieldHandled(String key) {");
            lines.add("        return switch (key) {");
            lines.add("            case " + String.join(", ", handled.stream().map(s -> "\"" + s + "\"").toList()) + " -> true;");
            lines.add("            default -> false;");
            lines.add("        };");
            lines.add("    }");
            lines.add("");
            lines.addAll(methods);
            lines.add("}");

            File outFile = new File(path + "/" + className + ".java");
            outFile.getParentFile().mkdirs();
            try (FileWriter writer = new FileWriter(outFile)) {
                for (String line : lines) {
                    writer.write(line + "\n");
                }
            }

            logger.info("✅ Generated class: {}", outFile.getAbsolutePath());

        } catch (Exception e) {
            logger.error("❌ Failed to generate class: " + className, e);
        }
    }

    private Map<String, List<JsonNode>> getSubGroups(Map<String, List<JsonNode>> all, String parentPath) {
        Map<String, List<JsonNode>> result = new TreeMap<>();
        for (String key : all.keySet()) {
            if (getParentPath(key).equals(parentPath)) {
                result.put(key, all.get(key));
            }
        }
        return result;
    }

    private String getParentPath(String path) {
        int idx = path.lastIndexOf('|');
        return (idx == -1) ? "" : path.substring(0, idx);
    }

    private String getLastPart(String path) {
        int idx = path.lastIndexOf('|');
        return (idx == -1) ? path : path.substring(idx + 1);
    }

    private String capitalize(String s) {
        return s == null || s.isEmpty() ? "Unknown" : s.substring(0, 1).toUpperCase() + s.substring(1);
    }
}
