package com.ultimate.uff.teller;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;

public class TellerTransaction extends ValidatableRecord {

    private static final Logger logger = LoggerFactory.getLogger(TellerTransaction.class);
    private final Map<String, Object> extraFields = new HashMap<>();
    private final FieldValue<String> desc = new FieldValue<>(null);
    private final FieldValue<String> transactionCode1 = new FieldValue<>(null);
    private final FieldValue<String> accountType1 = new FieldValue<>(null);
    private final FieldValue<String> validCurrencies1 = new FieldValue<>(null);
    private final FieldValue<String> transactionCode2 = new FieldValue<>(null);
    private final FieldValue<String> accountType2 = new FieldValue<>(null);
    private final FieldValue<String> validCurrencies2 = new FieldValue<>(null);
    private final FieldValue<String> ID = new FieldValue<>(null);
    private final FieldValue<String> recordStatus = new FieldValue<>(null);
    private final FieldValue<String> recordInputter = new FieldValue<>(null);
    private final FieldValue<String> recordAuthorizer = new FieldValue<>(null);
    private final FieldValue<String> recordCount = new FieldValue<>(null);
    private final FieldValue<String> dateTime = new FieldValue<>(null);

    public TellerTransaction() {
        logger.debug("TellerTransaction created with empty constructor.");
    }

    public TellerTransaction(Map<String, Object> recordDetail) {if (recordDetail == null) return;
    logger.debug("Initializing TellerTransaction from provided recordDetail.");

    desc.setValue(toStr(recordDetail.get("desc")));
    transactionCode1.setValue(toStr(recordDetail.get("transactionCode1")));
    accountType1.setValue(toStr(recordDetail.get("accountType1")));
    validCurrencies1.setValue(toStr(recordDetail.get("validCurrencies1")));
    transactionCode2.setValue(toStr(recordDetail.get("transactionCode2")));
    accountType2.setValue(toStr(recordDetail.get("accountType2")));
    validCurrencies2.setValue(toStr(recordDetail.get("validCurrencies2")));
    ID.setValue(toStr(recordDetail.get("ID")));
    recordStatus.setValue(toStr(recordDetail.get("recordStatus")));
    recordInputter.setValue(toStr(recordDetail.get("recordInputter")));
    recordAuthorizer.setValue(toStr(recordDetail.get("recordAuthorizer")));
    recordCount.setValue(toStr(recordDetail.get("recordCount")));
    dateTime.setValue(toStr(recordDetail.get("dateTime")));

    for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
        if (!isFieldHandled(entry.getKey())) {
            extraFields.put(entry.getKey(), entry.getValue());
        }
    }
}

    public Map<String, Object> toMap() {
        Map<String, Object> recordDetail = new HashMap<>();
        recordDetail.put("desc", desc.getValue());
        recordDetail.put("transactionCode1", transactionCode1.getValue());
        recordDetail.put("accountType1", accountType1.getValue());
        recordDetail.put("validCurrencies1", validCurrencies1.getValue());
        recordDetail.put("transactionCode2", transactionCode2.getValue());
        recordDetail.put("accountType2", accountType2.getValue());
        recordDetail.put("validCurrencies2", validCurrencies2.getValue());
        recordDetail.put("ID", ID.getValue());
        recordDetail.put("recordStatus", recordStatus.getValue());
        recordDetail.put("recordInputter", recordInputter.getValue());
        recordDetail.put("recordAuthorizer", recordAuthorizer.getValue());
        recordDetail.put("recordCount", recordCount.getValue());
        recordDetail.put("dateTime", dateTime.getValue());
        recordDetail.putAll(extraFields);
        logger.debug("TellerTransaction serialized to map with {} fields.", recordDetail.size());
        return recordDetail;
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
            case "desc", "transactionCode1", "accountType1", "validCurrencies1", "transactionCode2", "accountType2", "validCurrencies2", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public FieldValue<String> getDesc() { return desc; }
    public FieldValue<String> getTransactionCode1() { return transactionCode1; }
    public FieldValue<String> getAccountType1() { return accountType1; }
    public FieldValue<String> getValidCurrencies1() { return validCurrencies1; }
    public FieldValue<String> getTransactionCode2() { return transactionCode2; }
    public FieldValue<String> getAccountType2() { return accountType2; }
    public FieldValue<String> getValidCurrencies2() { return validCurrencies2; }
    public FieldValue<String> getID() { return ID; }
    public FieldValue<String> getRecordStatus() { return recordStatus; }
    public FieldValue<String> getRecordInputter() { return recordInputter; }
    public FieldValue<String> getRecordAuthorizer() { return recordAuthorizer; }
    public FieldValue<String> getRecordCount() { return recordCount; }
    public FieldValue<String> getDateTime() { return dateTime; }
    private String toStr(Object obj) {
        return obj != null ? obj.toString() : null;
    }

}