package com.ultimate.uff.generated;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class DebitUnit extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String debitUnit;
    private final FieldValue<String> debitUnitVal = new FieldValue<>(null);
    private Double debitUnitValue;
    private final FieldValue<Double> debitUnitValueVal = new FieldValue<>(null);

    public DebitUnit() {}

    public DebitUnit(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object debitUnitObj = recordDetail.get("debitUnit");
        if (debitUnitObj != null) {
            this.setDebitUnit(debitUnitObj.toString());
        }
        Object debitUnitValueObj = recordDetail.get("debitUnitValue");
        if (debitUnitValueObj instanceof Number) {
            this.setDebitUnitValue(((Number) debitUnitValueObj).doubleValue());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (debitUnit != null) map.put("debitUnit", debitUnit);
        if (debitUnitValue != null) map.put("debitUnitValue", debitUnitValue);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "debitUnit", "debitUnitValue" -> true;
            default -> false;
        };
    }

    public void setDebitUnit(String debitUnit) {
        this.debitUnit = debitUnit;
        this.debitUnitVal.setValue(debitUnit);
    }
    public String getDebitUnit() { return this.debitUnit; }
    public FieldValue<String> debitUnitVal() { return debitUnitVal; }
    public void setDebitUnitValue(Double debitUnitValue) {
        this.debitUnitValue = debitUnitValue;
        this.debitUnitValueVal.setValue(debitUnitValue);
    }
    public Double getDebitUnitValue() { return this.debitUnitValue; }
    public FieldValue<Double> debitUnitValueVal() { return debitUnitValueVal; }
}
