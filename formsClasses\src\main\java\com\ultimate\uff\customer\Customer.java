package com.ultimate.uff.customer;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Customer extends ValidatableRecord {

    private final FieldValue<String> customerName = new FieldValue<>(null);
    private final FieldValue<List<String>> mobileNumber = new FieldValue<>(null);
    private final FieldValue<String> sector = new FieldValue<>(null);
    private final FieldValue<String> status = new FieldValue<>(null);
    private final FieldValue<String> recordInputter = new FieldValue<>(null);
    private final FieldValue<String> recordAuthorizer = new FieldValue<>(null);
    private final FieldValue<Integer> recordCount = new FieldValue<>(0);
    private final FieldValue<String> dateTime = new FieldValue<>(null);

    private List<CustomerRelative> customerRelative;
    private List<Document> document;
    private final Map<String, Object> extraFields = new HashMap<>();

    public Customer(Map<String, Object> recordDetail) {
        customerName.setValue((String) recordDetail.get("customerName"));
        mobileNumber.setValue((List<String>) recordDetail.get("mobileNumber"));
        sector.setValue((String) recordDetail.get("sector"));
        status.setValue((String) recordDetail.get("status"));
        recordInputter.setValue((String) recordDetail.get("recordInputter"));
        recordAuthorizer.setValue((String) recordDetail.get("recordAuthorizer"));
        recordCount.setValue((Integer) recordDetail.getOrDefault("recordCount", 0));
        dateTime.setValue((String) recordDetail.get("dateTime"));

        customerRelative = (List<CustomerRelative>) recordDetail.get("customerRelative");
        document = (List<Document>) recordDetail.get("document");

        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
            case "customerName", "mobileNumber", "sector", "status", "customerRelative", "document",
                 "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public FieldValue<String> getCustomerName() { return customerName; }
    public FieldValue<List<String>> getMobileNumber() { return mobileNumber; }
    public FieldValue<String> getSector() { return sector; }
    public FieldValue<String> getStatus() { return status; }
    public FieldValue<String> getRecordInputter() { return recordInputter; }
    public FieldValue<String> getRecordAuthorizer() { return recordAuthorizer; }
    public FieldValue<Integer> getRecordCount() { return recordCount; }
    public FieldValue<String> getDateTime() { return dateTime; }

    public List<CustomerRelative> getCustomerRelative() { return customerRelative; }
    public List<Document> getDocument() { return document; }

    public Map<String, Object> getExtraFields() { return extraFields; }
}
