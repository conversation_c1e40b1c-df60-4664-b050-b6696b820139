package com.ultimate.uff.uql.util.mssql.condition;

import com.ultimate.uff.uql.model.ParsedFilter;
import com.ultimate.uff.uql.util.mssql.MSSQLExpressionBuilder;
import com.ultimate.uff.uql.util.mssql.MSSQLUtils;

import java.util.List;

public class MultiFieldConditionBuilder {

    public static String build(ParsedFilter filter, String operator, Object value) {
        String arrayField = filter.getField().replace("[]", "");
        String castType = MSSQLUtils.getSqlCastType(filter.getType());
        boolean isDate = "DATE".equalsIgnoreCase(filter.getType());
        boolean isLike = MSSQLExpressionBuilder.isLikeOperator(operator);

        String arrayExpr = MSSQLExpressionBuilder.buildMultiFieldExpr(arrayField, filter.getType());
        String columnExpr = buildColumnExpr(castType, isDate, isLike);

        return switch (operator.toUpperCase()) {
            case "EQ" -> buildEqCondition(arrayExpr, columnExpr, value, isDate);
            case "NE" -> buildNeCondition(arrayExpr, columnExpr, value, isDate);
            case "LK" -> ConditionBuildHelper.buildExists(arrayExpr, ConditionBuildHelper.buildLikeCondition(columnExpr, value.toString()));
            case "LI" -> ConditionBuildHelper.buildExists(arrayExpr, ConditionBuildHelper.buildILikeCondition(columnExpr, value.toString()));
            case "CT" -> ConditionBuildHelper.buildExists(arrayExpr, ConditionBuildHelper.buildLikeCondition(columnExpr, "%" + value + "%"));
            case "BW" -> ConditionBuildHelper.buildExists(arrayExpr, ConditionBuildHelper.buildLikeCondition(columnExpr, value + "%"));
            case "EW" -> ConditionBuildHelper.buildExists(arrayExpr, ConditionBuildHelper.buildLikeCondition(columnExpr, "%" + value));
            case "DNBW" -> "NOT " + ConditionBuildHelper.buildExists(arrayExpr, ConditionBuildHelper.buildLikeCondition(columnExpr, value + "%"));
            case "DNEW" -> "NOT " + ConditionBuildHelper.buildExists(arrayExpr, ConditionBuildHelper.buildLikeCondition(columnExpr, "%" + value));
            case "GT" -> buildSimpleComparison(arrayExpr, columnExpr, ">", value);
            case "LT" -> buildSimpleComparison(arrayExpr, columnExpr, "<", value);
            case "GE" -> buildSimpleComparison(arrayExpr, columnExpr, ">=", value);
            case "LE" -> buildSimpleComparison(arrayExpr, columnExpr, "<=", value);
            case "RG", "NR" -> buildRangeComparison(arrayExpr, columnExpr, value, operator.equalsIgnoreCase("NR"));
            default -> throw new IllegalArgumentException("Unsupported operator for multi field: " + operator);
        };
    }

    private static String buildEqCondition(String arrayExpr, String columnExpr, Object value, boolean isDate) {
        if (value == null) return "NOT " + ConditionBuildHelper.buildExists(arrayExpr, columnExpr + " IS NOT NULL");

        if (value instanceof List<?> list) {
            if (list.isEmpty()) return "1 = 0"; // Always false
            StringBuilder condition = new StringBuilder();
            for (Object item : list) {
                if (condition.length() > 0) condition.append(" AND ");
                condition.append(columnExpr).append(" = ").append(ConditionBuildHelper.formatValue(item, isDate));
            }
            return ConditionBuildHelper.buildExists(arrayExpr, condition.toString());
        }

        return ConditionBuildHelper.buildExists(arrayExpr, columnExpr + " = " + ConditionBuildHelper.formatValue(value, isDate));
    }

    private static String buildNeCondition(String arrayExpr, String columnExpr, Object value, boolean isDate) {
        if (value == null) return ConditionBuildHelper.buildExists(arrayExpr, columnExpr + " IS NOT NULL");
        return "NOT " + ConditionBuildHelper.buildExists(arrayExpr, columnExpr + " = " + ConditionBuildHelper.formatValue(value, isDate));
    }

    private static String buildSimpleComparison(String arrayExpr, String columnExpr, String op, Object value) {
        return ConditionBuildHelper.buildExists(arrayExpr, columnExpr + " " + op + " " + MSSQLUtils.formatSqlValue(value, "NVARCHAR(MAX)"));
    }

    private static String buildRangeComparison(String arrayExpr, String columnExpr, Object value, boolean negate) {
        if (!(value instanceof List<?> list) || list.size() != 2)
            throw new IllegalArgumentException("Range operator requires exactly 2 values");

        Object startRaw = list.get(0);
        Object endRaw = list.get(1);

        boolean isSimpleDate = MSSQLUtils.isSimpleDateFormat(startRaw.toString()) && MSSQLUtils.isSimpleDateFormat(endRaw.toString());

        String start = MSSQLUtils.formatSqlValue(startRaw, "NVARCHAR(MAX)");
        String end = MSSQLUtils.formatSqlValue(endRaw, "NVARCHAR(MAX)");


        String condition = isSimpleDate
                ? "CAST(" + columnExpr + " AS DATE) BETWEEN CAST(" + start + " AS DATE) AND CAST(" + end + " AS DATE)"
                : columnExpr + " BETWEEN " + start + " AND " + end;

        return negate ? "NOT " + ConditionBuildHelper.buildExists(arrayExpr, condition) : ConditionBuildHelper.buildExists(arrayExpr, condition);
    }

    private static String buildColumnExpr(String castType, boolean isDate, boolean isLike) {
        if (isLike) {
            return "value"; // LIKE always casts dynamically
        }
        return isDate ? "CAST(value AS DATE)" : "TRY_CAST(value AS " + castType + ")";
    }
}
