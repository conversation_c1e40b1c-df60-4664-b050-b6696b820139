package com.ultimate.uff.queue;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.tenant.RecordContext;
import com.ultimate.uff.validation.AfterAuthorizationService;
import com.ultimate.uff.validation.GlobalResponse;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class PostAuthorizationConsumer {

    private static final Logger logger = LoggerFactory.getLogger(PostAuthorizationConsumer.class);

    private final AfterAuthorizationService afterAuthorizationService;
    private final ObjectMapper objectMapper;

    public PostAuthorizationConsumer(AfterAuthorizationService afterAuthorizationService, ObjectMapper objectMapper) {
        this.afterAuthorizationService = afterAuthorizationService;
        this.objectMapper = objectMapper;
    }

    @KafkaListener(topics = "record-postauth-topic", groupId = "uff-postauth-group")
    public void handlePostAuthorization(ConsumerRecord<String, String> record) {
        try {
            Map<String, Object> data = objectMapper.readValue(record.value(), new TypeReference<>() {});
            String tableName = (String) data.get("tableName");
            String id = (String) data.get("id");
            Map<String, Object> recordContent = (Map<String, Object>) data.get("record");

            // Restore user ID
            String inputter = (String) recordContent.get("recordInputter");
            if (inputter != null) {
                AuthContextHolder.setUserId(inputter);
            }

            // Restore RecordContext from payload
            Map<String, Object> context = (Map<String, Object>) data.get("context");
            if (context != null) {
                String curr = (String) context.get("currentCompany");
                String prim = (String) context.get("primaryCompany");
                boolean tenant = Boolean.TRUE.equals(context.get("isTenantBased"));
                RecordContext.set(curr, prim, tenant);
            }

            logger.info("📥 Post-auth started for [{} - {}]", tableName, id);
            GlobalResponse result = afterAuthorizationService.applyPostAuthorizationLogic(tableName, id, recordContent);

            if (!result.isSuccess()) {
                logger.warn("⚠️ Post-auth failed: {}", result.getMessage());
            } else {
                logger.info("✅ Post-auth complete for [{} - {}]", tableName, id);
            }

        } catch (Exception e) {
            logger.error("❌ Error during post-auth: {}", e.getMessage(), e);
        } finally {
            AuthContextHolder.clear();
            RecordContext.clear();
        }
    }

}
