
package com.ultimate.uff.multi.thread.agents;

import com.ultimate.uff.multi.thread.jobs.ConfigurableJob;
import com.ultimate.uff.multi.thread.jobs.JobExecutable;
import com.ultimate.uff.multi.thread.jobs.JobResult;
import com.ultimate.uff.multi.thread.services.LoggerClass;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.ReadOnlyDatabaseService;
import java.util.List;
import java.util.Map;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TenantTableCreationJob implements JobExecutable,ConfigurableJob {
    @Autowired
    private ReadOnlyDatabaseService readOnlyDatabaseService;
    @Autowired
    private DatabaseService databaseService;
    //@Autowired
   // private JobLogService jobLogService;
    private Map<String, Object> jobData;
    @Autowired
    private LoggerClass loggerClass;
    @Override
    public void setJobData(Map<String, Object> jobData) {
        this.jobData = jobData;
    }

    @Override
    public JobResult execute() {
        try {
            createTablesForTenants();
            loggerClass.getLogger().info("JOB", getJobId(),  "Tenant tables created successfully", String.valueOf(System.currentTimeMillis()));
            return new JobResult("JOB", getJobId(), "SUCCESS", "Tenant tables created successfully", String.valueOf(System.currentTimeMillis()));
        } catch (Exception e) {
            loggerClass.getLogger().error("JOB", getJobId(), "FAILURE"  + e.getMessage(), String.valueOf(System.currentTimeMillis()),e);
            return new JobResult("JOB", getJobId(), "FAILURE", "Error: " + e.getMessage(), String.valueOf(System.currentTimeMillis()));
        }
    }
    public void createTablesForTenants() {
        // Step 1: Fetch tenant record IDs where isPrimary = true and status = 'NEW'
        List<String> tenantIds = readOnlyDatabaseService.getRecordIdsByCriteria("tenant", Map.of(
                "isPrimary", Map.of("EQ", true),
                "status", Map.of("EQ", "NEW")
            ));

        // Step 2: Fetch form definition IDs
        List<String> formDefinitionIds = readOnlyDatabaseService.getRecordIdsByCriteria("formDefinition", Map.of());

        // Step 3: Iterate over form definitions
        for (String formDefinitionId : formDefinitionIds) {

            Map<String, Object> formDef = readOnlyDatabaseService.getRecordById("formDefinition", formDefinitionId);
            if (formDef == null) {
                loggerClass.getLogger().error("TABLE_CREATION"+ formDefinitionId+ "FormDefinition not found for ID: " + formDefinitionId);
            continue;
        }
            // Properly handle the Boolean and String conversion
            boolean isTenantBased = false;
            Object isTenantBasedObj = formDef.get("isTenantBased");
            if (isTenantBasedObj instanceof Boolean) {
                isTenantBased = (Boolean) isTenantBasedObj;
            } else if (isTenantBasedObj instanceof String) {
                isTenantBased = Boolean.parseBoolean((String) isTenantBasedObj);
            }

            if (isTenantBased) {
                // Iterate over tenant IDs
                for (String tenantId : tenantIds) {
                    Map<String, Object> tenant = readOnlyDatabaseService.getRecordById("tenant", tenantId);
                    if (tenant == null){
                        loggerClass.getLogger().error("tenantJob"+ tenantId+ "FAILURE"+"Tenant not found for ID: " + tenantId);
                        continue;
                    }

                    String tenantCode = (String) tenant.get("ID");
                    if (tenantCode == null){
                        loggerClass.getLogger().error("tenantJob"+ tenantId+ "FAILURE"+"Tenant code is null for tenant ID: " + tenantId);
                        continue;
                    }
                    String tableName = tenantCode +"_"+ formDefinitionId;
                    try {
                        if (!readOnlyDatabaseService.checkTableExistence(tableName)) {
                            databaseService.createTable(tableName);
                            loggerClass.getLogger().info("tenantJob"+ tableName + "Tenant-based table created successfully.");
                        }else{
                            loggerClass.getLogger().info("TABLE_CREATION"+ tableName + "Tenant-based table already exists.");
                        }
                    } catch (Exception e) {
                       // System.err.println("Error creating table: " + tableName + " - " + e.getMessage());
                        loggerClass.getLogger().error("Error creating table: " + tableName + " - " + e.getMessage());
                    }
                }
            } else {
                try {
                    if (!readOnlyDatabaseService.checkTableExistence(formDefinitionId)) {
                        databaseService.createTable(formDefinitionId);
                        loggerClass.getLogger().info("TABLE_CREATION", formDefinitionId, "SUCCESS","Global table created successfully.");
                    } else {
                        loggerClass.getLogger().info("TABLE_CREATION", formDefinitionId, "Nothing","Global table already exists.");
                    }
                } catch (Exception e) {
                    System.err.println("Error creating table: " + formDefinitionId + " - " + e.getMessage());
                   // jobLogService.logError("TABLE_CREATION", formDefinitionId, "FAILURE","Error creating global table: " + formDefinitionId);
                    loggerClass.getLogger().error("Error creating table: " + formDefinitionId + " - " + e.getMessage());
                }
            }
        }
    }
    @Override
    public String getJobId() {
        return jobData != null ? (String) jobData.get("ID") : "unknown-job-id";
    }
}

