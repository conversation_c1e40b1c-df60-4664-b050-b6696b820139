package com.ultimate.uff.validation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import com.ultimate.uff.uql.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * Service responsible for applying default values to a record before insertion or processing.
 * Defaulting classes are configured dynamically per form/table via the "application_process" table.
 */
@Service
public class DefaultingService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultingService.class);

    @Autowired
    private DatabaseService databaseService;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * Applies default values to a record based on defaulting logic classes configured in the database.
     *
     * @param tableName    the name of the table/form being processed
     * @param recordDetail the original record data
     * @return the updated recordDetail after applying defaults (if any)
     */
    public Map<String, Object> applyDefaults(String tableName, Map<String, Object> recordDetail) {
        try {
            logger.info("Applying default values for table: {}", tableName);

            Map<String, Object> recordDetailMap = databaseService.getRecordById("application_process", tableName);

            if (recordDetailMap == null || recordDetailMap.isEmpty()) {
                logger.warn("No application_process entry found for table: {}", tableName);
                return recordDetail;
            }

            List<String> defaultingClasses = (List<String>) recordDetailMap.get("defultation");

            if (defaultingClasses == null || defaultingClasses.isEmpty()) {
                logger.info("No defaulting classes configured for table: {}", tableName);
                return recordDetail;
            }

            for (String className : defaultingClasses) {
                if (className == null || className.isEmpty()) continue;

                logger.debug("Invoking defaulting logic: {}", className);
                CustomValidator validator = (CustomValidator) applicationContext.getBean(Class.forName(className));

                // ✅ Save the returned updated data!
                recordDetail = validator.newData(recordDetail);
            }

            return recordDetail;
        } catch (Exception e) {
            logger.error("Error applying default values for table: " + tableName, e);
            return recordDetail;
        }
    }
}
