package com.ultimate.uff.validation;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.uql.service.DatabaseService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.Map;

/**
 * Custom validator for validating query-builder configurations.
 * Ensures that the `fileName` exists as a valid form and optionally validates referenced fields.
 */
@Component
public class QueryBuilderValidator implements CustomValidator {

    private static final Logger logger = LoggerFactory.getLogger(QueryBuilderValidator.class);

    @Autowired
    private DatabaseService databaseService;

    @Autowired
    private RecordInsertionService recordInsertionService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Validates the query builder configuration stored in `recordDetail`.
     *
     * @param id           the record ID (not used here)
     * @param recordDetail the dynamic form data containing fileName and other attributes
     * @return validation message: "Validation successful" or specific error
     */
    @Override
    public String validate(String id, Map<String, Object> recordDetail) {
        String fileName = (String) recordDetail.get("fileName");
        logger.info("Validating query builder for fileName: {}", fileName);

        Boolean tableExists = databaseService.checkRecordExistence("formDefinition", fileName);
        if (!Boolean.TRUE.equals(tableExists)) {
            logger.warn("fileName '{}' does not exist in formDefinition", fileName);
            return "fileName must be an existing table name";
        }

        GlobalResponse metadataJson = recordInsertionService.getTableMetadata(fileName);
        if (metadataJson == null) {
            logger.error("Metadata not found for table: {}", fileName);
            return "Metadata not found for table: " + fileName;
        }

        try {
            // Placeholder for future validation using JSON metadata
            logger.debug("Metadata fetched successfully for table: {}", fileName);
        } catch (Exception e) {
            logger.error("Exception while processing metadata for '{}': {}", fileName, e.getMessage(), e);
            return "Error processing table metadata: " + e.getMessage();
        }

        logger.info("Validation passed for query builder ID: {}", id);
        return "Validation successful";
    }

    /**
     * Recursively checks if a field name is valid based on a metadata node tree.
     *
     * @param fieldsNode metadata fields node
     * @param fieldName  field to validate
     * @return true if valid, false otherwise
     */
    private boolean isValidField(JsonNode fieldsNode, String fieldName) {
        if (fieldsNode.has(fieldName)) {
            return true;
        }

        for (Iterator<Map.Entry<String, JsonNode>> it = fieldsNode.fields(); it.hasNext(); ) {
            Map.Entry<String, JsonNode> fieldEntry = it.next();
            JsonNode fieldMetadata = fieldEntry.getValue();

            if (fieldMetadata.has("subfields")) {
                JsonNode subfieldsNode = fieldMetadata.path("subfields");
                if (isValidField(subfieldsNode, fieldName)) {
                    return true;
                }
            }
        }

        return false;
    }
}
