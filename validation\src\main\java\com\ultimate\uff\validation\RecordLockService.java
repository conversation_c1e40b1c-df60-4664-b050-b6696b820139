package com.ultimate.uff.validation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.uql.service.DatabaseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Manages locking and unlocking of records during concurrent operations
 * such as insertion and authorization to ensure data consistency.
 */
@Service
public class RecordLockService {

    private static final Logger logger = LoggerFactory.getLogger(RecordLockService.class);

    @Autowired
    private DatabaseService databaseService;

    private static final String LOCK_TABLE = "recordLocks";

    /**
     * Locks a record for a specific user if it is not already locked by another user.
     *
     * @param tableName the table containing the record
     * @param recordId  the record's unique ID
     * @param userId    the user attempting to lock the record
     * @return true if the lock is acquired, false otherwise
     */
    public boolean lockRecord(String tableName, String recordId, String userId) {
        String lockId = tableName + "_" + recordId;

        Map<String, Object> existingLock = databaseService.getRecordById(LOCK_TABLE, lockId);
        if (existingLock != null) {
            String lockedBy = (String) existingLock.get("lockedBy");

            // If already locked by someone else, deny lock
            if (!lockedBy.equals(userId)) {
                logger.warn("Record [{}] in table [{}] is already locked by [{}]", recordId, tableName, lockedBy);
                return false;
            }

            // Already locked by same user — allow
            logger.info("Record [{}] in table [{}] is already locked by requesting user [{}]", recordId, tableName, userId);
            return true;
        }

        Map<String, Object> lockData = new HashMap<>();
        lockData.put("lockId", lockId);
        lockData.put("recordId", recordId);
        lockData.put("tableName", tableName);
        lockData.put("lockedBy", userId);
        lockData.put("dateTime", System.currentTimeMillis());

        try {
            String jsonLockData = new ObjectMapper().writeValueAsString(lockData);
            databaseService.insertRecord(LOCK_TABLE, lockId, jsonLockData);
            logger.info("Locked record [{}] in table [{}] by user [{}]", recordId, tableName, userId);
            return true;
        } catch (Exception e) {
            logger.error("Failed to lock record [{}] in [{}]: {}", recordId, tableName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Unlocks a record, allowing other users to operate on it.
     *
     * @param tableName the table name
     * @param recordId  the record ID
     */
    public void unlockRecord(String tableName, String recordId) {
        String lockId = tableName + "_" + recordId;
        databaseService.deleteRecordById(LOCK_TABLE, lockId);
        logger.info("Unlocked record [{}] from table [{}]", recordId, tableName);
    }

    /**
     * Checks whether the record is locked by a user other than the given user.
     *
     * @param tableName the table
     * @param recordId  the record ID
     * @param userId    the current user
     * @return true if locked by someone else, false if not locked or locked by current user
     */
    public boolean isRecordLockedByAnotherUser(String tableName, String recordId, String userId) {
        String lockId = tableName + "_" + recordId;
        Map<String, Object> existingLock = databaseService.getRecordById(LOCK_TABLE, lockId);

        if (existingLock != null) {
            String lockedBy = (String) existingLock.get("lockedBy");
            boolean isLockedByOther = !lockedBy.equals(userId);
            if (isLockedByOther) {
                logger.debug("Record [{}] is locked by another user [{}]", recordId, lockedBy);
            }
            return isLockedByOther;
        }

        return false;
    }
}
