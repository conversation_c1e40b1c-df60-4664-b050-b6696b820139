package com.ultimate.uff.validation;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents the result of validating a record or form.
 * Contains a list of field-level validation errors.
 */
public class ValidationResponse {

    /**
     * Represents a single field error with field name and error message.
     */
    public static class FieldError {
        private final String field;
        private final String error;

        /**
         * Constructs a field error.
         *
         * @param field the name of the field
         * @param error the associated error message
         */
        public FieldError(String field, String error) {
            this.field = field;
            this.error = error;
        }

        public String getField() {
            return field;
        }

        public String getError() {
            return error;
        }
    }

    /** List of all field-level validation errors */
    private final List<FieldError> fieldErrors = new ArrayList<>();

    /**
     * Adds a new error to the list for a specific field.
     *
     * @param fieldName    the field name
     * @param errorMessage the validation error message
     */
    public void addFieldError(String fieldName, String errorMessage) {
        fieldErrors.add(new FieldError(fieldName, errorMessage));
    }

    /**
     * Retrieves all collected field errors.
     *
     * @return list of {@link FieldError}
     */
    public List<FieldError> getFieldErrors() {
        return fieldErrors;
    }

    /**
     * Checks if any field errors are present.
     *
     * @return true if there are validation errors
     */
    public boolean hasErrors() {
        return !fieldErrors.isEmpty();
    }
}
