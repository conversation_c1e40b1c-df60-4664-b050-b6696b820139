<jboss-deployment-structure xmlns="urn:jboss:deployment-structure:1.2">
    <deployment>
        <!-- Prevent WildFly from overriding application logging -->
        <exclude-subsystems>
            <subsystem name="logging"/>
        </exclude-subsystems>

        <!-- Ensure Logback dependencies are properly loaded -->
        <dependencies>
            <module name="org.slf4j" export="true"/>
            <module name="ch.qos.logback" export="true"/>
        </dependencies>
    </deployment>
</jboss-deployment-structure>
