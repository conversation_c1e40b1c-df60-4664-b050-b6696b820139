package com.ultimate.uff.uql.util.mssql.condition;

import com.ultimate.uff.uql.model.ParsedFilter;
import com.ultimate.uff.uql.util.mssql.MSSQLExpressionBuilder;
import com.ultimate.uff.uql.util.mssql.MSSQLUtils;
import com.ultimate.uff.uql.util.mssql.OpenJsonChainBuilder.OpenJsonChainResult;

import java.util.List;

public class GroupFieldConditionBuilder {

    public static String build(ParsedFilter filter, String operator, Object value) {
        String type = filter.getType();
        String castType = MSSQLUtils.getSqlCastType(type);
        boolean isDate = "DATE".equalsIgnoreCase(type);
        boolean isDeep = filter.getGroupPathParts().size() > 2;

        OpenJsonChainResult deepChain = null;
        String expr = null;

        if (isDeep) {
            boolean lastFieldIsArray = filter.getField() != null && filter.getField().endsWith("[]");
            deepChain = MSSQLExpressionBuilder.buildDeepGroupFieldSelector("JSON_ROW_DETAIL", filter.getGroupPathParts(), lastFieldIsArray);
        } else {
            expr = MSSQLExpressionBuilder.buildGroupFieldSelector(filter);
        }

        return switch (operator.toUpperCase()) {
            case "EQ" -> buildEqCondition(filter, expr, deepChain, value, isDate, isDeep);
            case "NE" -> buildNeCondition(filter, expr, deepChain, value, isDate, isDeep);
            case "LK" -> buildLikeCondition(filter, expr, deepChain, value.toString(), isDeep);
            case "LI" -> buildILikeCondition(filter, expr, deepChain, value.toString(), isDeep);
            case "CT" -> buildLikeCondition(filter, expr, deepChain, "%" + value + "%", isDeep);
            case "BW" -> buildLikeCondition(filter, expr, deepChain, value + "%", isDeep);
            case "EW" -> buildLikeCondition(filter, expr, deepChain, "%" + value, isDeep);
            case "DNBW" -> "NOT " + buildLikeCondition(filter, expr, deepChain, value + "%", isDeep);
            case "DNEW" -> "NOT " + buildLikeCondition(filter, expr, deepChain, "%" + value, isDeep);
            case "GT" -> buildSimpleComparison(filter, expr, deepChain, ">", value, castType, isDeep);
            case "LT" -> buildSimpleComparison(filter, expr, deepChain, "<", value, castType, isDeep);
            case "GE" -> buildSimpleComparison(filter, expr, deepChain, ">=", value, castType, isDeep);
            case "LE" -> buildSimpleComparison(filter, expr, deepChain, "<=", value, castType, isDeep);
            case "RG", "NR" ->
                buildRangeComparison(filter, expr, deepChain, value, castType, isDeep, operator.equalsIgnoreCase("NR"));
            default -> throw new IllegalArgumentException("Unsupported operator for group field: " + operator);
        };
    }

    private static String buildEqCondition(ParsedFilter filter, String expr, OpenJsonChainResult deepChain,
            Object value, boolean isDate, boolean isDeep) {
        if (value == null) {
            if (isDeep) {
                return wrapDeepNullCheck(deepChain);
            }
            return ConditionBuildHelper.buildExists(filter, expr + " IS NULL", isDeep);
        }

        if (isDeep) {
            return wrapDeepOpenJsonQuery(deepChain, value, isDate);
        }

        if (isDate && MSSQLUtils.isDateOnly(value)) {
            return MSSQLExpressionBuilder.buildGroupFieldDateOnly(
                    filter.getBaseField(),
                    filter.getNestedField(),
                    "=",
                    value.toString());
        }

        return ConditionBuildHelper.buildExists(filter, expr + " = " + ConditionBuildHelper.formatValue(value, isDate),
                isDeep);
    }

    private static String buildNeCondition(ParsedFilter filter, String expr, OpenJsonChainResult deepChain,
            Object value, boolean isDate, boolean isDeep) {
        if (value == null)
            return ConditionBuildHelper.buildExists(filter, expr + " IS NOT NULL", isDeep);
        if (isDeep)
            return "NOT " + wrapDeepOpenJsonQuery(deepChain, value, isDate);
        if (isDate && MSSQLUtils.isDateOnly(value)) {
            return MSSQLExpressionBuilder.buildGroupFieldDateOnly(filter.getBaseField(), filter.getNestedField(), "<>",
                    value.toString());
        }

        return "NOT " + ConditionBuildHelper.buildExists(filter,
                expr + " = " + ConditionBuildHelper.formatValue(value, isDate), isDeep);
    }

    private static String buildLikeCondition(ParsedFilter filter, String expr, OpenJsonChainResult deepChain,
            String pattern, boolean isDeep) {
        if (isDeep)
            return wrapDeepOpenJsonQueryLike(deepChain, pattern);
        String castedExpr = "CAST(" + expr + " AS NVARCHAR(MAX))";
        return ConditionBuildHelper.buildExists(filter, castedExpr + " LIKE " + MSSQLUtils.likeQuote(pattern), isDeep);
    }

    private static String buildSimpleComparison(ParsedFilter filter, String expr, OpenJsonChainResult deepChain,
            String op, Object value, String castType, boolean isDeep) {
        if (isDeep)
            return wrapDeepOpenJsonQueryComparison(deepChain, op, value, castType);
        return ConditionBuildHelper.buildExists(filter,
                expr + " " + op + " " + MSSQLUtils.formatSqlValue(value, castType), isDeep);
    }

    private static String buildRangeComparison(ParsedFilter filter, String expr, OpenJsonChainResult deepChain,
            Object value, String castType, boolean isDeep, boolean negate) {
        if (!(value instanceof List<?> list) || list.size() != 2)
            throw new IllegalArgumentException("Range operator requires two values");

        Object startRaw = list.get(0);
        Object endRaw = list.get(1);

        boolean isSimpleDate = MSSQLUtils.isSimpleDateFormat(startRaw.toString())
                && MSSQLUtils.isSimpleDateFormat(endRaw.toString());

        String start = MSSQLUtils.formatSqlValue(startRaw, "NVARCHAR(MAX)");
        String end = MSSQLUtils.formatSqlValue(endRaw, "NVARCHAR(MAX)");

        String rangeField;
        if (isDeep)
            rangeField = deepChain.lastField().endsWith("[]")
                    ? deepChain.lastAlias() + ".[value]"
                    : deepChain.lastAlias() + "." + deepChain.lastField();
        else
            rangeField = expr;

        String condition = isSimpleDate
                ? "CAST(" + rangeField + " AS DATE) BETWEEN CAST(" + start + " AS DATE) AND CAST(" + end + " AS DATE)"
                : rangeField + " BETWEEN " + start + " AND " + end;

        return negate ? "NOT " + ConditionBuildHelper.buildExists(filter, condition, isDeep)
                : ConditionBuildHelper.buildExists(filter, condition, isDeep);
    }

    private static String wrapDeepOpenJsonQuery(OpenJsonChainResult chain, Object value, boolean isDate) {
        if (chain == null) {
            throw new IllegalStateException("❗ deepChain is NULL inside wrapDeepOpenJsonQuery()");
        }
    
        String fullSql;
        String fieldExpr;
    
        if (chain.isLastArray()) {
            fieldExpr = chain.lastAlias() + ".value";
            fullSql = "EXISTS (SELECT 1 FROM " + chain.sql() +
                      " WHERE " + fieldExpr + " = " + ConditionBuildHelper.formatValue(value, isDate) + ")";
        }
         else {
            // When last field is scalar
            fieldExpr = chain.lastAlias() + "." + chain.lastField();
            fullSql = "EXISTS (SELECT 1 FROM " + chain.sql() +
                      " WHERE " + fieldExpr + " = " + ConditionBuildHelper.formatValue(value, isDate) + ")";
        }
    
        return fullSql;
    }    

    private static String wrapDeepOpenJsonQueryLike(OpenJsonChainResult chain, String pattern) {
        String fieldExpr;
        String fromSql;
    
        if (chain.isLastArray()) {
            // Last field is array -> fieldExpr = lvl3.value
            fieldExpr = chain.lastAlias() + ".value";
            fromSql = chain.sql();
        } else {
            // Last field is scalar -> fieldExpr = lvl3.subofsub (already extracted)
            fieldExpr = chain.lastAlias() + "." + chain.lastField();
            fromSql = chain.sql();
        }
    
        return "EXISTS (SELECT 1 FROM " + fromSql +
                " WHERE CAST(" + fieldExpr + " AS NVARCHAR(MAX)) LIKE " + MSSQLUtils.likeQuote(pattern) + ")";
    }    

    private static String wrapDeepOpenJsonQueryComparison(OpenJsonChainResult chain, String op, Object value,
            String castType) {
        String fieldExpr = chain.isLastArray() ? chain.lastAlias() + ".value" : chain.lastAlias() + "." + chain.lastField();

        return "EXISTS (SELECT 1 FROM " + chain.sql() +
                " WHERE " + fieldExpr + " " + op + " " + MSSQLUtils.formatSqlValue(value, castType) + ")";
    }

    private static String wrapDeepNullCheck(OpenJsonChainResult chain) {
        if (chain == null) {
            throw new IllegalStateException("❗ deepChain is NULL inside wrapDeepNullCheck()");
        }
        String fieldExpr = chain.isLastArray() ? chain.lastAlias() + ".value" : chain.lastAlias() + "." + chain.lastField();
        return "EXISTS (SELECT 1 FROM " + chain.sql() + " WHERE " + fieldExpr + " IS NULL)";
    }

    private static String buildILikeCondition(ParsedFilter filter, String expr, OpenJsonChainResult deepChain, String pattern, boolean isDeep) {
        if (isDeep) {
            return wrapDeepOpenJsonQueryILike(deepChain, pattern.toLowerCase());
        }
        String castedExpr = "LOWER(CAST(" + expr + " AS NVARCHAR(MAX)))";
        return ConditionBuildHelper.buildExists(filter, castedExpr + " LIKE " + MSSQLUtils.likeQuote(pattern.toLowerCase()), isDeep);
    }

    private static String wrapDeepOpenJsonQueryILike(OpenJsonChainResult chain, String pattern) {
        String fieldExpr = chain.isLastArray() ? chain.lastAlias() + ".value" : chain.lastAlias() + "." + chain.lastField();
        return "EXISTS (SELECT 1 FROM " + chain.sql() +
               " WHERE LOWER(CAST(" + fieldExpr + " AS NVARCHAR(MAX))) LIKE " + MSSQLUtils.likeQuote(pattern.toLowerCase()) + ")";
    }
    
}
