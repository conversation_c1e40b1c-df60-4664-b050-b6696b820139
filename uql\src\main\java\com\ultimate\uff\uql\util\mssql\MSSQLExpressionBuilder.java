package com.ultimate.uff.uql.util.mssql;

import java.util.List;
import com.ultimate.uff.uql.model.ParsedFilter;
import com.ultimate.uff.uql.util.mssql.OpenJsonChainBuilder.OpenJsonChainResult;

public class MSSQLExpressionBuilder {

    public static String buildSingleFieldExpr(ParsedFilter filter) {
        return wrapCast("JSON_VALUE(JSON_ROW_DETAIL, '$." + filter.getField() + "')", filter.getType());
    }

    public static String buildSingleFieldExpr(String fieldName, String type) {
        return wrapCast("JSON_VALUE(JSON_ROW_DETAIL, '$." + fieldName + "')", type);
    }

    public static String buildSingleFieldAsStringExpr(String fieldName) {
        return "CAST(JSON_VALUE(JSON_ROW_DETAIL, '$." + fieldName + "') AS NVARCHAR(MAX))";
    }

    public static String buildMultiFieldExpr(ParsedFilter filter) {
        return buildMultiFieldExpr(filter.getField().replace("[]", ""), filter.getType());
    }

    public static String buildMultiFieldExpr(String field, String type) {
        return "OPENJSON(JSON_QUERY(JSON_ROW_DETAIL, '$." + field + "')) WITH (value NVARCHAR(MAX) '$')";
    }

    public static String buildGroupFieldSelector(ParsedFilter filter) {
        String nested = filter.getNestedField();
        if (nested.isEmpty()) throw new IllegalArgumentException("Invalid nested field path: " + filter.getField());

        String expr = "JSON_VALUE(arr.value, '$." + nested + "')";
        return (filter.getType() == null || "STRING".equalsIgnoreCase(filter.getType()))
                ? expr
                : wrapCast(expr, filter.getType());
    }

    public static String buildSingleFieldDateOnly(String fieldName, String operator, String dateStr) {
        return "CAST(JSON_VALUE(JSON_ROW_DETAIL, '$." + fieldName + "') AS DATE) " +
                operator + " CAST('" + dateStr.replace("'", "''") + "' AS DATE)";
    }

    public static String buildGroupFieldDateOnly(String arrayField, String nestedField, String operator,
            String dateStr) {
        return "EXISTS (SELECT 1 FROM OPENJSON(JSON_QUERY(JSON_ROW_DETAIL, '$." + arrayField + "')) arr " +
                "WHERE CAST(JSON_VALUE(arr.value, '$." + nestedField + "') AS DATE) " +
                operator + " CAST('" + dateStr.replace("'", "''") + "' AS DATE))";
    }

    public static boolean isLikeOperator(String operator) {
        return switch (operator == null ? "" : operator.toUpperCase()) {
            case "CT", "LK", "BW", "EW", "DNBW", "DNEW" -> true;
            default -> false;
        };
    }

    public static String buildGroupFieldExpr(String arrayField) {
        return "OPENJSON(JSON_QUERY(JSON_ROW_DETAIL, '$." + arrayField + "')) AS arr";
    }

    public static OpenJsonChainResult buildDeepGroupFieldSelector(String jsonField, List<String> pathParts, boolean lastFieldIsArray){
        return OpenJsonChainBuilder.build(jsonField, pathParts, lastFieldIsArray);
    }

    public static String wrapCast(String expr, String type) {
        return switch (type == null ? "" : type.toUpperCase()) {
            case "INT" -> "TRY_CAST(" + expr + " AS INT)";
            case "DOUBLE" -> "TRY_CAST(" + expr + " AS FLOAT)";
            case "BOOLEAN" -> "TRY_CAST(" + expr + " AS BIT)";
            //case "DATE" -> "TRY_CAST(" + expr + " AS DATETIME)";
            case "DATE" -> "FORMAT(TRY_CAST(" + expr + " AS DATETIME), 'yyyy-MM-ddTHH:mm:ssZ')";  // ✅ ISO8601
            default -> expr;
        };
    }
}
