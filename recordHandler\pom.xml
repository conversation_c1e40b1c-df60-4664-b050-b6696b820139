<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ultimate.uff</groupId>
    <artifactId>uff</artifactId>
    <version>0.0.1-SNAPSHOT</version>
  </parent>
  <artifactId>recordHandler</artifactId>
  
  <dependencies>
  <!-- Inherit shared dependencies from parent if defined -->
  
  <!-- Example: Spring Context -->
  <dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-context</artifactId>
  </dependency>

  <!-- If using ObjectMapper or JSON -->
  <dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
  </dependency>
  <dependency>
  	<groupId>com.ultimate.uff</groupId>
  	<artifactId>queue</artifactId>
  	<version>0.0.1-SNAPSHOT</version>
  </dependency>
  </dependencies>

</project>