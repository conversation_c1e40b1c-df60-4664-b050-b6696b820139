package com.ultimate.uff.multi.thread.services;

import org.springframework.stereotype.Component;

@Component("workerService1")
public class WorkerService1 implements AgentWorker {

    private volatile boolean running = false;
    private String assignedUser; // User dynamically set from DB

    @Override
    public void run() {
        if (assignedUser == null) {
            System.err.println("❌ No user assigned to workerService1");
            return;
        }

        running = true;
        System.out.println("🟢 workerService1 started for user: " + assignedUser);

        while (running) {
            try {
                if (hasNewData(assignedUser)) {
                    System.out.println("⚙️ Processing new data for: " + assignedUser);
                    process(assignedUser);
                } else {
                    System.out.println("⏩ No new data for: " + assignedUser);
                }

                Thread.sleep(5000); // Delay before next check

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

        System.out.println("🛑 workerService1 stopped.");
    }

    @Override
    public void stop() {
        running = false;
    }

    @Override
    public boolean isRunning() {
        return running;
    }

    @Override
    public void setUser(String user) {
        this.assignedUser = user;
    }

    private boolean hasNewData(String user) {
        // Replace with real DB check
        return Math.random() > 0.5;
    }

    private void process(String user) {
        System.out.println("✅ Completed task for: " + user);
    }
}
