package com.ultimate.uff.teller;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class CcyDenom extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private Double denomValue;
    private final FieldValue<Double> denomValueVal = new FieldValue<>(null);
    private String ccy;
    private final FieldValue<String> ccyVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private String recordAuthorizer;
    private final FieldValue<String> recordAuthorizerVal = new FieldValue<>(null);
    private int recordCount;
    private final FieldValue<Integer> recordCountVal = new FieldValue<>(0);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public CcyDenom() {}

    public CcyDenom(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        this.setDenomValue(( Double ) recordDetail.get("denomValue"));
        this.setCcy(( String ) recordDetail.get("ccy"));
        this.setID(( String ) recordDetail.get("ID"));
        this.setRecordStatus(( String ) recordDetail.get("recordStatus"));
        this.setRecordInputter(( String ) recordDetail.get("recordInputter"));
        this.setRecordAuthorizer(( String ) recordDetail.get("recordAuthorizer"));
        this.setRecordCount(( Integer ) recordDetail.get("recordCount"));
        this.setDateTime(( String ) recordDetail.get("dateTime"));
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (denomValue != null) map.put("denomValue", denomValue);
        if (ccy != null) map.put("ccy", ccy);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != 0) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "denomValue", "ccy", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setDenomValue(Double denomValue) {
        this.denomValue = denomValue;
        this.denomValueVal.setValue(denomValue);
    }
    public Double getDenomValue() { return this.denomValue; }
    public FieldValue<Double> denomValueVal() { return denomValueVal; }
    public void setCcy(String ccy) {
        this.ccy = ccy;
        this.ccyVal.setValue(ccy);
    }
    public String getCcy() { return this.ccy; }
    public FieldValue<String> ccyVal() { return ccyVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(String recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public String getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<String> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public int getRecordCount() { return this.recordCount; }
    public FieldValue<Integer> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}
