package com.ultimate.uff.uql.util;

import java.util.Map;

public interface QueryBuilder {
    String createTable(String tableName);

    String createView(String tableName, String metadataJson);

    String insertRecord(String tableName, String id, String recordDetail);

    String checkTableExistence(String tableName);

    String deleteRecordById(String tableName, String id);

    String getRecordById(String tableName, String id);

    String getRecordsByCriteria(String tableName, Map<String, Object> criteria);

    /*
     * Deprecated: All getFieldsValues* will be removed. limited support for
     * nested/group fields. Use getRecordByIdJson instead.
     * String getFieldsValuesByCriteriaJson(String tableName, List<String> fields,
     * Map<String, Object> criteriaJson);
     * String getFieldsValuesById(String tableName, String id, List<String> fields);
     * String getFieldsValuesByIdJson(String tableName, String id, List<String>
     * fields);
     * String getFieldsValuesByCriteria(String tableName, List<String> fields,
     * Map<String, Object> criteria);
     */

    String dropTable(String tableName);

}
