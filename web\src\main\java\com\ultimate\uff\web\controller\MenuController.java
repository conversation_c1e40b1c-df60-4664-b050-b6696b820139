package com.ultimate.uff.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ultimate.uff.service.SignonProfileService;

import java.util.Map;

@RestController
@RequestMapping("/api")
public class MenuController {

    @Autowired
    private SignonProfileService signonProfileService;

  

    @GetMapping("/menu/{menuName}")
    public Map<String, Object> getSubMenuByMenuName(@PathVariable String menuName) {
        return signonProfileService.getSubMenuByMenuName(menuName);
    }
}
