package com.ultimate.uff.validation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.uql.service.DatabaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service responsible for validating fields in a dynamic record based on metadata definitions.
 * Handles type conversion, mandatory checks, multi-value fields, and foreign key validation.
 */
@Service
public class FieldValidationService {

    private static final Logger logger = LoggerFactory.getLogger(FieldValidationService.class);

    @Autowired
    private DatabaseService databaseService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Validates the provided record fields against the given metadata definition.
     *
     * @param metadataJson metadata structure containing field definitions
     * @param recordDetail the actual record to be validated
     * @return validation result string ("Validation successful" or error message)
     */
    public String validateFields(Map<String, Object> metadataJson, Map<String, Object> recordDetail) {
        try {
            logger.info("Starting validation for record");

            List<Map<String, Object>> fieldsList = (List<Map<String, Object>>) metadataJson.get("fieldName");

            for (Map<String, Object> fieldMetadata : fieldsList) {
                String fieldName = (String) fieldMetadata.get("fieldName");
                boolean isMandatory = parseBoolean(fieldMetadata.getOrDefault("mandatory", false));
                boolean isMulti = parseBoolean(fieldMetadata.getOrDefault("isMulti", false));
                String fieldType = (String) fieldMetadata.get("type");
                String group = (String) fieldMetadata.getOrDefault("Group", "");
                String foreignKey = (String) fieldMetadata.getOrDefault("foreginKey", "");

                if (group != null && !group.isEmpty()) {
                    if (!convertAndValidateGroupField(recordDetail, fieldMetadata, group, fieldName, isMandatory, isMulti, fieldType, foreignKey)) {
                        return "Field " + fieldName + " in group " + group + " is mandatory, invalid, or fails foreign key validation.";
                    }
                } else {
                    if (isMandatory && !recordDetail.containsKey(fieldName)) {
                        return "Field " + fieldName + " is mandatory";
                    }

                    if (recordDetail.containsKey(fieldName)) {
                        Object fieldValue = recordDetail.get(fieldName);

                        if (isMulti && fieldValue instanceof List && ((List<?>) fieldValue).isEmpty() && !isMandatory) {
                            continue;
                        }

                        Object convertedValue = convertFieldType(fieldType, fieldValue, isMulti);
                        if (convertedValue == null || !validateFieldType(fieldType, convertedValue, isMulti)) {
                            return "Field " + fieldName + " has an invalid type: " + fieldType;
                        }

                        if (foreignKey != null && !foreignKey.isEmpty()) {
                            if (isMulti && convertedValue instanceof List) {
                                for (Object val : (List<?>) convertedValue) {
                                    if (isEmptyValue(val)) continue;
                                    if (!databaseService.checkRecordExistence(foreignKey, val.toString())) {
                                        return "Field " + fieldName + " contains an invalid foreign key reference: " + val;
                                    }
                                }
                            } else if (!isEmptyValue(convertedValue)) {
                                if (!databaseService.checkRecordExistence(foreignKey, convertedValue.toString())) {
                                    return "Field " + fieldName + " has an invalid foreign key reference.";
                                }
                            }
                        }

                        recordDetail.put(fieldName, convertedValue);
                    }
                }
            }

            return "Validation successful";
        } catch (Exception e) {
            logger.error("Error during field validation", e);
            return "Error validating fields: " + e.getMessage();
        }
    }



    private boolean convertAndValidateGroupField(Map<String, Object> recordDetail, Map<String, Object> fieldMetadata,
                                                 String group, String fieldName, boolean isMandatory,
                                                 boolean isMulti, String fieldType, String foreignKey) {
        String[] groupParts = group.split("\\.");
        Map<String, Object> currentGroup = recordDetail;

        for (String groupPart : groupParts) {
            if (currentGroup.containsKey(groupPart)) {
                Object groupField = currentGroup.get(groupPart);

                if (groupField instanceof List) {
                    List<?> groupList = (List<?>) groupField;
                    boolean fieldValidated = false;

                    for (Object groupItem : groupList) {
                        if (groupItem instanceof Map) {
                            Map<String, Object> itemMap = (Map<String, Object>) groupItem;

                            if (itemMap.containsKey(fieldName)) {
                                Object fieldValue = itemMap.get(fieldName);

                                if (isMulti && fieldValue instanceof List && ((List<?>) fieldValue).isEmpty() && !isMandatory) {
                                    continue;
                                }

                                Object convertedValue = convertFieldType(fieldType, fieldValue, isMulti);
                                if (convertedValue == null || !validateFieldType(fieldType, convertedValue, isMulti)) {
                                    return false;
                                }

                                if (foreignKey != null && !foreignKey.isEmpty()) {
                                    if (isMulti && convertedValue instanceof List) {
                                        for (Object val : (List<?>) convertedValue) {
                                            if (isEmptyValue(val)) continue;
                                            if (!databaseService.checkRecordExistence(foreignKey, val.toString())) {
                                                return false;
                                            }
                                        }
                                    } else if (!isEmptyValue(convertedValue)) {
                                        if (!databaseService.checkRecordExistence(foreignKey, convertedValue.toString())) {
                                            return false;
                                        }
                                    }
                                }

                                itemMap.put(fieldName, convertedValue);
                                fieldValidated = true;
                            }
                        }
                    }

                    return fieldValidated || !isMandatory;

                } else if (groupField instanceof Map) {
                    currentGroup = (Map<String, Object>) groupField;

                    if (currentGroup.containsKey(fieldName)) {
                        Object fieldValue = currentGroup.get(fieldName);

                        if (isMulti && fieldValue instanceof List && ((List<?>) fieldValue).isEmpty() && !isMandatory) {
                            return true;
                        }

                        Object convertedValue = convertFieldType(fieldType, fieldValue, isMulti);
                        if (convertedValue == null || !validateFieldType(fieldType, convertedValue, isMulti)) {
                            return false;
                        }

                        if (foreignKey != null && !foreignKey.isEmpty()) {
                            if (isMulti && convertedValue instanceof List) {
                                for (Object val : (List<?>) convertedValue) {
                                    if (isEmptyValue(val)) continue;
                                    if (!databaseService.checkRecordExistence(foreignKey, val.toString())) {
                                        return false;
                                    }
                                }
                            } else if (!isEmptyValue(convertedValue)) {
                                if (!databaseService.checkRecordExistence(foreignKey, convertedValue.toString())) {
                                    return false;
                                }
                            }
                        }

                        currentGroup.put(fieldName, convertedValue);
                        return true;
                    }
                }
            }
        }

        return !isMandatory;
    }

    private Object convertFieldType(String fieldType, Object fieldValue, boolean isMulti) {
        try {
            if (isMulti && fieldValue instanceof List) {
                List<?> list = (List<?>) fieldValue;
                return list.stream()
                        .map(value -> convertSingleFieldType(fieldType, value.toString()))
                        .collect(Collectors.toList());
            } else {
                return convertSingleFieldType(fieldType, fieldValue.toString());
            }
        } catch (Exception e) {
            return null;
        }
    }

    private Object convertSingleFieldType(String fieldType, String fieldValue) {
        switch (fieldType) {
            case "int":
                return Integer.parseInt(fieldValue);
            case "string":
                return fieldValue;
            case "boolean":
                return Boolean.parseBoolean(fieldValue);
            case "double":
                return Double.parseDouble(fieldValue);
            case "date":
                return fieldValue;
            default:
                return null;
        }
    }

    private boolean validateFieldType(String fieldType, Object fieldValue, boolean isMulti) {
        if (isMulti && fieldValue instanceof List) {
            List<?> list = (List<?>) fieldValue;
            return list.stream().allMatch(value -> validateSingleFieldType(fieldType, value));
        } else {
            return validateSingleFieldType(fieldType, fieldValue);
        }
    }

    private boolean validateSingleFieldType(String fieldType, Object fieldValue) {
        if (fieldValue == null) {
            return false;
        }

        switch (fieldType) {
            case "int":
                return fieldValue instanceof Integer;
            case "string":
                return fieldValue instanceof String;
            case "boolean":
                return fieldValue instanceof Boolean;
            case "double":
                return fieldValue instanceof Double;
            case "date":
                return fieldValue instanceof String;
            default:
                return false;
        }
    }

    private boolean parseBoolean(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return false;
    }

    private boolean isEmptyValue(Object val) {
        return val == null || (val instanceof String && ((String) val).trim().isEmpty());
    }
}
