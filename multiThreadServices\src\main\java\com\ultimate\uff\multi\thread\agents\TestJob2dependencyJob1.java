package com.ultimate.uff.multi.thread.agents;

import java.time.LocalDateTime;
import java.util.Map;
import org.springframework.stereotype.Component;
import com.ultimate.uff.multi.thread.jobs.ConfigurableJob;
import com.ultimate.uff.multi.thread.jobs.JobExecutable;
import com.ultimate.uff.multi.thread.jobs.JobResult;

@Component
public class TestJob2dependencyJob1 implements JobExecutable,ConfigurableJob {
    private  Map<String, Object> jobData;
    @Override
    public void setJobData(Map<String, Object> jobData) {
        this.jobData = jobData;
    }
    @Override
    public JobResult execute() {
        String jobId = (String) jobData.get("ID");

        try {
            System.out.println("✅ TestJob2dependencyJob1 is executing with data: " + this.jobData);

            return new JobResult(
                "JOB",                              // type
                jobId,                              // refId
                "SUCCESS",                          // action
                "TestJob2dependencyJob1 executed successfullyYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY",    // message
                LocalDateTime.now().toString()      // timestamp
            );

        } catch (Exception e) {
            return new JobResult(
                "JOB",
                jobId,
                "FAILURE",
                "TestJob2dependencyJob1 execution failedFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF: " + e.getMessage(),
                LocalDateTime.now().toString()
            );
        }
    }

    @Override
    public String getJobId() {
        return (String) jobData.get("ID");
    }
}
