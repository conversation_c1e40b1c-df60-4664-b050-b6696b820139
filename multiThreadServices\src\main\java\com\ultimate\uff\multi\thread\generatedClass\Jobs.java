package com.ultimate.uff.multi.thread.generatedClass;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class Jobs extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String jobName;
    private final FieldValue<String> jobNameVal = new FieldValue<>(null);
    private String description;
    private final FieldValue<String> descriptionVal = new FieldValue<>(null);
    private String className;
    private final FieldValue<String> classNameVal = new FieldValue<>(null);
    private String startedAt;
    private final FieldValue<String> startedAtVal = new FieldValue<>(null);
    private String finishedAt;
    private final FieldValue<String> finishedAtVal = new FieldValue<>(null);
    private String errorMessage;
    private final FieldValue<String> errorMessageVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private List<String> recordAuthorizer = new ArrayList<>();
    private final FieldValue<List<String>> recordAuthorizerVal = new FieldValue<>(new ArrayList<>());
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public Jobs() {}

    public Jobs(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object jobNameObj = recordDetail.get("jobName");
        if (jobNameObj != null) {
            this.setJobName(jobNameObj.toString());
        }
        Object descriptionObj = recordDetail.get("description");
        if (descriptionObj != null) {
            this.setDescription(descriptionObj.toString());
        }
        Object classNameObj = recordDetail.get("className");
        if (classNameObj != null) {
            this.setClassName(classNameObj.toString());
        }
        Object startedAtObj = recordDetail.get("startedAt");
        if (startedAtObj != null) {
            this.setStartedAt(startedAtObj.toString());
        }
        Object finishedAtObj = recordDetail.get("finishedAt");
        if (finishedAtObj != null) {
            this.setFinishedAt(finishedAtObj.toString());
        }
        Object errorMessageObj = recordDetail.get("errorMessage");
        if (errorMessageObj != null) {
            this.setErrorMessage(errorMessageObj.toString());
        }
        Object IDObj = recordDetail.get("ID");
        if (IDObj != null) {
            this.setID(IDObj.toString());
        }
        Object recordStatusObj = recordDetail.get("recordStatus");
        if (recordStatusObj != null) {
            this.setRecordStatus(recordStatusObj.toString());
        }
        Object recordInputterObj = recordDetail.get("recordInputter");
        if (recordInputterObj != null) {
            this.setRecordInputter(recordInputterObj.toString());
        }
        Object listObj = recordDetail.get("recordAuthorizer");
        if (listObj instanceof List<?>) {
            this.setRecordAuthorizer((List<String>) listObj);
        }
        Object recordCountObj = recordDetail.get("recordCount");
        if (recordCountObj != null) {
            this.setRecordCount(recordCountObj.toString());
        }
        Object dateTimeObj = recordDetail.get("dateTime");
        if (dateTimeObj != null) {
            this.setDateTime(dateTimeObj.toString());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (jobName != null) map.put("jobName", jobName);
        if (description != null) map.put("description", description);
        if (className != null) map.put("className", className);
        if (startedAt != null) map.put("startedAt", startedAt);
        if (finishedAt != null) map.put("finishedAt", finishedAt);
        if (errorMessage != null) map.put("errorMessage", errorMessage);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "jobName", "description", "className", "startedAt", "finishedAt", "errorMessage", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
        this.jobNameVal.setValue(jobName);
    }
    public String getJobName() { return this.jobName; }
    public FieldValue<String> jobNameVal() { return jobNameVal; }
    public void setDescription(String description) {
        this.description = description;
        this.descriptionVal.setValue(description);
    }
    public String getDescription() { return this.description; }
    public FieldValue<String> descriptionVal() { return descriptionVal; }
    public void setClassName(String className) {
        this.className = className;
        this.classNameVal.setValue(className);
    }
    public String getClassName() { return this.className; }
    public FieldValue<String> classNameVal() { return classNameVal; }
    public void setStartedAt(String startedAt) {
        this.startedAt = startedAt;
        this.startedAtVal.setValue(startedAt);
    }
    public String getStartedAt() { return this.startedAt; }
    public FieldValue<String> startedAtVal() { return startedAtVal; }
    public void setFinishedAt(String finishedAt) {
        this.finishedAt = finishedAt;
        this.finishedAtVal.setValue(finishedAt);
    }
    public String getFinishedAt() { return this.finishedAt; }
    public FieldValue<String> finishedAtVal() { return finishedAtVal; }
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        this.errorMessageVal.setValue(errorMessage);
    }
    public String getErrorMessage() { return this.errorMessage; }
    public FieldValue<String> errorMessageVal() { return errorMessageVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(List<String> recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public List<String> getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<List<String>> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}
