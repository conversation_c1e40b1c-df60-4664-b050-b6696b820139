package com.ultimate.uff.validation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Abstract base class for dynamic form records that supports:
 * - Aggregating validation errors from {@link FieldValue} fields
 * - Serializing the object into a flat structure (map) suitable for saving or transmission
 */
public abstract class ValidatableRecord {

    private static final Logger logger = LoggerFactory.getLogger(ValidatableRecord.class);

    /**
     * Scans all declared fields of the current object and aggregates validation errors
     * from any field that is of type {@link FieldValue} and has attached errors.
     *
     * @return a {@link ValidationResponse} containing field-specific error messages
     */
    public ValidationResponse getValidationResponse() {
        ValidationResponse response = new ValidationResponse();

        for (Field field : this.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object fieldValue = field.get(this);
                if (fieldValue instanceof FieldValue<?> fv && fv.hasError()) {
                    for (String error : fv.getErrors()) {
                        response.addFieldError(field.getName(), error);
                    }
                }
            } catch (Exception e) {
                logger.warn("Unable to access field [{}] in [{}]: {}", field.getName(), this.getClass().getSimpleName(), e.getMessage());
            }
        }

        return response;
    }

    /**
     * Converts this record into a plain `Map<String, Object>` structure suitable
     * for JSON storage or transmission. Automatically unwraps FieldValue, nested ValidatableRecord,
     * lists of records/maps, and primitive values.
     *
     * @return a flat map representation of the object
     */
    public Map<String, Object> toStructure() {
        Map<String, Object> result = new HashMap<>();
        Class<?> clazz = this.getClass();

        for (Field field : clazz.getDeclaredFields()) {
            try {
                field.setAccessible(true);
                Object value = field.get(this);

                if (value instanceof FieldValue<?> fv) {
                    result.put(field.getName(), fv.getValue());

                } else if (value instanceof Map<?, ?> map) {
                    result.put(field.getName(), map);

                } else if (value instanceof List<?> list) {
                    List<Object> serializedList = new ArrayList<>();
                    for (Object item : list) {
                        if (item instanceof ValidatableRecord vr) {
                            serializedList.add(vr.toStructure());
                        } else if (item instanceof Map<?, ?> m) {
                            serializedList.add(m);
                        } else {
                            serializedList.add(item);
                        }
                    }
                    result.put(field.getName(), serializedList);

                } else {
                    result.put(field.getName(), value);
                }

            } catch (Exception e) {
                logger.error("Failed to serialize field [{}] in [{}]: {}", field.getName(), clazz.getSimpleName(), e.getMessage(), e);
            }
        }

        return result;
    }
}
