package com.ultimate.uff.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import com.ultimate.uff.uql.service.DatabaseService;

import java.util.Arrays;
import java.util.Map;

@Service
public class SignonProfileService {



    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private DatabaseService databaseService;

    public String getProfileBySignOnName(String signOnName) {
        try {
            // Get the recordDetailJson using the databaseService
            Map<String, Object> user = Map.of("Sign_on_name", Map.of("EQ", signOnName));

            ArrayNode results = databaseService.getRecordsByCriteria("user", user);
            if (results.isEmpty()) {
                throw new UsernameNotFoundException("Invalid username");
            }

            // Parse the RECORDDETAIL JSON
            String recordDetailJson = results.get(0).toString();
            JsonNode outerNode = objectMapper.readTree(recordDetailJson);

            // Step 1: Get the inner JSON string
            String jsonRowDetailStr = outerNode.path("JSON_ROW_DETAIL").asText();

            // Step 2: Parse the inner JSON string into a real JsonNode
            JsonNode recordDetailNode = objectMapper.readTree(jsonRowDetailStr);

            // Step 3: Now you can safely access "profile"
            JsonNode profileNode = recordDetailNode.path("profile");

            if (!profileNode.isMissingNode()) {
                return profileNode.asText();
            } else {
                return "Profile not found";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "Error occurred: " + e.getMessage();
        }
    }

    public Map<String, Object> getMenuByProfile(String profile) {
        try {
            // Fetch recordDetailJson as a JsonNode
        	Map<String, Object> recordDetailJson = databaseService.getRecordById("menu", profile);

            if (recordDetailJson != null && !recordDetailJson.isEmpty()) {
                // Convert JsonNode to Map<String, Object>
                return recordDetailJson;
            } else {
                return null;  // No record found
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;  // Handle exception and return null in case of error
        }
    }


    public Map<String, Object> getSubMenuByMenuName(String menuName) {
    	try {
            // Fetch recordDetailJson as a JsonNode
    		Map<String, Object> recordDetailJson = databaseService.getRecordById("menu", menuName);

            if (recordDetailJson != null && !recordDetailJson.isEmpty()) {
                // Convert JsonNode to Map<String, Object>
                return recordDetailJson;
            } else {
                return null;  // No record found
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;  // Handle exception and return null in case of error
        }
    }
}
