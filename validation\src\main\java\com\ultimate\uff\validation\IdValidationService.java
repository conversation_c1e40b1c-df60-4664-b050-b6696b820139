package com.ultimate.uff.validation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import com.ultimate.uff.uql.service.DatabaseService;

/**
 * Service responsible for executing custom ID validation logic per table.
 * Dynamically fetches validator class names from the application_process table
 * and invokes them using Spring's ApplicationContext.
 */
@Service
public class IdValidationService {

    private static final Logger logger = LoggerFactory.getLogger(IdValidationService.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private DatabaseService databaseService;

    /**
     * Validates a record ID for a specific table using dynamically configured validation classes.
     *
     * @param tableName the name of the table (used to retrieve configuration)
     * @param id the record ID to validate
     * @return GlobalResponse indicating success or error message
     */
    public GlobalResponse validateId(String tableName, String id) {
        try {
            logger.info("Validating ID '{}' for table '{}'", id, tableName);
            Map<String, Object> recordDetailMap = null;

            try {
                recordDetailMap = databaseService.getRecordById("application_process", tableName);
            } catch (IncorrectResultSizeDataAccessException e) {
                logger.warn("No application_process config found for table '{}'", tableName);
                return new GlobalResponse(true, "Validation successful", null);
            }

            if (recordDetailMap == null || recordDetailMap.isEmpty()) {
                logger.debug("Empty config for '{}'; skipping ID validation.", tableName);
                return new GlobalResponse(true, "Validation successful", null);
            }

            List<String> idValidationClasses = (List<String>) recordDetailMap.get("idValidation");

            if (idValidationClasses == null || idValidationClasses.isEmpty()) {
                logger.debug("No ID validators configured for '{}'", tableName);
                return new GlobalResponse(true, "Validation successful", null);
            }

            for (String className : idValidationClasses) {
                if (className == null || className.isEmpty()) continue;

                logger.debug("Executing ID validator: {}", className);
                CustomIdValidator validator = (CustomIdValidator) applicationContext.getBean(Class.forName(className));
                String validationResult = validator.validate(id);

                if (!"Validation successful".equals(validationResult)) {
                    logger.warn("Validation failed: {}", validationResult);
                    return new GlobalResponse(false, validationResult, null);
                }
            }

            logger.info("ID validation passed for '{}'", id);
            return new GlobalResponse(true, "Validation successful", null);

        } catch (Exception e) {
            logger.error("Error during ID validation for '{}': {}", id, e.getMessage(), e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("exception", e.getClass().getSimpleName());
            errorData.put("message", e.getMessage());
            return new GlobalResponse(false, "Error during ID validation", errorData);
        }
    }
}
