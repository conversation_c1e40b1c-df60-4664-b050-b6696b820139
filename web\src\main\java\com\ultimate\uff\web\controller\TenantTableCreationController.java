package com.ultimate.uff.web.controller;

import com.ultimate.uff.validation.TenantTableCreationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/tables")
public class TenantTableCreationController {

    @Autowired
    private TenantTableCreationService tenantTableCreationService;

    /**
     * Endpoint to trigger table creation based on tenant and form definitions.
     * @return String indicating the status of the operation
     */
    @GetMapping("/create")
    public String createTenantTables() {
        try {
            tenantTableCreationService.createTablesForTenants();
            return "Table creation process completed successfully.";
        } catch (Exception e) {
            return "Error during table creation: " + e.getMessage();
        }
    }
}
