package com.ultimate.uff.uql.util.mssql;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.ultimate.uff.uql.model.ParsedFilter;
import com.ultimate.uff.uql.util.FilterStringParser;
import com.ultimate.uff.uql.util.mssql.condition.*;

public class MSSQLConditionBuilder {

    public String buildCondition(String field, String operator, String value) {
        ParsedFilter parsed = FilterStringParser.parseKey(field); // 👈 NEW PARSED WAY
        return switch (parsed.getStructure()) {
            case SINGIL -> SingleFieldConditionBuilder.build(parsed, operator, value);
            case MULTI -> MultiFieldConditionBuilder.build(parsed, operator, value);
            case GROUP -> GroupFieldConditionBuilder.build(parsed, operator, value);
        };
    }

    public String buildWhereClauseFromJson(Map<String, Object> criteriaBody) {
        if (criteriaBody.containsKey("AND")) {
            return buildLogicalGroup("AND", criteriaBody.get("AND"));
        } else if (criteriaBody.containsKey("OR")) {
            return buildLogicalGroup("OR", criteriaBody.get("OR"));
        } else {
            return buildFlatGroup(criteriaBody);
        }
    }

    public String parseCriteriaString(String criteriaString) {
        if (criteriaString == null || criteriaString.isBlank())
            return "";

        StringBuilder where = new StringBuilder();
        String[] tokens = criteriaString.split("\\s+");
        int i = 0;

        while (i < tokens.length) {
            String token = tokens[i];
            if (token.equalsIgnoreCase("AND") || token.equalsIgnoreCase("OR")) {
                where.append(" ").append(token.toUpperCase()).append(" ");
                i++;
            } else {
                if (i + 2 >= tokens.length)
                    throw new IllegalArgumentException("Invalid criteria near: " + token);
                String field = tokens[i];
                String op = tokens[i + 1];
                String val = tokens[i + 2];
                where.append(buildCondition(field, op, val));
                i += 3;
            }
        }

        return where.toString();
    }

    private String buildLogicalGroup(String op, Object listObj) {
        if (!(listObj instanceof List<?> list))
            return "";
    
        if (list.isEmpty()) {
            return op.equals("AND") ? "(1 = 1)" : "(1 = 0)"; // ✅ Fix: AND = always true, OR = always false
        }
    
        List<String> clauses = new ArrayList<>();
        for (Object item : list) {
            if (item instanceof Map<?, ?> map)
                clauses.add("(" + buildWhereClauseFromJson((Map<String, Object>) map) + ")");
        }
    
        return String.join(" " + op + " ", clauses);
    }    

    private String buildFlatGroup(Map<String, Object> criteria) {
        List<String> conditions = new ArrayList<>();

        for (Map.Entry<String, Object> entry : criteria.entrySet()) {
            String fieldKey = entry.getKey(); // e.g. "multiIntField:INT[]"
            Object cond = entry.getValue(); // e.g. { "EQ": 88 }

            ParsedFilter parsed = FilterStringParser.parseKey(fieldKey); // 🔄 Use parser

            if (cond instanceof Map<?, ?> opMap) {
                for (Map.Entry<?, ?> opEntry : opMap.entrySet()) {
                    String operator = opEntry.getKey().toString().toUpperCase();
                    Object value = opEntry.getValue();

                    String sql = switch (parsed.getStructure()) {
                        case SINGIL -> SingleFieldConditionBuilder.build(parsed, operator, value);
                        case MULTI -> MultiFieldConditionBuilder.build(parsed, operator, value);
                        case GROUP -> GroupFieldConditionBuilder.build(parsed, operator, value);
                    };

                    conditions.add(sql);
                }
            }
        }

        return String.join(" AND ", conditions);
    }

}
