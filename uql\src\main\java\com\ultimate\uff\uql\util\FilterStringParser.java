package com.ultimate.uff.uql.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.ultimate.uff.uql.model.ParsedFilter;
import com.ultimate.uff.uql.model.enums.FieldStructureType;

public class FilterStringParser {

    // Format: field[:TYPE][[] or [].key] OPERATOR value
    private static final Pattern FULL_FILTER_PATTERN = Pattern.compile(
        "^([\\w.\\[\\]]+)(?::(\\w+))?\\s+(\\w+)\\s+(.+)$"
    );

    /**
     * Parses a full filter string like:
     * "multiIntField[]:INT EQ 88"
     */
    public static ParsedFilter parse(String input) {
        Matcher matcher = FULL_FILTER_PATTERN.matcher(input.trim());
        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid filter format: " + input);
        }

        String rawField = matcher.group(1);
        String type = matcher.group(2);
        String operator = matcher.group(3).toUpperCase();
        String value = matcher.group(4).trim();

        ParsedFilter base = parseKeyInternal(rawField, type);
        return new ParsedFilter(base.getField(), base.getBaseField(), base.getType(), base.getStructure(), operator, value);
    }

    /**
     * Parses only the field key part, like:
     * "multiIntField[]:INT"
     */
    public static ParsedFilter parseKey(String fieldKey) {
        String rawField;
        String type = "STRING"; // default

        if (fieldKey.contains(":")) {
            String[] parts = fieldKey.split(":", 2);
            rawField = parts[0].trim();
            type = parts[1].trim().toUpperCase();
        } else {
            rawField = fieldKey.trim();
        }

        return parseKeyInternal(rawField, type);
    }

    /**
     * Internal reusable parser logic for field + type.
     */
    private static ParsedFilter parseKeyInternal(String rawField, String type) {
        String field;
        String baseField;
        FieldStructureType structure;
    
        if (rawField.contains("[].")) {
            // GROUP field
            field = rawField; // ✅ KEEP []
            baseField = rawField.substring(0, rawField.indexOf("[]."));
            structure = FieldStructureType.GROUP;
        } else if (rawField.endsWith("[]")) {
            // MULTI field
            field = rawField; // ✅ KEEP []
            baseField = rawField.substring(0, rawField.length() - 2);
            structure = FieldStructureType.MULTI;
        } else {
            // SINGLE field
            field = rawField;
            baseField = rawField;
            structure = FieldStructureType.SINGIL;
        }
    
        return new ParsedFilter(field, baseField, type, structure, null, null);
    }
    
    
}
