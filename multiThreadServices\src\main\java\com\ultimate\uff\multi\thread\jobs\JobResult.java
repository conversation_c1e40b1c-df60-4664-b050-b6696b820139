package com.ultimate.uff.multi.thread.jobs;

public class JobResult {
    private final String type;       // like JOB
    private final String refId;      // ID job
    private final String action;     // SUCCESS / FAILURE
    private final String message;
    private final String timestamp;

    public JobResult(String type, String refId, String action, String message, String timestamp) {
        this.type = type;
        this.refId = refId;
        this.action = action;
        this.message = message;
        this.timestamp = timestamp;
    }

    public String getType() {
        return type;
    }

    public String getRefId() {
        return refId;
    }

    public String getAction() {
        return action;
    }

    public String getMessage() {
        return message;
    }

    public String getTimestamp() {
        return timestamp;
    }
}
