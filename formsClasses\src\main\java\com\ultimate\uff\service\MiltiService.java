package com.ultimate.uff.service;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;

import java.util.HashMap;
import java.util.Map;

public class MiltiService extends ValidatableRecord {

    private final FieldValue<String> serviceName = new FieldValue<>(null);
    private final FieldValue<String> user = new FieldValue<>(null);
    private final FieldValue<String> status = new FieldValue<>(null);

    private final FieldValue<String> inputter = new FieldValue<>(null);
    private final FieldValue<String> authorizer = new FieldValue<>(null);
    private final FieldValue<String> dateTime = new FieldValue<>(null);
    private final FieldValue<Integer> recordCount = new FieldValue<>(0);

    private final Map<String, Object> extraFields = new HashMap<>();

    public MiltiService(Map<String, Object> record) {
        serviceName.setValue((String) record.get("serviceName"));
        user.setValue((String) record.get("user"));
        status.setValue((String) record.get("status"));

        inputter.setValue((String) record.get("recordInputter"));
        authorizer.setValue((String) record.get("recordAuthorizer"));
        dateTime.setValue((String) record.get("dateTime"));
        recordCount.setValue(toInteger(record.get("recordCount")));

        for (Map.Entry<String, Object> entry : record.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
            case "serviceName", "user", "status",
                 "recordInputter", "recordAuthorizer", "dateTime", "recordCount" -> true;
            default -> false;
        };
    }

    private Integer toInteger(Object value) {
        if (value instanceof Number) return ((Number) value).intValue();
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException ignored) {}
        }
        return 0;
    }

    public Map<String, Object> updateRecordDetail(Map<String, Object> recordDetail) {
        if (recordDetail == null) {
            recordDetail = new HashMap<>();
        }

        recordDetail.put("serviceName", serviceName.getValue());
        recordDetail.put("user", user.getValue());
        recordDetail.put("status", status.getValue());

        recordDetail.put("recordInputter", inputter.getValue());
        recordDetail.put("recordAuthorizer", authorizer.getValue());
        recordDetail.put("dateTime", dateTime.getValue());
        recordDetail.put("recordCount", recordCount.getValue());

        recordDetail.putAll(extraFields);

        return recordDetail;
    }

    // Getters (add others if needed)
    public FieldValue<String> getServiceName() { return serviceName; }
    public FieldValue<String> getUser() { return user; }
    public FieldValue<String> getStatus() { return status; }
    public FieldValue<String> getInputter() { return inputter; }
    public FieldValue<String> getAuthorizer() { return authorizer; }
    public FieldValue<String> getDateTime() { return dateTime; }
    public FieldValue<Integer> getRecordCount() { return recordCount; }
    public Map<String, Object> getExtraFields() { return extraFields; }
}
