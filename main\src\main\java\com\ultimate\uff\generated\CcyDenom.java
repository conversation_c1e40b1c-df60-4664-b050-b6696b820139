package com.ultimate.uff.generated;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class CcyDenom extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private Double denomValue;
    private final FieldValue<Double> denomValueVal = new FieldValue<>(null);
    private String ccy;
    private final FieldValue<String> ccyVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private List<String> recordAuthorizer = new ArrayList<>();
    private final FieldValue<List<String>> recordAuthorizerVal = new FieldValue<>(new ArrayList<>());
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);

    public CcyDenom() {}

    public CcyDenom(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object denomValueObj = recordDetail.get("denomValue");
        if (denomValueObj instanceof Number) {
            this.setDenomValue(((Number) denomValueObj).doubleValue());
        }
        Object ccyObj = recordDetail.get("ccy");
        if (ccyObj != null) {
            this.setCcy(ccyObj.toString());
        }
        Object IDObj = recordDetail.get("ID");
        if (IDObj != null) {
            this.setID(IDObj.toString());
        }
        Object recordStatusObj = recordDetail.get("recordStatus");
        if (recordStatusObj != null) {
            this.setRecordStatus(recordStatusObj.toString());
        }
        Object recordInputterObj = recordDetail.get("recordInputter");
        if (recordInputterObj != null) {
            this.setRecordInputter(recordInputterObj.toString());
        }
        Object listObj = recordDetail.get("recordAuthorizer");
        if (listObj instanceof List<?>) {
            this.setRecordAuthorizer((List<String>) listObj);
        }
        Object recordCountObj = recordDetail.get("recordCount");
        if (recordCountObj != null) {
            this.setRecordCount(recordCountObj.toString());
        }
        Object dateTimeObj = recordDetail.get("dateTime");
        if (dateTimeObj != null) {
            this.setDateTime(dateTimeObj.toString());
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (denomValue != null) map.put("denomValue", denomValue);
        if (ccy != null) map.put("ccy", ccy);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "denomValue", "ccy", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public void setDenomValue(Double denomValue) {
        this.denomValue = denomValue;
        this.denomValueVal.setValue(denomValue);
    }
    public Double getDenomValue() { return this.denomValue; }
    public FieldValue<Double> denomValueVal() { return denomValueVal; }
    public void setCcy(String ccy) {
        this.ccy = ccy;
        this.ccyVal.setValue(ccy);
    }
    public String getCcy() { return this.ccy; }
    public FieldValue<String> ccyVal() { return ccyVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(List<String> recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public List<String> getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<List<String>> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
}
