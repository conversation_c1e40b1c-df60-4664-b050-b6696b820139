package com.ultimate.uff.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ultimate.uff.queue.RecordProcessingService;

@Service
public class MergingService {

    @Autowired
    private RecordProcessingService recordProcessingService;

    @Autowired
    private ScreenBuilderService screenBuilderService;

    public String mergeApiResponses(String tableName) {
        String tables = tableName.split(",")[0];
        ResponseEntity<Map<String, Object>> firstApiResponseEntity = recordProcessingService.getTableMetadata(tables);

        if (!firstApiResponseEntity.getStatusCode().is2xxSuccessful() || firstApiResponseEntity.getBody() == null) {
            System.out.println("First API call failed or returned null.");
            return null;
        }

        Map<String, Object> firstApiResponse = firstApiResponseEntity.getBody();
        Map<String, Object> dataFromFirst = (Map<String, Object>) firstApiResponse.get("data");
        ObjectMapper objectMapper = new ObjectMapper();

        Map<String, Object> secondApiResponse = screenBuilderService.getscreenMetadata(tableName);

        // 🟨 Case: Second API is null — just return the first API response as-is
        if (secondApiResponse == null) {
            System.out.println("Second API call returned null. Returning first API only.");
            try {
                Map<String, Object> responseMap = new HashMap<>();
                responseMap.put("data", dataFromFirst);
                responseMap.put("status", "success");
                return objectMapper.writeValueAsString(responseMap);
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }

        try {
            List<Map<String, Object>> firstApiFields = (List<Map<String, Object>>) dataFromFirst.get("fieldName");
            List<Map<String, Object>> secondApiFields = (List<Map<String, Object>>) secondApiResponse.get("fieldName");

            if (firstApiFields == null || secondApiFields == null) {
                System.out.println("No fieldName found in one of the API responses.");
                return null;
            }

            List<Map<String, Object>> filteredFields = new ArrayList<>();
            boolean idIncluded = false;

            for (Map<String, Object> secondField : secondApiFields) {
                String secondFieldName = (String) secondField.get("fieldName");

                for (Map<String, Object> firstField : firstApiFields) {
                    String firstFieldName = (String) firstField.get("fieldName");

                    if (firstFieldName.equals(secondFieldName)) {
                        secondField.forEach(firstField::put);
                        filteredFields.add(firstField);

                        if ("ID".equalsIgnoreCase(firstFieldName)) {
                            idIncluded = true;
                        }
                    }
                }
            }

            if (!idIncluded) {
                for (Map<String, Object> firstField : firstApiFields) {
                    String firstFieldName = (String) firstField.get("fieldName");
                    if ("ID".equalsIgnoreCase(firstFieldName)) {
                        filteredFields.add(firstField);
                        break;
                    }
                }
            }

            Map<String, Object> dataMap = new HashMap<>(secondApiResponse);
            dataMap.put("fieldName", filteredFields);  // Replace fieldName with merged fields

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("data", dataMap);
            responseMap.put("status", "success");

            return objectMapper.writeValueAsString(responseMap);

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
