package com.ultimate.uff.uql.util.mssql;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.ultimate.uff.uql.util.QueryBuilder;

import java.util.Map;

@Component
// @Profile("mssql")
@ConditionalOnProperty(name = "spring.datasource.driver-class-name", havingValue = "com.microsoft.sqlserver.jdbc.SQLServerDriver")
public class MSSQLQueryBuilder implements QueryBuilder {

    private final MSSQLSelectBuilder selectBuilder = new MSSQLSelectBuilder();

    @Override
    public String getRecordById(String tableName, String id) {
        return selectBuilder.getRecordById(tableName, id);
    }

    @Override
    public String getRecordsByCriteria(String tableName, Map<String, Object> criteriaJson) {
        return selectBuilder.getRecordsByCriteria(tableName, criteriaJson);
    }

    // Core table operations
    @Override
    public String createTable(String tableName) {
        MSSQLUtils.validateInput(tableName, "Table Name");
        return "CREATE TABLE " + MSSQLUtils.escapeIdentifier(tableName) +
                " (ROW_ID NVARCHAR(255) PRIMARY KEY, JSON_ROW_DETAIL NVARCHAR(MAX)); " +
                "CREATE INDEX IDX_" + tableName.toUpperCase() + "_ROW_ID ON " + MSSQLUtils.escapeIdentifier(tableName)
                + " (ROW_ID)";
    }

    // Create view based on metadata from F_formDefinition
    @Override
    public String createView(String tableName, String metadataJson) {
        throw new UnsupportedOperationException("createView not yet implemented for MSSQL");
    }

    @Override
    public String checkTableExistence(String tableName) {
        MSSQLUtils.validateInput(tableName, "Table Name");
        return String.format("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '%s'", tableName);
    }

    // Record operations
    @Override
    public String insertRecord(String tableName, String id, String recordDetail) {
        MSSQLUtils.validateInput(id, "ID");
        MSSQLUtils.validateInput(recordDetail, "Record Detail");

        return new StringBuilder()
                .append("MERGE INTO ")
                .append(MSSQLUtils.escapeIdentifier(tableName))
                .append(" AS target USING (SELECT '")
                .append(id)
                .append("' AS ROW_ID, '")
                .append(recordDetail.replace("'", "''")) // Escape single quotes in the JSON detail
                .append("' AS JSON_ROW_DETAIL) AS source ")
                .append("ON target.ROW_ID = source.ROW_ID ")
                .append("WHEN MATCHED THEN UPDATE SET JSON_ROW_DETAIL = source.JSON_ROW_DETAIL ")
                .append("WHEN NOT MATCHED THEN INSERT (ROW_ID, JSON_ROW_DETAIL) VALUES (source.ROW_ID, source.JSON_ROW_DETAIL);")
                .toString();
    }

    @Override
    public String deleteRecordById(String tableName, String id) {
        MSSQLUtils.validateInput(id, "ID");
        return "DELETE FROM " + MSSQLUtils.escapeIdentifier(tableName) + " WHERE ROW_ID = ?";
    }

    // Query operations

    public String dropTable(String tableName) {
        return ("DROP TABLE IF EXISTS " + MSSQLUtils.escapeIdentifier(tableName));
    }

    // Utility methods

    // Exception class


    public static class InvalidCriteriaException extends RuntimeException {
        public InvalidCriteriaException(String message) {
            super(message);
        }

        public InvalidCriteriaException(String message, Throwable cause) {
            super(message, cause);
        }
    }

}
