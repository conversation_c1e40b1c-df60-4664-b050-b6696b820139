package com.ultimate.uff.validation;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ultimate.uff.uql.service.DatabaseService;

@Service
public class ReadOnlyDatabaseService {

    private final DatabaseService databaseService;

    @Autowired
    public ReadOnlyDatabaseService(DatabaseService databaseService) {
        this.databaseService = databaseService;
    }

    public Map<String, Object> getRecordById(String tableName, String id) {
        return databaseService.getRecordById(tableName, id);
    }

    public boolean checkRecordExistence(String tableName, String id) {
        return databaseService.checkRecordExistence(tableName, id);
    }

    public boolean checkTableExistence(String tableName) {
        return databaseService.checkTableExistence(tableName);
    }
    
    public void createTable(String tableName) {
        databaseService.createTable(tableName);
    }

    /**
     * Retrieves only the ROW_IDs matching the criteria as a List<String>.
     */
    public List<String> getRecordIdsByCriteria(String tableName, Map<String, Object> criteriaJson) {
        ArrayNode records = databaseService.getRecordsByCriteria(tableName, criteriaJson);

        List<String> result = new java.util.ArrayList<>();

        for (int i = 0; i < records.size(); i++) {
            String rowId = records.get(i).get("ROW_ID").asText();
            result.add(rowId);
        }

        return result;
    }
}
