package com.ultimate.uff.validation;

import java.util.Map;
/**
 * Interface for applying custom validation, defaulting, and authorization logic per table/form.
 * This allows extending system behavior on a per-record basis without modifying the core engine.
 */
public interface CustomValidator {

    /**
     * Performs a basic validation check and returns a single error message if invalid.
     * This method is optional and can be overridden.
     *
     * @param id           the ID of the record
     * @param recordDetail the data of the record being validated
     * @return error message as a String or null if valid
     */
	default String validate(String id, Map<String, Object> recordDetail) {
        return null; // Default does nothing
    }

    /**
     * Allows injecting or modifying data before record insertion.
     *
     * @param recordDetail the original record data
     * @return modified or enriched record data
     */
    default Map<String, Object> newData(Map<String, Object> recordDetail) {
        // Default does nothing and returns the original map
        return recordDetail;
    }
    
    /**
     * Executes custom authorization logic after validation and before final commit.
     *
     * @param id           the ID of the record
     * @param recordDetail the full record data
     */
    default void authorize(String id,  Map<String, Object> recordDetail) {
    }
    
    /**
     * Performs structured validation and returns field-level error details.
     *
     * @param id           the ID of the record
     * @param recordDetail the record data
     * @return a ValidationResponse object containing validation result and field errors
     */
    default ValidationResponse validateWithResponse(String id, Map<String, Object> recordDetail) {
        return new ValidationResponse(); // default returns no errors
    }
    // You can add any additional methods here with default implementations
}
