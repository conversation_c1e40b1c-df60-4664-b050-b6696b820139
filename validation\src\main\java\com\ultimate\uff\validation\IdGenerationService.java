package com.ultimate.uff.validation;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

/**
 * ID Generation helper service based on the idGenerator configuration table.
 */
@Service
public class IdGenerationService {

    private static final Random random = new Random();

    public void generateAndSetNextId(Map<String, Object> record, String tableName) {
        if (!"idGenerator".equals(tableName)) return;

        String idMode = (String) record.getOrDefault("idMode", "INCREMENTAL");
        String prefix = (String) record.getOrDefault("prefix", "");
        String date = (String) record.getOrDefault("date", "");
        String dateFormat = (String) record.getOrDefault("dateFormat", "yyyyMMdd");
        String lastSeq = (String) record.getOrDefault("lastSeq", "0");
        String randomLengthStr = (String) record.getOrDefault("randomLength", "6");
        String allowedChars = (String) record.getOrDefault("allowedChars", "0123456789");

        int last = parseInt(lastSeq, 0);
        int randomLength = parseInt(randomLengthStr, 6);

        String nextId;
        switch (idMode.toUpperCase()) {
            case "INCREMENTAL":
                last += 1;
                record.put("lastSeq", String.valueOf(last));
                nextId = formatId(prefix, date, dateFormat, String.format("%04d", last));
                break;
            case "RANDOM":
                nextId = formatId(prefix, date, dateFormat, generateRandom(randomLength, allowedChars));
                break;
            case "UUID":
                nextId = prefix + UUID.randomUUID().toString();
                break;
            case "TIMESTAMP":
                nextId = prefix + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
                break;
            default:
                throw new IllegalArgumentException("Unsupported idMode: " + idMode);
        }

        record.put("nextId", nextId);
        record.put("lastGenId", nextId);
    }

    private String formatId(String prefix, String date, String dateFormat, String seqPart) {
        String formattedDate = date != null && !date.isEmpty()
                ? date
                : LocalDateTime.now().format(DateTimeFormatter.ofPattern(dateFormat));
        return prefix + formattedDate + seqPart;
    }

    private String generateRandom(int length, String chars) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    private int parseInt(String value, int defaultVal) {
        try {
            return Integer.parseInt(value);
        } catch (Exception e) {
            return defaultVal;
        }
    }
}
