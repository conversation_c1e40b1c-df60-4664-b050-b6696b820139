package com.ultimate.uff.uql.model;

import java.util.Arrays;
import java.util.List;

import com.ultimate.uff.uql.model.enums.FieldStructureType;

public class ParsedFilter {
    private final String field; // e.g. "transactions.itemCode"
    private final String baseField; // e.g. "transactions"
    private final String type; // e.g. "STRING", "INT"
    private final FieldStructureType structure;
    private final String operator; // e.g. "EQ"
    private final String value; // e.g. "SKU-001"
    private final String nestedField;
    //private List<String> groupPathPartsCache = null;

    public ParsedFilter(String field, String baseField, String type, FieldStructureType structure, String operator,
            String value) {
        this.field = field;
        this.baseField = baseField;
        this.type = type;
        this.structure = structure;
        this.operator = operator;
        this.value = value;
        this.nestedField = normalizeNestedPath(field.substring(baseField.length()));
    }

    public String getField() {
        return field;
    }

    public String getBaseField() {
        return baseField;
    }

    public String getType() {
        return type;
    }

    public FieldStructureType getStructure() {
        return structure;
    }

    public String getOperator() {
        return operator;
    }

    public String getValue() {
        return value;
    }

    public String getNestedField() {
        return nestedField;
    }    

    public List<String> getGroupPathParts() {
        if (this.field == null || !this.field.contains("[")) {
            throw new IllegalStateException("Not a valid group field format: " + this.field);
        }

        // Replace all occurrences of [] with a safe marker, then split
        String normalized = this.field.replace("[]", "#");
        String[] parts = normalized.split("#\\.?");

        if (parts.length < 2) {
            throw new IllegalStateException("Not a valid group field format: " + this.field);
        }

        return Arrays.stream(parts).map(String::trim).toList();
    }

    public static String normalizeNestedPath(String path) {
        if (path == null || path.isEmpty()) return "";
        
        // Remove leading "[]" or "." if present
        if (path.startsWith("[]")) {
            path = path.substring(2);
        }
        if (path.startsWith(".")) {
            path = path.substring(1);
        }
    
        // Remove all remaining "[]" and clean accidental spaces
        String[] parts = path.split("\\.");
        return Arrays.stream(parts)
                .map(part -> part.replace("[]", "").trim()) // Remove any [] inside and trim
                .filter(part -> !part.isEmpty())             // Skip any empty parts (safe guard)
                .reduce((a, b) -> a + "." + b)                // Join back with .
                .orElse("");
    }

    public boolean isLastFieldMulti() {
        return field != null && field.contains("[]") && field.endsWith("[]");
    }    
    
}
