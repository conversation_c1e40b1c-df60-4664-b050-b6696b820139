package com.ultimate.uff.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ultimate.uff.validation.ReadOnlyDatabaseService;

import java.util.List;
import java.util.Map;

/**
 * Controller for read-only database access.
 * Supports both GET (no criteria) and POST (with criteria) for fetching ROW_IDs.
 */
@RestController
@RequestMapping("/api/recordsss")
public class ReadOnlyDatabaseController {

    private final ReadOnlyDatabaseService readOnlyDatabaseService;

    @Autowired
    public ReadOnlyDatabaseController(ReadOnlyDatabaseService readOnlyDatabaseService) {
        this.readOnlyDatabaseService = readOnlyDatabaseService;
    }

    /**
     * Retrieves all record ROW_IDs from the specified table without any criteria.
     *
     * Example:
     * GET /api/recordsss/customer
     *
     * @param tableName the table name
     * @return list of ROW_IDs
     */
    @GetMapping("/{tableName}")
    public List<String> getRecordIds(
            @PathVariable String tableName
    ) {
        return readOnlyDatabaseService.getRecordIdsByCriteria(tableName, Map.of());
    }

    /**
     * Retrieves record ROW_IDs based on provided criteria (JSON body).
     *
     * Example:
     * POST /api/recordsss/customer
     * Body:
     * {
     *   "AND": [
     *     { "status": { "EQ": "active" } }
     *   ]
     * }
     *
     * @param tableName the table name
     * @param criteria the JSON-style search criteria
     * @return list of matching ROW_IDs
     */
    @PostMapping("/{tableName}")
    public List<String> getRecordIdsByCriteria(
            @PathVariable String tableName,
            @RequestBody(required = false) Map<String, Object> criteria
    ) {
        if (criteria == null) {
            criteria = Map.of(); // Use empty map if no criteria provided
        }
        return readOnlyDatabaseService.getRecordIdsByCriteria(tableName, criteria);
    }
}
