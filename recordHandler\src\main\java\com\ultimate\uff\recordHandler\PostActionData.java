package com.ultimate.uff.recordHandler;

import java.util.Map;

public class PostActionData {
    private String table;
    private String function;
    private String id;
    private int AuthNum;
    private Map<String, Object> body;

    public String getTable() { return table; }
    public void setTable(String table) { this.table = table; }

    public String getFunction() { return function; }
    public void setFunction(String function) { this.function = function; }

    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public int getAuthNum() { return AuthNum; }
    public void setAuthNum(int AuthNum) { this.AuthNum = AuthNum; }

    public Map<String, Object> getBody() { return body; }
    public void setBody(Map<String, Object> body) { this.body = body; }
}
