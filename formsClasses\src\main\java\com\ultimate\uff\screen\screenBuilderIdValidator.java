package com.ultimate.uff.screen;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.CustomIdValidator;

@Component
public class screenBuilderIdValidator implements CustomIdValidator {

 
    @Autowired
    private DatabaseService databaseService;
    @Override
    public String validate(String id) {
        // Extract potential table name from ID
        String[] idParts = id.split(",");
        if (idParts.length < 2) {
            return "ID must be in the format of <table_name>,<custom_string>";
        }


        // Validate the ID field

        Boolean tableExistance = databaseService.checkRecordExistence("formDefinition", idParts[0]);


        if (tableExistance.equals(false)) {
            return "ID must start with an existing table name followed by a comma and then any string";
        }

        return "Validation successful";
    }
}
