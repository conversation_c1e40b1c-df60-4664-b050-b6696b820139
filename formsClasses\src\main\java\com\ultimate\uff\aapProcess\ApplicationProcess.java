package com.ultimate.uff.aapProcess;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ApplicationProcess {

    // Fields from the fieldName array
    private List<String> recordValidation;
    private List<String> nauSave;
    private List<String> idValidation;
    private List<String> defultation;
    private List<String> afterAuth;

    // Metadata fields
    private String recordInputter;
    private String recordAuthorizer;
    private int recordCount;
    private String dateTime;

    // Map to store dynamic/extra fields
    private Map<String, Object> extraFields = new HashMap<>();

    // Constructor that initializes fields from a map
    public ApplicationProcess(Map<String, Object> recordDetail) {
        this.recordValidation = (List<String>) recordDetail.get("recordValidation");
        this.nauSave = (List<String>) recordDetail.get("nauSave");
        this.idValidation = (List<String>) recordDetail.get("idValidation");
        this.defultation = (List<String>) recordDetail.get("defultation");
        this.afterAuth = (List<String>) recordDetail.get("afterAuth");

        this.recordInputter = (String) recordDetail.get("recordInputter");
        this.recordAuthorizer = (String) recordDetail.get("recordAuthorizer");
        this.recordCount = (recordDetail.get("recordCount") != null) ? (Integer) recordDetail.get("recordCount") : 0;
        this.dateTime = (String) recordDetail.get("dateTime");

        // Store any extra fields not explicitly handled
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    // Method to check if a field is explicitly handled
    private boolean isFieldHandled(String fieldName) {
        return fieldName.equals("recordValidation") || fieldName.equals("nauSave") || 
               fieldName.equals("idValidation") || fieldName.equals("defultation") || 
               fieldName.equals("afterAuth") || fieldName.equals("recordInputter") || 
               fieldName.equals("recordAuthorizer") || fieldName.equals("recordCount") || 
               fieldName.equals("dateTime");
    }

    // Getters and setters
    public List<String> getRecordValidation() {
        return recordValidation;
    }

    public void setRecordValidation(List<String> recordValidation) {
        this.recordValidation = recordValidation;
    }

    public List<String> getNauSave() {
        return nauSave;
    }

    public void setNauSave(List<String> nauSave) {
        this.nauSave = nauSave;
    }

    public List<String> getIdValidation() {
        return idValidation;
    }

    public void setIdValidation(List<String> idValidation) {
        this.idValidation = idValidation;
    }

    public List<String> getDefultation() {
        return defultation;
    }

    public void setDefultation(List<String> defultation) {
        this.defultation = defultation;
    }

    public List<String> getAfterAuth() {
        return afterAuth;
    }

    public void setAfterAuth(List<String> afterAuth) {
        this.afterAuth = afterAuth;
    }

    public String getRecordInputter() {
        return recordInputter;
    }

    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
    }

    public String getRecordAuthorizer() {
        return recordAuthorizer;
    }

    public void setRecordAuthorizer(String recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
    }

    public int getRecordCount() {
        return recordCount;
    }

    public void setRecordCount(int recordCount) {
        this.recordCount = recordCount;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public Map<String, Object> getExtraFields() {
        return extraFields;
    }

    public void setExtraFields(Map<String, Object> extraFields) {
        this.extraFields = extraFields;
    }
}
