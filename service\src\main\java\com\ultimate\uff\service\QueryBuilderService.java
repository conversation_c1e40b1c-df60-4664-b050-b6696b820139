package com.ultimate.uff.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.RecordInsertionService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class QueryBuilderService {

    @Autowired
    private DatabaseService DatabaseService;
    


    public List<Map<String, Object>> getQueryBuilderRecordById(String queryBuilderId) {

        Map<String, Object> queryBuilderRecord = DatabaseService.getRecordById("query_builder", queryBuilderId);
        if (queryBuilderRecord == null) return null;

        String fileName = (String) queryBuilderRecord.get("fileName");
        Map<String, Object> tableMetaData = DatabaseService.getRecordById("formDefinition", fileName);

        Map<String, Map<String, Object>> fieldMetaMap = new HashMap<>();
        if (tableMetaData != null && tableMetaData.containsKey("fieldName")) {
            List<Map<String, Object>> fields = (List<Map<String, Object>>) tableMetaData.get("fieldName");
            for (Map<String, Object> field : fields) {
                String fieldName = (String) field.get("fieldName");
                fieldMetaMap.put(fieldName, field);
            }
        }

        List<Map<String, Object>> selectionFields = (List<Map<String, Object>>) queryBuilderRecord.get("selectionFields");
        if (selectionFields == null) return null;

        List<Map<String, Object>> formattedFields = new ArrayList<>();
        for (Map<String, Object> field : selectionFields) {
            String fieldName = (String) field.get("selectionField");
            String operator = (String) field.get("operator");

            Map<String, Object> meta = fieldMetaMap.get(fieldName);
            if (meta == null) continue;

            String type = (String) meta.getOrDefault("type", "UNKNOWN");
            boolean isMulti = Boolean.TRUE.equals(meta.get("isMulti"));
            String group = (String) meta.get("Group");

            StringBuilder sb = new StringBuilder();

            // Process group path if present
            if (group != null) {
                String[] parts = group.split("\\|");
                for (String part : parts) {
                    sb.append(part).append("[]").append(".");
                }
            }

            sb.append(fieldName);
            if (isMulti) {
                sb.append("[]");
            }

            if (!"string".equalsIgnoreCase(type)) {
                sb.append(":").append(type.toUpperCase());
            }

            Map<String, Object> formattedField = new HashMap<>();
            formattedField.put("selectionField", sb.toString());
            formattedField.put("operator", operator);

            formattedFields.add(formattedField);
        }

        return formattedFields;
    }

     public ArrayNode searchRecords(String queryBuilderId,Map<String, Object>  criteria) {
        Map<String, Object> queryBuilderRecord = DatabaseService.getRecordById("query_builder", queryBuilderId);

        // Check if the record was found
        if (queryBuilderRecord == null) {
            return null; // or you can throw an exception if preferred
        }

        String fileName = (String) queryBuilderRecord.get("fileName");

        return DatabaseService.getRecordsByCriteria(fileName, criteria);
    }
}
