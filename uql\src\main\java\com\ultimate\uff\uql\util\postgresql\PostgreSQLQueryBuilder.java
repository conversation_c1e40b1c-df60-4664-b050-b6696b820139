package com.ultimate.uff.uql.util.postgresql;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.ultimate.uff.uql.util.QueryBuilder;

import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

@Component
// @Profile("postgresql")
@ConditionalOnProperty(name = "spring.datasource.driver-class-name", havingValue = "org.postgresql.Driver")
public class PostgreSQLQueryBuilder implements QueryBuilder {

    // Core table operations
    @Override
    public String createTable(String tableName) {
        validateInput(tableName, "Table Name");
        return new StringBuilder()
                .append("CREATE TABLE ")
                .append(escapeIdentifier(tableName))
                .append(" (ROW_ID VARCHAR(255) PRIMARY KEY, JSON_ROW_DETAIL TEXT); ")
                .append("CREATE INDEX IDX_")
                .append(tableName.toUpperCase())
                .append("_ROW_ID ON ")
                .append(escapeIdentifier(tableName))
                .append(" (ROW_ID);")
                .toString();
    }

    // Create view based on metadata from F_formDefinition for PostgreSQL
    @Override
    public String createView(String tableName, String metadataJson) {
        throw new UnsupportedOperationException("createView not yet implemented for PostgreSQL");
    }

    @Override
    public String checkTableExistence(String tableName) {
        validateInput(tableName, "Table Name");
        return "SELECT COUNT(*) FROM information_schema.tables WHERE upper(table_name) = '" + tableName.toUpperCase()
                + "'";
    }

    // Record operations
    @Override
    public String insertRecord(String tableName, String id, String recordDetail) {
        validateInput(id, "ID");
        validateInput(recordDetail, "Record Detail");
        return new StringBuilder()
                .append("INSERT INTO ")
                .append(escapeIdentifier(tableName))
                .append(" (ROW_ID, JSON_ROW_DETAIL) VALUES ('")
                .append(id.replace("'", "''")) // Escape single quotes in the ID
                .append("', '")
                .append(recordDetail.replace("'", "''")) // Escape single quotes in the JSON detail
                .append("') ON CONFLICT (ROW_ID) ")
                .append("DO UPDATE SET JSON_ROW_DETAIL = EXCLUDED.JSON_ROW_DETAIL;")
                .toString();
    }

    @Override
    public String deleteRecordById(String tableName, String id) {
        validateInput(id, "ID");
        return "DELETE FROM " + escapeIdentifier(tableName) + " WHERE ROW_ID = '" + id.replace("'", "''") + "'";
    }

    @Override
    public String getRecordById(String tableName, String id) {
        validateInput(id, "ID");

        return "SELECT JSON_ROW_DETAIL FROM " + escapeIdentifier(tableName) + " WHERE ROW_ID = '"
                + id.replace("'", "''") + "'";

    }

    // Query operations
    @Override
    public String getRecordsByCriteria(String tableName, Map<String, Object> criteria) {
        throw new UnsupportedOperationException("getRecordsByCriteria not yet implemented for PostgreSQL");
    }

    public String dropTable(String tableName) {
        return ("DROP TABLE IF EXISTS " + escapeIdentifier(tableName));
    }

    // PostgreSQL-specific criteria parsing
    private String parseCriteria(Map<String, Object> criteriaMap) {
        StringBuilder parsedCriteria = new StringBuilder();

        criteriaMap.forEach((field, value) -> {
            String operator;
            String operand;

            // Assuming value is passed in the format "operator operand", e.g., "EQ XYZ"
            if (value instanceof String) {
                String[] parts = ((String) value).split(" ", 2);
                if (parts.length < 2) {
                    throw new IllegalArgumentException("Invalid criteria format for field: " + field);
                }
                operator = parts[0].toUpperCase();
                operand = parts[1];
            } else {
                // If the value is not a String, use EQ as the default operator
                operator = "EQ";
                operand = value.toString();
            }

            // Generate JSON path for the field
            String jsonPath = buildPostgresJsonPath(field);

            switch (operator) {
                case "EQ":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" = '").append(operand.replace("'", "''")).append("' AND ");
                    break;
                case "NE":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" != '").append(operand.replace("'", "''")).append("' AND ");
                    break;
                case "GT":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" > '").append(operand.replace("'", "''")).append("' AND ");
                    break;
                case "GE":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" >= '").append(operand.replace("'", "''")).append("' AND ");
                    break;
                case "LT":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" < '").append(operand.replace("'", "''")).append("' AND ");
                    break;
                case "LE":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" <= '").append(operand.replace("'", "''")).append("' AND ");
                    break;
                case "RG":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" BETWEEN '").append(operand.replace("'", "''"))
                            .append("' AND ?").append(" AND ");
                    break;
                case "NR":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" NOT BETWEEN '").append(operand.replace("'", "''"))
                            .append("' AND ?").append(" AND ");
                    break;
                case "CT":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" LIKE '%").append(operand.replace("'", "''")).append("%' AND ");
                    break;
                case "NC":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" NOT LIKE '%").append(operand.replace("'", "''")).append("%' AND ");
                    break;
                case "BW":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" LIKE '").append(operand.replace("'", "''")).append("%' AND ");
                    break;
                case "EW":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" LIKE '%").append(operand.replace("'", "''")).append("' AND ");
                    break;
                case "LK":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" LIKE '").append(operand.replace("'", "''")).append("' AND ");
                    break;
                case "DNBW":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" NOT LIKE '").append(operand.replace("'", "''")).append("%' AND ");
                    break;
                case "DNEW":
                    parsedCriteria.append("(JSON_ROW_DETAIL::jsonb)").append(jsonPath)
                            .append(" NOT LIKE '%").append(operand.replace("'", "''")).append("' AND ");
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported operator: " + operator);
            }
        });

        // Remove the trailing " AND " if any criteria were added
        if (parsedCriteria.length() > 0) {
            parsedCriteria.setLength(parsedCriteria.length() - 5);
        }

        return parsedCriteria.toString();
    }

    // Utility methods
    private void validateInput(String input, String fieldName) {
        if (input == null || input.trim().isEmpty()) {
            throw new InvalidCriteriaException(fieldName + " cannot be null or empty.");
        }
    }

    private void validateFields(List<String> fields) {
        if (fields == null || fields.isEmpty()) {
            throw new InvalidCriteriaException("Fields list cannot be null or empty.");
        }
    }

    private void validateLimitOffset(int limit, int offset) {
        if (limit <= 0 || offset < 0) {
            throw new InvalidCriteriaException("Limit must be greater than 0 and offset cannot be negative.");
        }
    }

    private String escapeIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            throw new InvalidCriteriaException("Identifier cannot be null or empty.");
        }
        return "\"" + identifier.replace("\"", "\"\"") + "\"";
    }

    // Exception class
    public static class InvalidCriteriaException extends RuntimeException {
        public InvalidCriteriaException(String message) {
            super(message);
        }

        public InvalidCriteriaException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    private String buildPostgresJsonPath(String field) {
        String[] pathElements = field.split("\\.");
        StringBuilder jsonPath = new StringBuilder();
        for (String element : pathElements) {
            if (element.matches(".*\\[\\d+\\]")) { // Handling array elements
                String arrayElement = "'" + element.replaceAll("\\[", "'->").replaceAll("\\]", "");
                jsonPath.append("->").append(arrayElement);
            } else {
                jsonPath.append("->>'").append(element).append("'");
            }
        }
        return jsonPath.toString();
    }

}
