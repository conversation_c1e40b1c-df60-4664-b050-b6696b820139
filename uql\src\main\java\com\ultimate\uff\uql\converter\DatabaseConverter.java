package com.ultimate.uff.uql.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.uql.util.QueryBuilder;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.concurrent.TimeUnit;

public class DatabaseConverter {
/* 
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        System.out.println("=======================================");
        System.out.println("  Welcome to the Database Converter!   ");
        System.out.println("=======================================");
        System.out.println("This wizard will guide you through the process of converting data between databases.");
        System.out.println("Press Enter to continue...");
        scanner.nextLine();

        try {
            // Step 1: Ask the user what operations they want to perform
            boolean createTables = getUserConfirmation(scanner, "Create Tables? (Yes/No, Default: Yes): ");
            boolean insertRecords = getUserConfirmation(scanner, "Insert Records? (Yes/No, Default: Yes): ");
            boolean createViews = getUserConfirmation(scanner, "Create Views? (Yes/No, Default: Yes): ");

            // Step 2: Collect and test source database connection
            String sourceDbType;
            DataSource sourceDataSource;
            while (true) {
                try {
                    System.out.println("Enter Source Database Details:");
                    sourceDbType = getDatabaseType(scanner, "source");
                    String sourceDbUrl = getDatabaseDetail(scanner, "source", "URL");
                    String sourceUsername = getDatabaseDetail(scanner, "source", "username");
                    String sourcePassword = getDatabaseDetail(scanner, "source", "password");

                    sourceDataSource = createDataSource(sourceDbUrl, sourceUsername, sourcePassword, sourceDbType);
                    testConnection(sourceDataSource, "Source");
                    break;
                } catch (SQLException e) {
                    System.err.println("Source database connection failed: " + e.getMessage());
                    System.out.println("Please re-enter the source database details.");
                }
            }

            // Step 3: Collect and test target database connection
            String targetDbType;
            DataSource targetDataSource;
            while (true) {
                try {
                    System.out.println("Enter Target Database Details:");
                    targetDbType = getDatabaseType(scanner, "target");
                    String targetDbUrl = getDatabaseDetail(scanner, "target", "URL");
                    String targetUsername = getDatabaseDetail(scanner, "target", "username");
                    String targetPassword = getDatabaseDetail(scanner, "target", "password");

                    targetDataSource = createDataSource(targetDbUrl, targetUsername, targetPassword, targetDbType);
                    testConnection(targetDataSource, "Target");
                    break;
                } catch (SQLException e) {
                    System.err.println("Target database connection failed: " + e.getMessage());
                    System.out.println("Please re-enter the target database details.");
                }
            }

            // Step 4: Start timing after all inputs are collected
            long startTime = System.currentTimeMillis();
            String startDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            System.out.println("Start Time: " + startDateTime);

            // Step 5: Create services
            QueryBuilder sourceQueryBuilder = getQueryBuilder(sourceDbType);
            QueryBuilder targetQueryBuilder = getQueryBuilder(targetDbType);
            ObjectMapper objectMapper = new ObjectMapper();
            DatabaseService sourceDatabaseService = new DatabaseService(sourceQueryBuilder, new JdbcTemplate(sourceDataSource), objectMapper);
            DatabaseService targetDatabaseService = new DatabaseService(targetQueryBuilder, new JdbcTemplate(targetDataSource), objectMapper);

            // Fetch table names
            List<String> fieldsToFetch = List.of("ROW_ID");
            ArrayNode tableNamesNode = sourceDatabaseService.getFieldsValuesByCriteria("formDefinition", fieldsToFetch, "");
            List<String> tableNames = convertArrayNodeToListOfStrings(tableNamesNode);
            int totalTables = tableNames.size();
            System.out.println("Total tables to be created: " + totalTables);

            // Create a log file with the current date and time
            String logFileName = "DatabaseConversionLog_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".txt";
            FileWriter logWriter = new FileWriter(logFileName);
            int errors = 0;

            // Log the start time
            logWriter.write("Start Time: " + startDateTime + "\n");

            // Create Tables
            if (createTables) {
                int tablesCreated = 0;
                for (String tableName : tableNames) {
                    try {
                        targetDatabaseService.createTable(tableName);
                        tablesCreated++;
                        System.out.println("Created Table in target DB: " + tablesCreated + "/" + totalTables);
                    } catch (Exception e) {
                        errors++;
                        logError(logWriter, "Error creating table " + tableName + ": " + e.getMessage());
                    }
                }
                System.out.println("Table creation completed with " + errors + " errors.");
            }

            // Insert Records
            if (insertRecords) {
                for (String tableName : tableNames) {
                    try {
                        List<String> IDsValue = List.of("ROW_ID");
                        ArrayNode IDsNode = sourceDatabaseService.getFieldsValuesByCriteria(tableName, IDsValue, "");
                        List<Map<String, Object>> IDs = convertArrayNodeToListOfMaps(IDsNode);
                        int totalRecords = IDs.size();
                        int recordsInserted = 0;

                        for (Map<String, Object> record : IDs) {
                            try {
                                String id = (String) record.get("ROW_ID");
                                Map<String, Object> recordDetail = sourceDatabaseService.getRecordById(tableName, id);
                                String recordDetailJson = objectMapper.writeValueAsString(recordDetail);
                                targetDatabaseService.insertRecord(tableName, id, recordDetailJson);
                                recordsInserted++;
                                System.out.print("\r" + tableName + " Inserting (" + recordsInserted + "/" + totalRecords + ")");
                            } catch (Exception e) {
                                errors++;
                                logError(logWriter, "Error inserting record in table " + tableName + ": " + e.getMessage());
                            }
                        }
                        System.out.println();
                    } catch (Exception e) {
                        errors++;
                        logError(logWriter, "Error fetching records for table " + tableName + ": " + e.getMessage());
                    }
                }
            }

            // Create Views
            if (createViews) {
                for (String tableName : tableNames) {
                    try {
                        targetDatabaseService.createView(tableName);
                        System.out.println("Created view for table: " + tableName);
                    } catch (Exception e) {
                        errors++;
                        logError(logWriter, "Error creating view for table " + tableName + ": " + e.getMessage());
                    }
                }
            }

            // Calculate and log the time spent
            long endTime = System.currentTimeMillis();
            String endDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
            long timeSpentMillis = endTime - startTime;
            String timeSpent = formatDuration(timeSpentMillis);
            System.out.println("Completion Time: " + endDateTime);
            System.out.println("Time Spent: " + timeSpent);

            // Log the end time and time spent
            logWriter.write("Completion Time: " + endDateTime + "\n");
            logWriter.write("Time Spent: " + timeSpent + "\n");
            logWriter.write("Database conversion completed with " + errors + " total errors.\n");
            logWriter.close();
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
        } finally {
            scanner.close();
        }
    }

    // Helper method to get user confirmation with a default "Yes"
    private static boolean getUserConfirmation(Scanner scanner, String message) {
        System.out.print(message);
        String input = scanner.nextLine().trim().toUpperCase();
        return input.isEmpty() || input.equals("YES");
    }

    // Helper method to format duration in a readable format
    private static String formatDuration(long millis) {
        long hours = TimeUnit.MILLISECONDS.toHours(millis);
        long minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % 60;
        long seconds = TimeUnit.MILLISECONDS.toSeconds(millis) % 60;
        return String.format("%02d hours, %02d minutes, %02d seconds", hours, minutes, seconds);
    }

    // Helper methods for user input
    private static String getDatabaseType(Scanner scanner, String dbType) {
        System.out.print("Choose the " + dbType + " database type (MSSQL, ORACLE, POSTGRESQL) (Default: MSSQL): ");
        String type = scanner.nextLine().trim().toUpperCase();
        return type.isEmpty() ? "MSSQL" : type;
    }

    private static String getDatabaseDetail(Scanner scanner, String dbType, String detail) {
        System.out.print("Enter the " + dbType + " database " + detail + ": ");
        return scanner.nextLine();
    }

    // Method to create a DataSource
    private static DataSource createDataSource(String url, String username, String password, String dbType) {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        switch (dbType) {
            case "MSSQL":
                dataSource.setDriverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                break;
            case "ORACLE":
                dataSource.setDriverClassName("oracle.jdbc.OracleDriver");
                break;
            case "POSTGRESQL":
                dataSource.setDriverClassName("org.postgresql.Driver");
                break;
            default:
                throw new IllegalArgumentException("Unsupported database type: " + dbType);
        }
        return dataSource;
    }

    // Method to test the database connection
    private static void testConnection(DataSource dataSource, String dbName) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            System.out.println(dbName + " database connection successful.");
        } catch (SQLException e) {
            throw new SQLException(dbName + " database connection failed: " + e.getMessage(), e);
        }
    }

    // Method to log errors to a file
    private static void logError(FileWriter logWriter, String errorMessage) {
        try {
            logWriter.write(errorMessage + "\n");
            logWriter.flush();
        } catch (IOException e) {
            System.err.println("Error writing to log file: " + e.getMessage());
        }
    }

    private static QueryBuilder getQueryBuilder(String dbType) {
        switch (dbType) {
            case "MSSQL":
                return new MSSQLQueryBuilder();
            case "ORACLE":
                return new OracleQueryBuilder();
            case "POSTGRESQL":
                return new PostgreSQLQueryBuilder();
            default:
                throw new IllegalArgumentException("Unsupported database type: " + dbType);
        }
    }

    // Helper method to convert ArrayNode to List<String>
    private static List<String> convertArrayNodeToListOfStrings(ArrayNode arrayNode) {
        List<String> list = new ArrayList<>();
        for (JsonNode node : arrayNode) {
            // Extract the "ROW_ID" field value and add it to the list
            String rowId = node.get("ROW_ID").asText();
            list.add(rowId);
        }
        return list;
    }

    // Helper method to convert ArrayNode to List<Map<String, Object>>
    private static List<Map<String, Object>> convertArrayNodeToListOfMaps(ArrayNode arrayNode) {
        List<Map<String, Object>> list = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        for (JsonNode node : arrayNode) {
            Map<String, Object> map = objectMapper.convertValue(node, new TypeReference<Map<String, Object>>() {
            });
            list.add(map);
        }
        return list;
    }
        */
}