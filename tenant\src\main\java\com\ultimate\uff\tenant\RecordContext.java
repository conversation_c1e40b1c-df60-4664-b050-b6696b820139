package com.ultimate.uff.tenant;

public class RecordContext {

    private static final ThreadLocal<String> currentCompany = new ThreadLocal<>();
    private static final ThreadLocal<String> primaryCompany = new ThreadLocal<>();
    private static final ThreadLocal<Boolean> isTenantBased = new ThreadLocal<>();

    public static void set(String current, String primary, boolean tenantBased) {
        currentCompany.set(current);
        primaryCompany.set(primary);
        isTenantBased.set(tenantBased);
    }

    public static void setCompanies(String current, String primary) {
        currentCompany.set(current);
        primaryCompany.set(primary);
    }

    public static void setTenantBased(boolean tenantBased) {
        isTenantBased.set(tenantBased);
    }

    public static String getCurrentCompany() {
        return currentCompany.get();
    }

    public static String getPrimaryCompany() {
        return primaryCompany.get();
    }

    public static boolean isTenantBased() {
        Boolean flag = isTenantBased.get();
        return flag != null && flag;
    }

    public static void clear() {
        currentCompany.remove();
        primaryCompany.remove();
        isTenantBased.remove();
    }
}
