package com.ultimate.uff.account;

import com.ultimate.uff.customer.Customer;
import com.ultimate.uff.uql.service.DatabaseService;
import com.ultimate.uff.validation.CustomValidator;
import com.ultimate.uff.validation.ValidationResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class accountValidation implements CustomValidator {

    @Autowired
    private DatabaseService databaseService;

    @Override
    public ValidationResponse validateWithResponse(String id, Map<String, Object> recordDetail) {
        Account accRec = new Account(recordDetail);

       

        return accRec.getValidationResponse(); // ✅ returns only validation info
    }
}
