package com.ultimate.uff.screen;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.*;

public class ScreenBuilder extends ValidatableRecord {

    private static final Logger logger = LoggerFactory.getLogger(ScreenBuilder.class);
    private final Map<String, Object> extraFields = new HashMap<>();
    private final FieldValue<String> table = new FieldValue<>(null);
    private final FieldValue<String> screenDesc = new FieldValue<>(null);
    private final FieldValue<String> ID = new FieldValue<>(null);
    private final FieldValue<String> recordStatus = new FieldValue<>(null);
    private final FieldValue<String> recordInputter = new FieldValue<>(null);
    private final FieldValue<String> recordAuthorizer = new FieldValue<>(null);
    private final FieldValue<String> recordCount = new FieldValue<>(null);
    private final FieldValue<String> dateTime = new FieldValue<>(null);
    private final List<FieldName> fieldNameList = new ArrayList<>();
    private final List<DefaultFields> defaultFieldsList = new ArrayList<>();

    public ScreenBuilder() {
        logger.debug("Screen_builder created with empty constructor.");
    }

    public ScreenBuilder(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        logger.debug("Initializing Screen_builder from provided recordDetail.");
        table.setValue((String) recordDetail.get("table"));
        screenDesc.setValue((String) recordDetail.get("screenDesc"));
        ID.setValue((String) recordDetail.get("ID"));
        recordStatus.setValue((String) recordDetail.get("recordStatus"));
        recordInputter.setValue((String) recordDetail.get("recordInputter"));
        recordAuthorizer.setValue((String) recordDetail.get("recordAuthorizer"));
        recordCount.setValue((String) recordDetail.get("recordCount"));
        dateTime.setValue((String) recordDetail.get("dateTime"));
        Object fieldNameObj = recordDetail.get("fieldName");
        if (fieldNameObj instanceof List<?>) {
            for (Object item : (List<?>) fieldNameObj) {
                if (item instanceof Map) {
                    fieldNameList.add(new FieldName((Map<String, Object>) item));
                }
            }
        }
        Object defaultFieldsObj = recordDetail.get("defaultFields");
        if (defaultFieldsObj instanceof List<?>) {
            for (Object item : (List<?>) defaultFieldsObj) {
                if (item instanceof Map) {
                    defaultFieldsList.add(new DefaultFields((Map<String, Object>) item));
                }
            }
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) {
                extraFields.put(entry.getKey(), entry.getValue());
            }
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> recordDetail = new HashMap<>();
        recordDetail.put("table", table.getValue());
        recordDetail.put("screenDesc", screenDesc.getValue());
        recordDetail.put("ID", ID.getValue());
        recordDetail.put("recordStatus", recordStatus.getValue());
        recordDetail.put("recordInputter", recordInputter.getValue());
        recordDetail.put("recordAuthorizer", recordAuthorizer.getValue());
        recordDetail.put("recordCount", recordCount.getValue());
        recordDetail.put("dateTime", dateTime.getValue());
        List<Map<String, Object>> fieldNameMapList = new ArrayList<>();
        for (FieldName item : fieldNameList) {
            fieldNameMapList.add(item.toMap());
        }
        recordDetail.put("fieldName", fieldNameMapList);
        List<Map<String, Object>> defaultFieldsMapList = new ArrayList<>();
        for (DefaultFields item : defaultFieldsList) {
            defaultFieldsMapList.add(item.toMap());
        }
        recordDetail.put("defaultFields", defaultFieldsMapList);
        recordDetail.putAll(extraFields);
        logger.debug("Screen_builder serialized to map with {} fields.", recordDetail.size());
        return recordDetail;
    }

    private boolean isFieldHandled(String fieldName) {
        return switch (fieldName) {
            case "table", "fieldName", "size", "label", "row", "column", "defaultField", "defaultFields", "defaultValue", "screenDesc", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime" -> true;
            default -> false;
        };
    }

    public FieldValue<String> getTable() { return table; }
    public FieldValue<String> getScreenDesc() { return screenDesc; }
    public FieldValue<String> getID() { return ID; }
    public FieldValue<String> getRecordStatus() { return recordStatus; }
    public FieldValue<String> getRecordInputter() { return recordInputter; }
    public FieldValue<String> getRecordAuthorizer() { return recordAuthorizer; }
    public FieldValue<String> getRecordCount() { return recordCount; }
    public FieldValue<String> getDateTime() { return dateTime; }
    public List<FieldName> getFieldNameList() { return fieldNameList; }
    public List<DefaultFields> getDefaultFieldsList() { return defaultFieldsList; }
}