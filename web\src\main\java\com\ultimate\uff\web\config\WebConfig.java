package com.ultimate.uff.web.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web configuration for CORS and request interceptors.
 * Enables cross-origin requests and applies session validation via {@link SessionInterceptor}.
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(WebConfig.class);

    @Value("${cors.allowed.origins}")
    private String allowedOrigins;

    @Value("${cors.allowed.methods}")
    private String[] allowedMethods;

    @Value("${cors.allowed.headers}")
    private String[] allowedHeaders;

    @Value("${cors.allow.credentials}")
    private boolean allowCredentials;

    @Autowired
    private SessionInterceptor sessionInterceptor;

    /**
     * Configures CORS (Cross-Origin Resource Sharing) settings.
     * Allows frontend apps hosted on other domains to communicate with this backend.
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        logger.info("🌐 Configuring CORS settings:");
        logger.info("  🔸 Allowed Origins    : {}", allowedOrigins);
        logger.info("  🔸 Allowed Methods    : {}", String.join(", ", allowedMethods));
        logger.info("  🔸 Allowed Headers    : {}", String.join(", ", allowedHeaders));
        logger.info("  🔸 Allow Credentials  : {}", allowCredentials);

        registry.addMapping("/**")
                .allowedOrigins(allowedOrigins.split(","))
                .allowedMethods(allowedMethods)
                .allowedHeaders(allowedHeaders)
                .allowCredentials(allowCredentials);
    }

    /**
     * Registers the session interceptor for all secured API paths.
     * This ensures session validation for authenticated endpoints.
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(sessionInterceptor).addPathPatterns("/api/**");
        logger.info("✅ SessionInterceptor registered for /api/** endpoints.");
    }
}
