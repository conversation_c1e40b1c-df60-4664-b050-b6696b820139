package com.ultimate.uff.uql.service;

import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

@SpringBootTest(classes = com.ultimate.uff.main.UffMainApplication.class)
public class SelectClauseTests extends BaseCriteriaTest {

    @Test
    public void test_Select_SingleField() {
        runSelectTest("""
                    {
                      "_select": ["singleField"],
                      "singleField": { "EQ": "singleValue" }
                    }
                """, "singleField", "singleValue");
    }

    @Test
    public void test_Select_MultipleFields() {
        runSelectTest("""
                    {
                      "_select": ["singleField", "numericField:INT"],
                      "singleField": { "CT": "Value" }
                    }
                """, "singleField", "singleValue");
    }

    @Test
    public void test_Select_FieldWithTypeSuffix_INT() {
        runSelectTest("""
                    {
                      "_select": ["numericField:INT"],
                      "numericField:INT": { "EQ": "42" }
                    }
                """, "ROW_ID", "T1");
    }

    @Test
    public void test_Select_FieldWithTypeSuffix_DATE() {
        runSelectTest("""
                    {
                      "_select": ["createdDate:DATE"],
                      "createdDate:DATE": { "EQ": "2024-01-01" }
                    }
                """, "createdDate", "2024-01-01T00:00:00Z");
    }

    @Test
    public void test_Select_NestedArrayField() {
        runSelectTest("""
                    {
                      "_select": ["nestedArray[].nestedKey"],
                      "nestedArray[].nestedKey": { "EQ": "nestedValue2" }
                    }
                """, "nestedArray.nestedKey", "[\"nestedValue4\",\"nestedValue2\"]");
    }

    @Test
    public void test_Select_DeepNestedGroupField() {
        runSelectTest("""
                    {
                      "_select": ["nestedArray[].nestedKey[].subkey[].subofsub"],
                      "nestedArray[].nestedKey[].subkey[].subofsub": { "EQ": "DeepValue" }
                    }
                """, "nestedArray.nestedKey.subkey.subofsub", "[\"DeepValue\",\"OtherValue\"]");
    }

    @Test
    public void test_Select_Without_SelectClause_ShouldFallback() {
        runJsonTest("""
                    {
                      "singleField": { "EQ": "singleValue" }
                    }
                """, "singleField", "singleValue");
    }

    @Test
    public void test_Select_InvalidTypeSuffix_ShouldFallbackToString() {
      runSelectTest("""
                    {
                      "_select": ["singleField:INVALID"],
                      "singleField": { "EQ": "singleValue" }
                    }
                """, "singleField", "singleValue");
    }

    @Test
    public void test_Select_MultipleRowsReturned() {
        try {
            Map<String, Object> criteria = Map.of(
                    "_select", List.of("ROW_ID", "singleField"),
                    "singleField", Map.of("CT", "Value"));
            ArrayNode result = databaseService.getRecordsByCriteria(TEST_TABLE, criteria);

            assertTrue(result.size() >= 2, "Expected multiple matching rows");

            for (JsonNode row : result) {
                assertTrue(row.has("ROW_ID"), "Missing ROW_ID field in selected row");
                assertTrue(row.has("singleField"), "Missing singleField in selected row");
            }
        } catch (Exception e) {
            allTestsPassed = false;
            throw new RuntimeException("Error in test_Select_MultipleRowsReturned", e);
        }
    }

    @Test
    public void test_Select_NullFieldShouldBeHandled() {
      runSelectTest("""
                    {
                      "_select": ["ghostField"],
                      "singleField": { "EQ": "singleValue" }
                    }
                """, "ROW_ID", "T1"); // It should still succeed even if ghostField is NULL
    }

    @Test
    public void test_Select_FullArray_WithBrackets() {
        runSelectTest("""
                    {
                      "_select": ["multiField[]"],
                      "ROW_ID": { "EQ": "T1" }
                    }
                """, "multiField", "[\"value1\", \"value2\", \"value3\"]"); // JSON string check
    }

    @Test
    public void test_Select_FullNestedArray_WithBrackets() {
        runSelectTest("""
                    {
                      "_select": ["nestedArray[]"],
                      "ROW_ID": { "EQ": "T1" }
                    }
                """, "nestedArray", "[{\"nestedKey\":\"nestedValue1\", \"type\":\"typeA\"}]");
    }

    @Test
    public void test_Select_DeepNestedStructure_WithBrackets() {
        runSelectTest("""
                    {
                      "_select": ["nestedArray[]"],
                      "ROW_ID": { "EQ": "T8" }
                    }
                """, "nestedArray",
                "[{\"nestedKey\":[{\"subkey\":[{\"subofsub\":\"DeepValue\"},{\"subofsub\":\"OtherValue\"}]}]}]");
    }

    @Test
    public void test_Select_MixedFieldTypes() {
        runSelectTest("""
                    {
                      "_select": ["singleField", "multiField[]", "nestedArray[]"],
                      "ROW_ID": { "EQ": "T1" }
                    }
                """, "singleField", "singleValue");
    }

    @Test
    public void test_Select_SameFieldWithDifferentVariants() {
        runSelectTest("""
                    {
                      "_select": ["numericField", "numericField:INT"],
                      "ROW_ID": { "EQ": "T1" }
                    }
                """, "numericField", "42");
    }

    @Test
    public void test_Select_DeepMultiFieldWithoutBrackets() {
        runSelectTest("""
                    {
                      "_select": ["nestedArray.nestedKey"],
                      "ROW_ID": { "EQ": "T1" }
                    }
                """, "ROW_ID", "T1");
    }

}
