# =============================================================================
# Common Application Settings
# =============================================================================

# Application Name
spring.application.name=uff

# Always initialize SQL scripts on startup
spring.sql.init.mode=always

# =============================================================================
# Database Configuration
# =============================================================================
# Configure only one database at a time by uncommenting the relevant section

# -----------------------------------------
# Oracle Database Configuration
# -----------------------------------------
# spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
# spring.datasource.url=*********************************************
# spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect

# -----------------------------------------
# SQL Server Database Configuration
# -----------------------------------------
 spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
 #spring.datasource.url=***********************************************************************
 spring.datasource.url=*********************************************************************
 spring.jpa.database-platform=org.hibernate.dialect.SQLServerDialect

# -----------------------------------------
# PostgreSQL Database Configuration
# -----------------------------------------
#spring.datasource.driver-class-name=org.postgresql.Driver
#spring.datasource.url=****************************************
#spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Common Database Credentials (used by the active database configuration)
spring.datasource.username=uff
spring.datasource.password=omar1234

# =============================================================================
# Hibernate Settings
# =============================================================================

# Automatically update the database schema based on the entities
spring.jpa.hibernate.ddl-auto=update

# Show SQL statements in the logs
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# =============================================================================
# Logging Configuration
# =============================================================================

# Log the SQL statements generated by Hibernate
logging.level.org.hibernate.SQL=DEBUG

# Log the binding of parameters in SQL statements
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Log detailed information about Spring JDBC operations
logging.level.org.springframework.jdbc.core=DEBUG

# =============================================================================
# HikariCP Connection Pool Settings
# =============================================================================

# Maximum number of connections in the pool
spring.datasource.hikari.maximum-pool-size=10

# Minimum number of idle connections in the pool
spring.datasource.hikari.minimum-idle=5

# Maximum time that a connection can sit idle in the pool (in milliseconds)
spring.datasource.hikari.idle-timeout=30000

# Maximum lifetime of a connection in the pool (in milliseconds)
spring.datasource.hikari.max-lifetime=600000

# Maximum time to wait for a connection from the pool (in milliseconds)
spring.datasource.hikari.connection-timeout=60000

# Time threshold to report a potential connection leak (in milliseconds)
spring.datasource.hikari.leak-detection-threshold=5000
