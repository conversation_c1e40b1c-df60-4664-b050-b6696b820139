package com.ultimate.uff.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.ultimate.uff.service.QueryBuilderService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/query-builder")
public class QueryBuilderController {

    @Autowired
    private QueryBuilderService queryBuilderService;



    @GetMapping("/{queryBuilderId}")
    public ResponseEntity<List<Map<String, Object>>> getQueryBuilder(@PathVariable String queryBuilderId) {
        List<Map<String, Object>> selectionFields = queryBuilderService.getQueryBuilderRecordById(queryBuilderId);
        return ResponseEntity.ok(selectionFields);
    }


    @PostMapping("/search")
    public ResponseEntity<List<Map<String, Object>>> searchRecords(
        @RequestParam String queryBuilderId,
        @RequestBody(required = false) Map<String, Object> criteria
    ) {
        try {
            // Use empty map if criteria is null
            if (criteria == null) {
                criteria = new HashMap<>();
            }

            // Call the updated method
            ArrayNode results = queryBuilderService.searchRecords(queryBuilderId, criteria);

            // Convert ArrayNode to List<Map<String, Object>>
            ObjectMapper mapper = new ObjectMapper();
            List<Map<String, Object>> resultList = mapper.convertValue(results, new TypeReference<>() {});

            return ResponseEntity.ok(resultList);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

}
