package com.ultimate.uff.multi.thread.generatedClass;

import com.ultimate.uff.validation.FieldValue;
import com.ultimate.uff.validation.ValidatableRecord;
import java.util.*;

public class ServiceTasks extends ValidatableRecord {

    private final Map<String, Object> extraFields = new HashMap<>();
    private String serviceId;
    private final FieldValue<String> serviceIdVal = new FieldValue<>(null);
    private String taskName;
    private final FieldValue<String> taskNameVal = new FieldValue<>(null);
    private String description;
    private final FieldValue<String> descriptionVal = new FieldValue<>(null);
    private String status;
    private final FieldValue<String> statusVal = new FieldValue<>(null);
    private String ID;
    private final FieldValue<String> IDVal = new FieldValue<>(null);
    private String recordStatus;
    private final FieldValue<String> recordStatusVal = new FieldValue<>(null);
    private String recordInputter;
    private final FieldValue<String> recordInputterVal = new FieldValue<>(null);
    private List<String> recordAuthorizer = new ArrayList<>();
    private final FieldValue<List<String>> recordAuthorizerVal = new FieldValue<>(new ArrayList<>());
    private String recordCount;
    private final FieldValue<String> recordCountVal = new FieldValue<>(null);
    private String dateTime;
    private final FieldValue<String> dateTimeVal = new FieldValue<>(null);
    private final List<JobDetails> JobDetailsList = new ArrayList<>();

    public ServiceTasks() {}

    public ServiceTasks(Map<String, Object> recordDetail) {
        if (recordDetail == null) return;
        Object serviceIdObj = recordDetail.get("serviceId");
        if (serviceIdObj != null) {
            this.setServiceId(serviceIdObj.toString());
        }
        Object taskNameObj = recordDetail.get("taskName");
        if (taskNameObj != null) {
            this.setTaskName(taskNameObj.toString());
        }
        Object descriptionObj = recordDetail.get("description");
        if (descriptionObj != null) {
            this.setDescription(descriptionObj.toString());
        }
        Object statusObj = recordDetail.get("status");
        if (statusObj != null) {
            this.setStatus(statusObj.toString());
        }
        Object IDObj = recordDetail.get("ID");
        if (IDObj != null) {
            this.setID(IDObj.toString());
        }
        Object recordStatusObj = recordDetail.get("recordStatus");
        if (recordStatusObj != null) {
            this.setRecordStatus(recordStatusObj.toString());
        }
        Object recordInputterObj = recordDetail.get("recordInputter");
        if (recordInputterObj != null) {
            this.setRecordInputter(recordInputterObj.toString());
        }
        Object listObj = recordDetail.get("recordAuthorizer");
        if (listObj instanceof List<?>) {
            this.setRecordAuthorizer((List<String>) listObj);
        }
        Object recordCountObj = recordDetail.get("recordCount");
        if (recordCountObj != null) {
            this.setRecordCount(recordCountObj.toString());
        }
        Object dateTimeObj = recordDetail.get("dateTime");
        if (dateTimeObj != null) {
            this.setDateTime(dateTimeObj.toString());
        }
        Object JobDetailsObj = recordDetail.get("JobDetails");
        if (JobDetailsObj instanceof List<?>) {
            for (Object item : (List<?>) JobDetailsObj) {
                if (item instanceof Map) {
                    JobDetailsList.add(new JobDetails((Map<String, Object>) item));
                }
            }
        }
        for (Map.Entry<String, Object> entry : recordDetail.entrySet()) {
            if (!isFieldHandled(entry.getKey())) extraFields.put(entry.getKey(), entry.getValue());
        }
    }

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        if (serviceId != null) map.put("serviceId", serviceId);
        if (taskName != null) map.put("taskName", taskName);
        if (description != null) map.put("description", description);
        if (status != null) map.put("status", status);
        if (ID != null) map.put("ID", ID);
        if (recordStatus != null) map.put("recordStatus", recordStatus);
        if (recordInputter != null) map.put("recordInputter", recordInputter);
        if (recordAuthorizer != null) map.put("recordAuthorizer", recordAuthorizer);
        if (recordCount != null) map.put("recordCount", recordCount);
        if (dateTime != null) map.put("dateTime", dateTime);
        if (!JobDetailsList.isEmpty()) {
            List<Map<String, Object>> tmp = new ArrayList<>();
            for (JobDetails u : JobDetailsList) tmp.add(u.toMap());
            map.put("JobDetails", tmp);
        }
        map.putAll(extraFields);
        return map;
    }

    private boolean isFieldHandled(String key) {
        return switch (key) {
            case "serviceId", "taskName", "description", "status", "ID", "recordStatus", "recordInputter", "recordAuthorizer", "recordCount", "dateTime", "JobDetails" -> true;
            default -> false;
        };
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
        this.serviceIdVal.setValue(serviceId);
    }
    public String getServiceId() { return this.serviceId; }
    public FieldValue<String> serviceIdVal() { return serviceIdVal; }
    public void setTaskName(String taskName) {
        this.taskName = taskName;
        this.taskNameVal.setValue(taskName);
    }
    public String getTaskName() { return this.taskName; }
    public FieldValue<String> taskNameVal() { return taskNameVal; }
    public void setDescription(String description) {
        this.description = description;
        this.descriptionVal.setValue(description);
    }
    public String getDescription() { return this.description; }
    public FieldValue<String> descriptionVal() { return descriptionVal; }
    public void setStatus(String status) {
        this.status = status;
        this.statusVal.setValue(status);
    }
    public String getStatus() { return this.status; }
    public FieldValue<String> statusVal() { return statusVal; }
    public void setID(String ID) {
        this.ID = ID;
        this.IDVal.setValue(ID);
    }
    public String getID() { return this.ID; }
    public FieldValue<String> IDVal() { return IDVal; }
    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
        this.recordStatusVal.setValue(recordStatus);
    }
    public String getRecordStatus() { return this.recordStatus; }
    public FieldValue<String> recordStatusVal() { return recordStatusVal; }
    public void setRecordInputter(String recordInputter) {
        this.recordInputter = recordInputter;
        this.recordInputterVal.setValue(recordInputter);
    }
    public String getRecordInputter() { return this.recordInputter; }
    public FieldValue<String> recordInputterVal() { return recordInputterVal; }
    public void setRecordAuthorizer(List<String> recordAuthorizer) {
        this.recordAuthorizer = recordAuthorizer;
        this.recordAuthorizerVal.setValue(recordAuthorizer);
    }
    public List<String> getRecordAuthorizer() { return this.recordAuthorizer; }
    public FieldValue<List<String>> recordAuthorizerVal() { return recordAuthorizerVal; }
    public void setRecordCount(String recordCount) {
        this.recordCount = recordCount;
        this.recordCountVal.setValue(recordCount);
    }
    public String getRecordCount() { return this.recordCount; }
    public FieldValue<String> recordCountVal() { return recordCountVal; }
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
        this.dateTimeVal.setValue(dateTime);
    }
    public String getDateTime() { return this.dateTime; }
    public FieldValue<String> dateTimeVal() { return dateTimeVal; }
    public List<JobDetails> getJobDetailsList() { return JobDetailsList; }
}
